{"version": 3, "names": ["_index", "require", "BLOCK_SCOPED_SYMBOL", "Symbol", "for", "isVar", "node", "isVariableDeclaration", "kind"], "sources": ["../../src/validators/isVar.ts"], "sourcesContent": ["import { isVariableDeclaration } from \"./generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var BLOCK_SCOPED_SYMBOL = Symbol.for(\"var used to be block scoped\");\n}\n\n/**\n * Check if the input `node` is a variable declaration.\n */\nexport default function isVar(node: t.Node): boolean {\n  if (process.env.BABEL_8_BREAKING) {\n    return isVariableDeclaration(node) && node.kind === \"var\";\n  } else {\n    return (\n      isVariableDeclaration(node, { kind: \"var\" }) &&\n      !(\n        // @ts-expect-error document private properties\n        node[BLOCK_SCOPED_SYMBOL]\n      )\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAGmC;EAEjC,IAAIC,mBAAmB,GAAGC,MAAM,CAACC,GAAG,CAAC,6BAA6B,CAAC;AACrE;AAKe,SAASC,KAAKA,CAACC,IAAY,EAAW;EAG5C;IACL,OACE,IAAAC,4BAAqB,EAACD,IAAI,EAAE;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC,IAC5C,CAEEF,IAAI,CAACJ,mBAAmB,CACzB;EAEL;AACF", "ignoreList": []}