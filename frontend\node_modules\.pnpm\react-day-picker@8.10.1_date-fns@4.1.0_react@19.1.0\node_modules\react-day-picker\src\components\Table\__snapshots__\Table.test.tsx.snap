// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`should render correctly 1`] = `
<table
  class="rdp-table"
  role="grid"
>
  <thead
    class="rdp-head"
  >
    <tr
      class="rdp-head_row"
    >
      <th
        aria-label="Sunday"
        class="rdp-head_cell"
        scope="col"
      >
        Su
      </th>
      <th
        aria-label="Monday"
        class="rdp-head_cell"
        scope="col"
      >
        Mo
      </th>
      <th
        aria-label="Tuesday"
        class="rdp-head_cell"
        scope="col"
      >
        Tu
      </th>
      <th
        aria-label="Wednesday"
        class="rdp-head_cell"
        scope="col"
      >
        We
      </th>
      <th
        aria-label="Thursday"
        class="rdp-head_cell"
        scope="col"
      >
        Th
      </th>
      <th
        aria-label="Friday"
        class="rdp-head_cell"
        scope="col"
      >
        Fr
      </th>
      <th
        aria-label="Saturday"
        class="rdp-head_cell"
        scope="col"
      >
        Sa
      </th>
    </tr>
  </thead>
  <tbody
    class="rdp-tbody"
  >
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          1
        </div>
      </td>
    </tr>
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          2
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          3
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          4
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          5
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          6
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          7
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          8
        </div>
      </td>
    </tr>
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          9
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          10
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          11
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          12
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          13
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          14
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          15
        </div>
      </td>
    </tr>
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          16
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          17
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          18
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          19
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          20
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          21
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          22
        </div>
      </td>
    </tr>
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          23
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          24
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          25
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          26
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          27
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          28
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          29
        </div>
      </td>
    </tr>
  </tbody>
</table>
`;

exports[`when showing the week numbers should render correctly 1`] = `
<table
  class="rdp-table"
  role="grid"
>
  <thead
    class="rdp-head"
  >
    <tr
      class="rdp-head_row"
    >
      <td
        class="rdp-head_cell"
      />
      <th
        aria-label="Sunday"
        class="rdp-head_cell"
        scope="col"
      >
        Su
      </th>
      <th
        aria-label="Monday"
        class="rdp-head_cell"
        scope="col"
      >
        Mo
      </th>
      <th
        aria-label="Tuesday"
        class="rdp-head_cell"
        scope="col"
      >
        Tu
      </th>
      <th
        aria-label="Wednesday"
        class="rdp-head_cell"
        scope="col"
      >
        We
      </th>
      <th
        aria-label="Thursday"
        class="rdp-head_cell"
        scope="col"
      >
        Th
      </th>
      <th
        aria-label="Friday"
        class="rdp-head_cell"
        scope="col"
      >
        Fr
      </th>
      <th
        aria-label="Saturday"
        class="rdp-head_cell"
        scope="col"
      >
        Sa
      </th>
    </tr>
  </thead>
  <tbody
    class="rdp-tbody"
  >
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
      >
        <span
          class="rdp-weeknumber"
        >
          5
        </span>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          role="gridcell"
        />
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          1
        </div>
      </td>
    </tr>
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
      >
        <span
          class="rdp-weeknumber"
        >
          6
        </span>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          2
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          3
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          4
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          5
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          6
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          7
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          8
        </div>
      </td>
    </tr>
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
      >
        <span
          class="rdp-weeknumber"
        >
          7
        </span>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          9
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          10
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          11
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          12
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          13
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          14
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          15
        </div>
      </td>
    </tr>
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
      >
        <span
          class="rdp-weeknumber"
        >
          8
        </span>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          16
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          17
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          18
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          19
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          20
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          21
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          22
        </div>
      </td>
    </tr>
    <tr
      class="rdp-row"
    >
      <td
        class="rdp-cell"
      >
        <span
          class="rdp-weeknumber"
        >
          9
        </span>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          23
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          24
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          25
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          26
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          27
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          28
        </div>
      </td>
      <td
        class="rdp-cell"
        role="presentation"
      >
        <div
          class="rdp-day"
          role="gridcell"
        >
          29
        </div>
      </td>
    </tr>
  </tbody>
</table>
`;

exports[`when using custom components should render correctly 1`] = `
<table
  class="rdp-table"
  role="grid"
>
  <thead>
    <tr>
      <td>
        CustomHead
      </td>
    </tr>
  </thead>
  <tbody
    class="rdp-tbody"
  >
    <tr>
      <td>
        CustomRow
      </td>
    </tr>
    <tr>
      <td>
        CustomRow
      </td>
    </tr>
    <tr>
      <td>
        CustomRow
      </td>
    </tr>
    <tr>
      <td>
        CustomRow
      </td>
    </tr>
    <tr>
      <td>
        CustomRow
      </td>
    </tr>
  </tbody>
  <tfoot>
    <tr>
      <td>
        Sat Feb 01 2020
      </td>
    </tr>
  </tfoot>
</table>
`;
