from flask import Blueprint, jsonify, request, current_app
from src.models.user import User
from src.models.inventory import db

user_bp = Blueprint('user', __name__)

@user_bp.route('/users', methods=['GET'])
def get_users():
    """获取用户列表"""
    try:
        with current_app.app_context():
            users = User.query.all()
            result = []
            for user in users:
                result.append({
                    'id': user.id,
                    'username': user.username,
                    'email': user.email
                })
            return jsonify({'code': 200, 'message': '获取用户列表成功', 'success': True, 'data': result})
    except Exception as e:
        return jsonify({'code': 500, 'message': f'获取用户列表失败：{str(e)}', 'success': False}), 500

@user_bp.route('/users', methods=['POST'])
def create_user():
    """创建用户"""
    try:
        with current_app.app_context():
            data = request.json

            # 验证必填字段
            if not data or not data.get('username'):
                return jsonify({'code': 400, 'message': '用户名不能为空', 'success': False}), 400
            if not data.get('email'):
                return jsonify({'code': 400, 'message': '邮箱不能为空', 'success': False}), 400

            # 检查用户名是否已存在
            existing_user = User.query.filter_by(username=data.get('username')).first()
            if existing_user:
                return jsonify({'code': 400, 'message': '用户名已存在，请使用其他用户名', 'success': False}), 400

            # 检查邮箱是否已存在
            existing_email = User.query.filter_by(email=data.get('email')).first()
            if existing_email:
                return jsonify({'code': 400, 'message': '邮箱已存在，请使用其他邮箱', 'success': False}), 400

            # 创建用户
            user = User(
                username=data.get('username'),
                email=data.get('email')
            )

            db.session.add(user)
            db.session.commit()

            result = {
                'id': user.id,
                'username': user.username,
                'email': user.email
            }

            return jsonify({'code': 200, 'message': '用户创建成功', 'success': True, 'data': result})
    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'message': f'创建用户失败：{str(e)}', 'success': False}), 500

@user_bp.route('/users/<int:user_id>', methods=['GET'])
def get_user(user_id):
    """获取单个用户"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'code': 404, 'message': '用户不存在', 'success': False}), 404
        return jsonify({'code': 200, 'message': '获取用户信息成功', 'success': True, 'data': user.to_dict()})
    except Exception as e:
        return jsonify({'code': 500, 'message': f'获取用户信息失败：{str(e)}', 'success': False}), 500

@user_bp.route('/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    """更新用户"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'code': 404, 'message': '用户不存在', 'success': False}), 404

        data = request.json
        if not data:
            return jsonify({'code': 400, 'message': '请提供要更新的数据', 'success': False}), 400

        # 更新用户信息
        user.username = data.get('username', user.username)
        user.email = data.get('email', user.email)
        db.session.commit()

        return jsonify({'code': 200, 'message': '用户更新成功', 'success': True, 'data': user.to_dict()})
    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'message': f'更新用户失败：{str(e)}', 'success': False}), 500

@user_bp.route('/users/<int:user_id>', methods=['DELETE'])
def delete_user(user_id):
    """删除用户"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'code': 404, 'message': '用户不存在', 'success': False}), 404

        db.session.delete(user)
        db.session.commit()

        return jsonify({'code': 200, 'message': '用户删除成功', 'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'message': f'删除用户失败：{str(e)}', 'success': False}), 500
