{"compilerOptions": {"target": "es5", "jsx": "react-jsx", "module": "es6", "moduleResolution": "node", "rootDir": "./", "baseUrl": "./src", "noEmit": true, "paths": {"test/*": ["../test/*"]}, "declaration": true, "sourceMap": true, "outDir": "./dist", "importHelpers": true, "esModuleInterop": false, "allowSyntheticDefaultImports": false, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": false, "types": ["jest", "node"]}, "include": ["test", "src"]}