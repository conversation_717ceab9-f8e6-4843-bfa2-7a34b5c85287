export declare const eachYearOfIntervalWithOptions: import("./types.js").FPFn2<
  import("../eachYearOfInterval.js").EachYearOfIntervalResult<
    import("../fp.js").Interval<
      import("../fp.js").DateArg<Date>,
      import("../fp.js").DateArg<Date>
    >,
    | import("../eachYearOfInterval.js").EachYearOfIntervalOptions<Date>
    | undefined
  >,
  | import("../eachYearOfInterval.js").EachYearOfIntervalOptions<Date>
  | undefined,
  import("../fp.js").Interval<
    import("../fp.js").DateArg<Date>,
    import("../fp.js").DateArg<Date>
  >
>;
