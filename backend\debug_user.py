#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试用户API问题
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

try:
    print("🔍 开始调试用户API问题...")
    
    # 测试导入
    print("📦 测试导入模块...")
    from src.models.inventory import db
    print("✅ 导入 inventory db 成功")
    
    from src.models.user import User
    print("✅ 导入 User 模型成功")
    
    from src.utils.response import APIResponse, BusinessMessages
    print("✅ 导入响应工具成功")
    
    # 测试Flask应用
    print("🌐 测试Flask应用...")
    from src.main import app
    print("✅ 导入Flask应用成功")
    
    # 测试数据库连接
    print("🗄️ 测试数据库连接...")
    with app.app_context():
        # 检查表是否存在
        from sqlalchemy import inspect
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        print(f"📋 数据库中的表: {tables}")
        
        if 'user' in tables:
            print("✅ user表存在")
        else:
            print("❌ user表不存在，正在创建...")
            db.create_all()
            print("✅ 数据库表创建完成")
        
        # 测试创建用户
        print("👤 测试创建用户...")
        test_user = User(username="debug_user", email="<EMAIL>")
        db.session.add(test_user)
        db.session.commit()
        print("✅ 用户创建成功")
        
        # 查询用户
        users = User.query.all()
        print(f"📊 当前用户数量: {len(users)}")
        for user in users:
            print(f"   - {user.username} ({user.email})")
    
    print("🎉 所有测试通过！")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
