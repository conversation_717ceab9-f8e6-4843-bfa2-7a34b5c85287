#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试服务器
"""

from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/')
def index():
    return jsonify({'message': '后端服务正常运行', 'status': 'ok'})

@app.route('/api/test')
def test():
    return jsonify({'message': 'API测试成功', 'status': 'ok'})

if __name__ == '__main__':
    print("🚀 启动简单测试服务器...")
    print("📡 地址: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
