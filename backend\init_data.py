#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化测试数据脚本
"""

import requests
import json

API_BASE_URL = 'http://localhost:5000/api'

def create_data(endpoint, data):
    """创建数据的通用函数"""
    try:
        response = requests.post(f"{API_BASE_URL}/{endpoint}", 
                               headers={'Content-Type': 'application/json'},
                               data=json.dumps(data))
        result = response.json()
        if result.get('code') == 200:
            print(f"✓ 创建{endpoint}成功: {data.get('product_name', data.get('warehouse_name', data.get('supplier_name', data.get('customer_name', 'Unknown'))))}")
            return result
        else:
            print(f"✗ 创建{endpoint}失败: {result.get('message', 'Unknown error')}")
            return None
    except Exception as e:
        print(f"✗ 创建{endpoint}时发生错误: {str(e)}")
        return None

def init_products():
    """初始化商品数据"""
    print("\n=== 初始化商品数据 ===")
    products = [
        {
            "product_name": "华为Mate60 Pro",
            "sku": "HW-MATE60-PRO",
            "category": "智能手机",
            "brand": "华为",
            "unit": "台",
            "purchase_price": 5999.00,
            "sales_price": 6999.00
        },
        {
            "product_name": "小米14 Ultra",
            "sku": "MI-14-ULTRA",
            "category": "智能手机",
            "brand": "小米",
            "unit": "台",
            "purchase_price": 4999.00,
            "sales_price": 5999.00
        },
        {
            "product_name": "MacBook Pro 14寸",
            "sku": "APPLE-MBP-14",
            "category": "笔记本电脑",
            "brand": "苹果",
            "unit": "台",
            "purchase_price": 12999.00,
            "sales_price": 15999.00
        },
        {
            "product_name": "iPad Air",
            "sku": "APPLE-IPAD-AIR",
            "category": "平板电脑",
            "brand": "苹果",
            "unit": "台",
            "purchase_price": 3999.00,
            "sales_price": 4999.00
        },
        {
            "product_name": "AirPods Pro",
            "sku": "APPLE-AIRPODS-PRO",
            "category": "耳机",
            "brand": "苹果",
            "unit": "副",
            "purchase_price": 1599.00,
            "sales_price": 1999.00
        }
    ]
    
    for product in products:
        create_data('products', product)

def init_warehouses():
    """初始化仓库数据"""
    print("\n=== 初始化仓库数据 ===")
    warehouses = [
        {
            "warehouse_name": "北京总仓",
            "location": "北京市朝阳区",
            "capacity": 10000.00
        },
        {
            "warehouse_name": "上海分仓",
            "location": "上海市浦东新区",
            "capacity": 8000.00
        },
        {
            "warehouse_name": "深圳分仓",
            "location": "深圳市南山区",
            "capacity": 6000.00
        }
    ]
    
    for warehouse in warehouses:
        create_data('warehouses', warehouse)

def init_suppliers():
    """初始化供应商数据"""
    print("\n=== 初始化供应商数据 ===")
    suppliers = [
        {
            "supplier_name": "苹果公司",
            "contact_person": "王经理",
            "contact_phone": "13800138003",
            "address": "美国加利福尼亚州库比蒂诺"
        }
    ]
    
    for supplier in suppliers:
        create_data('suppliers', supplier)

def init_customers():
    """初始化客户数据"""
    print("\n=== 初始化客户数据 ===")
    customers = [
        {
            "customer_name": "拼多多",
            "contact_phone": "400-8822-528",
            "shipping_address": "上海市长宁区"
        },
        {
            "customer_name": "苏宁易购",
            "contact_phone": "400-365-365",
            "shipping_address": "南京市雨花台区"
        }
    ]
    
    for customer in customers:
        create_data('customers', customer)

def main():
    """主函数"""
    print("开始初始化测试数据...")
    
    init_products()
    init_warehouses()
    init_suppliers()
    init_customers()
    
    print("\n=== 数据初始化完成 ===")
    print("现在可以在前端界面中查看和管理这些数据了！")

if __name__ == "__main__":
    main()
