"use strict";
exports.da = void 0;
var _index = require("./da/_lib/formatDistance.cjs");
var _index2 = require("./da/_lib/formatLong.cjs");
var _index3 = require("./da/_lib/formatRelative.cjs");
var _index4 = require("./da/_lib/localize.cjs");
var _index5 = require("./da/_lib/match.cjs");

/**
 * @category Locales
 * @summary Danish locale.
 * @language Danish
 * @iso-639-2 dan
 * <AUTHOR> [@MathiasKandelborg](https://github.com/MathiasKandelborg)
 * <AUTHOR> [@Andersbiha](https://github.com/Andersbiha)
 * <AUTHOR>
 * <AUTHOR>
 */
const da = (exports.da = {
  code: "da",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
