!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n="undefined"!=typeof globalThis?globalThis:n||self).MotionUtils={})}(this,(function(n){"use strict";function t(n,t){-1===n.indexOf(t)&&n.push(t)}function e(n,t){const e=n.indexOf(t);e>-1&&n.splice(e,1)}const i=(n,t,e)=>e>t?t:e<n?n:e;let s=()=>{};const o=n=>n,r=(n,t)=>e=>t(n(e));const c=new Set;const u=(n,t,e)=>{const i=t-n;return((e-n)%i+i)%i+n},a=(n,t,e)=>(((1-3*e+3*t)*n+(3*e-6*t))*n+3*t)*n;function l(n,t,e,i){if(n===t&&e===i)return o;const s=t=>function(n,t,e,i,s){let o,r,c=0;do{r=t+(e-t)/2,o=a(r,i,s)-n,o>0?e=r:t=r}while(Math.abs(o)>1e-7&&++c<12);return r}(t,0,1,n,e);return n=>0===n||1===n?n:a(s(n),t,i)}const f=n=>t=>t<=.5?n(2*t)/2:(2-n(2*(1-t)))/2,p=n=>t=>1-n(1-t),d=l(.33,1.53,.69,.99),h=p(d),b=f(h),g=n=>(n*=2)<1?.5*h(n):.5*(2-Math.pow(2,-10*(n-1))),m=n=>1-Math.sin(Math.acos(n)),O=p(m),y=f(m),I=l(.42,0,1,1),M=l(0,0,.58,1),w=l(.42,0,.58,1);const S=n=>Array.isArray(n)&&"number"!=typeof n[0];const k=n=>Array.isArray(n)&&"number"==typeof n[0],v={linear:o,easeIn:I,easeInOut:w,easeOut:M,circIn:m,circInOut:y,circOut:O,backIn:h,backInOut:b,backOut:d,anticipate:g};n.MotionGlobalConfig={},n.SubscriptionManager=class{constructor(){this.subscriptions=[]}add(n){return t(this.subscriptions,n),()=>e(this.subscriptions,n)}notify(n,t,e){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](n,t,e);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(n,t,e)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}},n.addUniqueItem=t,n.anticipate=g,n.backIn=h,n.backInOut=b,n.backOut=d,n.circIn=m,n.circInOut=y,n.circOut=O,n.clamp=i,n.cubicBezier=l,n.easeIn=I,n.easeInOut=w,n.easeOut=M,n.easingDefinitionToFunction=n=>{if(k(n)){n.length;const[t,e,i,s]=n;return l(t,e,i,s)}return"string"==typeof n?v[n]:n},n.getEasingForSegment=function(n,t){return S(n)?n[u(0,n.length,t)]:n},n.hasWarned=function(n){return c.has(n)},n.invariant=s,n.isBezierDefinition=k,n.isEasingArray=S,n.isNumericalString=n=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(n),n.isObject=function(n){return"object"==typeof n&&null!==n},n.isZeroValueString=n=>/^0[^.\s]+$/u.test(n),n.memo=function(n){let t;return()=>(void 0===t&&(t=n()),t)},n.millisecondsToSeconds=n=>n/1e3,n.mirrorEasing=f,n.moveItem=function([...n],t,e){const i=t<0?n.length+t:t;if(i>=0&&i<n.length){const i=e<0?n.length+e:e,[s]=n.splice(t,1);n.splice(i,0,s)}return n},n.noop=o,n.pipe=(...n)=>n.reduce(r),n.progress=(n,t,e)=>{const i=t-n;return 0===i?1:(e-n)/i},n.removeItem=e,n.reverseEasing=p,n.secondsToMilliseconds=n=>1e3*n,n.steps=function(n,t="end"){return e=>{const s=(e="end"===t?Math.min(e,.999):Math.max(e,.001))*n,o="end"===t?Math.floor(s):Math.ceil(s);return i(0,1,o/n)}},n.velocityPerSecond=function(n,t){return t?n*(1e3/t):0},n.warnOnce=function(n,t,e){n||c.has(t)||(console.warn(t),e&&console.warn(e),c.add(t))},n.warning=()=>{},n.wrap=u}));
