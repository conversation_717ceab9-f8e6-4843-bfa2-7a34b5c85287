// https://d3js.org/d3-format/ v3.1.0 Copyright 2010-2021 <PERSON>
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?i(exports):"function"==typeof define&&define.amd?define(["exports"],i):i((t="undefined"!=typeof globalThis?globalThis:t||self).d3=t.d3||{})}(this,(function(t){"use strict";function i(t,i){if((r=(t=i?t.toExponential(i-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function r(t){return(t=i(Math.abs(t)))?t[1]:NaN}var n,e=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function o(t){if(!(i=e.exec(t)))throw new Error("invalid format: "+t);var i;return new a({fill:i[1],align:i[2],sign:i[3],symbol:i[4],zero:i[5],width:i[6],comma:i[7],precision:i[8]&&i[8].slice(1),trim:i[9],type:i[10]})}function a(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function s(t,r){var n=i(t,r);if(!n)return t+"";var e=n[0],o=n[1];return o<0?"0."+new Array(-o).join("0")+e:e.length>o+1?e.slice(0,o+1)+"."+e.slice(o+1):e+new Array(o-e.length+2).join("0")}o.prototype=a.prototype,a.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var c={"%":(t,i)=>(100*t).toFixed(i),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,i)=>t.toExponential(i),f:(t,i)=>t.toFixed(i),g:(t,i)=>t.toPrecision(i),o:t=>Math.round(t).toString(8),p:(t,i)=>s(100*t,i),r:s,s:function(t,r){var e=i(t,r);if(!e)return t+"";var o=e[0],a=e[1],s=a-(n=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,c=o.length;return s===c?o:s>c?o+new Array(s-c+1).join("0"):s>0?o.slice(0,s)+"."+o.slice(s):"0."+new Array(1-s).join("0")+i(t,Math.max(0,r+s-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function h(t){return t}var l,u=Array.prototype.map,f=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function d(t){var i,e,a=void 0===t.grouping||void 0===t.thousands?h:(i=u.call(t.grouping,Number),e=t.thousands+"",function(t,r){for(var n=t.length,o=[],a=0,s=i[0],c=0;n>0&&s>0&&(c+s+1>r&&(s=Math.max(1,r-c)),o.push(t.substring(n-=s,n+s)),!((c+=s+1)>r));)s=i[a=(a+1)%i.length];return o.reverse().join(e)}),s=void 0===t.currency?"":t.currency[0]+"",l=void 0===t.currency?"":t.currency[1]+"",d=void 0===t.decimal?".":t.decimal+"",m=void 0===t.numerals?h:function(t){return function(i){return i.replace(/[0-9]/g,(function(i){return t[+i]}))}}(u.call(t.numerals,String)),p=void 0===t.percent?"%":t.percent+"",g=void 0===t.minus?"−":t.minus+"",v=void 0===t.nan?"NaN":t.nan+"";function M(t){var i=(t=o(t)).fill,r=t.align,e=t.sign,h=t.symbol,u=t.zero,M=t.width,y=t.comma,x=t.precision,b=t.trim,w=t.type;"n"===w?(y=!0,w="g"):c[w]||(void 0===x&&(x=12),b=!0,w="g"),(u||"0"===i&&"="===r)&&(u=!0,i="0",r="=");var S="$"===h?s:"#"===h&&/[boxX]/.test(w)?"0"+w.toLowerCase():"",j="$"===h?l:/[%p]/.test(w)?p:"",k=c[w],P=/[defgprs%]/.test(w);function z(t){var o,s,c,h=S,l=j;if("c"===w)l=k(t)+l,t="";else{var p=(t=+t)<0||1/t<0;if(t=isNaN(t)?v:k(Math.abs(t),x),b&&(t=function(t){t:for(var i,r=t.length,n=1,e=-1;n<r;++n)switch(t[n]){case".":e=i=n;break;case"0":0===e&&(e=n),i=n;break;default:if(!+t[n])break t;e>0&&(e=0)}return e>0?t.slice(0,e)+t.slice(i+1):t}(t)),p&&0==+t&&"+"!==e&&(p=!1),h=(p?"("===e?e:g:"-"===e||"("===e?"":e)+h,l=("s"===w?f[8+n/3]:"")+l+(p&&"("===e?")":""),P)for(o=-1,s=t.length;++o<s;)if(48>(c=t.charCodeAt(o))||c>57){l=(46===c?d+t.slice(o+1):t.slice(o))+l,t=t.slice(0,o);break}}y&&!u&&(t=a(t,1/0));var z=h.length+t.length+l.length,A=z<M?new Array(M-z+1).join(i):"";switch(y&&u&&(t=a(A+t,A.length?M-l.length:1/0),A=""),r){case"<":t=h+t+l+A;break;case"=":t=h+A+t+l;break;case"^":t=A.slice(0,z=A.length>>1)+h+t+l+A.slice(z);break;default:t=A+h+t+l}return m(t)}return x=void 0===x?6:/[gprs]/.test(w)?Math.max(1,Math.min(21,x)):Math.max(0,Math.min(20,x)),z.toString=function(){return t+""},z}return{format:M,formatPrefix:function(t,i){var n=M(((t=o(t)).type="f",t)),e=3*Math.max(-8,Math.min(8,Math.floor(r(i)/3))),a=Math.pow(10,-e),s=f[8+e/3];return function(t){return n(a*t)+s}}}}function m(i){return l=d(i),t.format=l.format,t.formatPrefix=l.formatPrefix,l}t.format=void 0,t.formatPrefix=void 0,m({thousands:",",grouping:[3],currency:["$",""]}),t.FormatSpecifier=a,t.formatDefaultLocale=m,t.formatLocale=d,t.formatSpecifier=o,t.precisionFixed=function(t){return Math.max(0,-r(Math.abs(t)))},t.precisionPrefix=function(t,i){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(r(i)/3)))-r(Math.abs(t)))},t.precisionRound=function(t,i){return t=Math.abs(t),i=Math.abs(i)-t,Math.max(0,r(i)-r(t))+1},Object.defineProperty(t,"__esModule",{value:!0})}));
