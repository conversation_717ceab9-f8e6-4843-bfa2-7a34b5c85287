"use strict";
exports.eo = void 0;
var _index = require("./eo/_lib/formatDistance.cjs");
var _index2 = require("./eo/_lib/formatLong.cjs");
var _index3 = require("./eo/_lib/formatRelative.cjs");
var _index4 = require("./eo/_lib/localize.cjs");
var _index5 = require("./eo/_lib/match.cjs");

/**
 * @category Locales
 * @summary Esperanto locale.
 * @language Esperanto
 * @iso-639-2 epo
 * <AUTHOR>
 */
const eo = (exports.eo = {
  code: "eo",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
