import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom'
import { Button } from '@/components/ui/button.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Label } from '@/components/ui/label.jsx'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table.jsx'
import { Badge } from '@/components/ui/badge.jsx'
import { Alert, AlertDescription } from '@/components/ui/alert.jsx'
import { Package, Warehouse, TrendingUp, AlertTriangle, Plus, Search, Eye } from 'lucide-react'
import './App.css'

const API_BASE_URL = 'http://localhost:5000/api'

// 导航组件
function Navigation() {
  const location = useLocation()
  
  const navItems = [
    { path: '/', label: '仪表板', icon: TrendingUp },
    { path: '/products', label: '商品管理', icon: Package },
    { path: '/warehouses', label: '仓库管理', icon: Warehouse },
    { path: '/inventory', label: '库存查询', icon: Search },
    { path: '/inbound', label: '入库管理', icon: Plus },
    { path: '/outbound', label: '出库管理', icon: Eye },
    { path: '/warnings', label: '库存预警', icon: AlertTriangle }
  ]

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Package className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">库存管理系统</span>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                      location.pathname === item.path
                        ? 'border-blue-500 text-gray-900'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-1" />
                    {item.label}
                  </Link>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}

// 仪表板组件
function Dashboard() {
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalWarehouses: 0,
    lowStockItems: 0,
    totalInventoryValue: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    setLoading(true)
    setError(null)

    try {
      // 并行获取所有数据
      const [productsRes, warehousesRes, inventoryRes, warningsRes] = await Promise.all([
        fetch(`${API_BASE_URL}/products`),
        fetch(`${API_BASE_URL}/warehouses`),
        fetch(`${API_BASE_URL}/inventory`),
        fetch(`${API_BASE_URL}/inventory/warnings`)
      ])

      const [productsData, warehousesData, inventoryData, warningsData] = await Promise.all([
        productsRes.json(),
        warehousesRes.json(),
        inventoryRes.json(),
        warningsRes.json()
      ])

      // 计算统计数据
      const totalProducts = productsData.code === 200 ? productsData.data.length : 0
      const totalWarehouses = warehousesData.code === 200 ? warehousesData.data.length : 0
      const lowStockItems = warningsData.code === 200 ? warningsData.data.length : 0

      // 计算库存总价值
      let totalInventoryValue = 0
      if (inventoryData.code === 200 && productsData.code === 200) {
        const products = productsData.data
        const inventory = inventoryData.data

        totalInventoryValue = inventory.reduce((total, item) => {
          const product = products.find(p => p.product_name === item.product_name)
          const price = product ? parseFloat(product.purchase_price || 0) : 0
          return total + (item.quantity * price)
        }, 0)
      }

      setStats({
        totalProducts,
        totalWarehouses,
        lowStockItems,
        totalInventoryValue
      })
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
      setError('获取数据失败，请检查后端服务是否正常运行')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-3 w-24 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={fetchDashboardData}
            >
              重新加载
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
        <Button variant="outline" onClick={fetchDashboardData}>
          <TrendingUp className="h-4 w-4 mr-2" />
          刷新数据
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">商品总数</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalProducts > 0 ? '商品种类丰富' : '暂无商品数据'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">仓库数量</CardTitle>
            <Warehouse className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalWarehouses}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalWarehouses > 0 ? '仓储网络完善' : '暂无仓库数据'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">低库存商品</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${stats.lowStockItems > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {stats.lowStockItems}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.lowStockItems > 0 ? '需要补货' : '库存充足'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">库存总价值</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{stats.totalInventoryValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalInventoryValue > 0 ? '资产价值统计' : '暂无库存数据'}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// 商品管理组件
function ProductManagement() {
  const [products, setProducts] = useState([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [newProduct, setNewProduct] = useState({
    product_name: '',
    sku: '',
    category: '',
    brand: '',
    unit: '',
    purchase_price: '',
    sales_price: ''
  })

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/products`)
      const data = await response.json()
      if (data.code === 200) {
        setProducts(data.data)
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
    }
  }

  const handleAddProduct = async (e) => {
    e.preventDefault()
    try {
      const response = await fetch(`${API_BASE_URL}/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newProduct)
      })
      const data = await response.json()
      if (data.code === 200) {
        setShowAddForm(false)
        setNewProduct({
          product_name: '',
          sku: '',
          category: '',
          brand: '',
          unit: '',
          purchase_price: '',
          sales_price: ''
        })
        fetchProducts()
      } else {
        alert(data.message)
      }
    } catch (error) {
      console.error('添加商品失败:', error)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">商品管理</h1>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          添加商品
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>添加新商品</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddProduct} className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="product_name">商品名称</Label>
                <Input
                  id="product_name"
                  value={newProduct.product_name}
                  onChange={(e) => setNewProduct({...newProduct, product_name: e.target.value})}
                  required
                />
              </div>
              <div>
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  value={newProduct.sku}
                  onChange={(e) => setNewProduct({...newProduct, sku: e.target.value})}
                  required
                />
              </div>
              <div>
                <Label htmlFor="category">分类</Label>
                <Input
                  id="category"
                  value={newProduct.category}
                  onChange={(e) => setNewProduct({...newProduct, category: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="brand">品牌</Label>
                <Input
                  id="brand"
                  value={newProduct.brand}
                  onChange={(e) => setNewProduct({...newProduct, brand: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="unit">单位</Label>
                <Input
                  id="unit"
                  value={newProduct.unit}
                  onChange={(e) => setNewProduct({...newProduct, unit: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="purchase_price">采购价格</Label>
                <Input
                  id="purchase_price"
                  type="number"
                  step="0.01"
                  value={newProduct.purchase_price}
                  onChange={(e) => setNewProduct({...newProduct, purchase_price: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="sales_price">销售价格</Label>
                <Input
                  id="sales_price"
                  type="number"
                  step="0.01"
                  value={newProduct.sales_price}
                  onChange={(e) => setNewProduct({...newProduct, sales_price: e.target.value})}
                />
              </div>
              <div className="col-span-2 flex gap-2">
                <Button type="submit">保存</Button>
                <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                  取消
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>商品列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>商品名称</TableHead>
                <TableHead>SKU</TableHead>
                <TableHead>分类</TableHead>
                <TableHead>品牌</TableHead>
                <TableHead>单位</TableHead>
                <TableHead>采购价格</TableHead>
                <TableHead>销售价格</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {products.map((product) => (
                <TableRow key={product.product_id}>
                  <TableCell>{product.product_name}</TableCell>
                  <TableCell>{product.sku}</TableCell>
                  <TableCell>{product.category}</TableCell>
                  <TableCell>{product.brand}</TableCell>
                  <TableCell>{product.unit}</TableCell>
                  <TableCell>¥{product.purchase_price}</TableCell>
                  <TableCell>¥{product.sales_price}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

// 仓库管理组件
function WarehouseManagement() {
  const [warehouses, setWarehouses] = useState([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [newWarehouse, setNewWarehouse] = useState({
    warehouse_name: '',
    location: '',
    capacity: ''
  })

  useEffect(() => {
    fetchWarehouses()
  }, [])

  const fetchWarehouses = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/warehouses`)
      const data = await response.json()
      if (data.code === 200) {
        setWarehouses(data.data)
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error)
    }
  }

  const handleAddWarehouse = async (e) => {
    e.preventDefault()
    try {
      const response = await fetch(`${API_BASE_URL}/warehouses`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newWarehouse)
      })
      const data = await response.json()
      if (data.code === 200) {
        setShowAddForm(false)
        setNewWarehouse({
          warehouse_name: '',
          location: '',
          capacity: ''
        })
        fetchWarehouses()
      } else {
        alert(data.message)
      }
    } catch (error) {
      console.error('添加仓库失败:', error)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">仓库管理</h1>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          添加仓库
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>添加新仓库</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddWarehouse} className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="warehouse_name">仓库名称</Label>
                <Input
                  id="warehouse_name"
                  value={newWarehouse.warehouse_name}
                  onChange={(e) => setNewWarehouse({...newWarehouse, warehouse_name: e.target.value})}
                  required
                />
              </div>
              <div>
                <Label htmlFor="location">位置</Label>
                <Input
                  id="location"
                  value={newWarehouse.location}
                  onChange={(e) => setNewWarehouse({...newWarehouse, location: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="capacity">容量 (立方米)</Label>
                <Input
                  id="capacity"
                  type="number"
                  step="0.01"
                  value={newWarehouse.capacity}
                  onChange={(e) => setNewWarehouse({...newWarehouse, capacity: e.target.value})}
                />
              </div>
              <div className="col-span-2 flex gap-2">
                <Button type="submit">保存</Button>
                <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                  取消
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>仓库列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>仓库名称</TableHead>
                <TableHead>位置</TableHead>
                <TableHead>容量 (立方米)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {warehouses.map((warehouse) => (
                <TableRow key={warehouse.warehouse_id}>
                  <TableCell>{warehouse.warehouse_name}</TableCell>
                  <TableCell>{warehouse.location}</TableCell>
                  <TableCell>{warehouse.capacity}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

// 入库管理组件
function InboundManagement() {
  const [inboundOrders, setInboundOrders] = useState([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [products, setProducts] = useState([])
  const [warehouses, setWarehouses] = useState([])
  const [suppliers, setSuppliers] = useState([])
  const [newOrder, setNewOrder] = useState({
    supplier_id: '',
    warehouse_id: '',
    order_type: 'purchase',
    items: []
  })
  const [newItem, setNewItem] = useState({
    product_id: '',
    quantity: '',
    unit_price: ''
  })

  useEffect(() => {
    fetchInboundOrders()
    fetchProducts()
    fetchWarehouses()
    fetchSuppliers()
  }, [])

  const fetchInboundOrders = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/inbound`)
      const data = await response.json()
      if (data.code === 200) {
        setInboundOrders(data.data)
      }
    } catch (error) {
      console.error('获取入库单列表失败:', error)
    }
  }

  const fetchProducts = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/products`)
      const data = await response.json()
      if (data.code === 200) {
        setProducts(data.data)
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
    }
  }

  const fetchWarehouses = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/warehouses`)
      const data = await response.json()
      if (data.code === 200) {
        setWarehouses(data.data)
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error)
    }
  }

  const fetchSuppliers = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/suppliers`)
      const data = await response.json()
      if (data.code === 200) {
        setSuppliers(data.data)
      }
    } catch (error) {
      console.error('获取供应商列表失败:', error)
    }
  }

  const addItemToOrder = () => {
    if (newItem.product_id && newItem.quantity && newItem.unit_price) {
      const product = products.find(p => p.product_id === parseInt(newItem.product_id))
      setNewOrder({
        ...newOrder,
        items: [...newOrder.items, {
          ...newItem,
          product_name: product?.product_name || '',
          sku: product?.sku || ''
        }]
      })
      setNewItem({
        product_id: '',
        quantity: '',
        unit_price: ''
      })
    }
  }

  const removeItemFromOrder = (index) => {
    const updatedItems = newOrder.items.filter((_, i) => i !== index)
    setNewOrder({...newOrder, items: updatedItems})
  }

  const handleCreateOrder = async (e) => {
    e.preventDefault()
    if (newOrder.items.length === 0) {
      alert('请至少添加一个商品')
      return
    }
    
    try {
      const response = await fetch(`${API_BASE_URL}/inbound`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newOrder)
      })
      const data = await response.json()
      if (data.code === 200) {
        setShowAddForm(false)
        setNewOrder({
          supplier_id: '',
          warehouse_id: '',
          order_type: 'purchase',
          items: []
        })
        fetchInboundOrders()
      } else {
        alert(data.message)
      }
    } catch (error) {
      console.error('创建入库单失败:', error)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">入库管理</h1>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          创建入库单
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>创建入库单</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCreateOrder} className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="order_type">入库类型</Label>
                  <select
                    id="order_type"
                    className="w-full p-2 border rounded"
                    value={newOrder.order_type}
                    onChange={(e) => setNewOrder({...newOrder, order_type: e.target.value})}
                  >
                    <option value="purchase">采购入库</option>
                    <option value="return">退货入库</option>
                    <option value="transfer">调拨入库</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="supplier_id">供应商</Label>
                  <select
                    id="supplier_id"
                    className="w-full p-2 border rounded"
                    value={newOrder.supplier_id}
                    onChange={(e) => setNewOrder({...newOrder, supplier_id: e.target.value})}
                    required
                  >
                    <option value="">请选择供应商</option>
                    {suppliers.map(supplier => (
                      <option key={supplier.supplier_id} value={supplier.supplier_id}>
                        {supplier.supplier_name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <Label htmlFor="warehouse_id">目标仓库</Label>
                  <select
                    id="warehouse_id"
                    className="w-full p-2 border rounded"
                    value={newOrder.warehouse_id}
                    onChange={(e) => setNewOrder({...newOrder, warehouse_id: e.target.value})}
                    required
                  >
                    <option value="">请选择仓库</option>
                    {warehouses.map(warehouse => (
                      <option key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                        {warehouse.warehouse_name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="text-lg font-medium mb-4">添加商品</h3>
                <div className="grid grid-cols-4 gap-4 items-end">
                  <div>
                    <Label htmlFor="product_id">商品</Label>
                    <select
                      id="product_id"
                      className="w-full p-2 border rounded"
                      value={newItem.product_id}
                      onChange={(e) => setNewItem({...newItem, product_id: e.target.value})}
                    >
                      <option value="">请选择商品</option>
                      {products.map(product => (
                        <option key={product.product_id} value={product.product_id}>
                          {product.product_name} ({product.sku})
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="quantity">数量</Label>
                    <Input
                      id="quantity"
                      type="number"
                      value={newItem.quantity}
                      onChange={(e) => setNewItem({...newItem, quantity: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="unit_price">单价</Label>
                    <Input
                      id="unit_price"
                      type="number"
                      step="0.01"
                      value={newItem.unit_price}
                      onChange={(e) => setNewItem({...newItem, unit_price: e.target.value})}
                    />
                  </div>
                  <div>
                    <Button type="button" onClick={addItemToOrder}>
                      添加
                    </Button>
                  </div>
                </div>
              </div>

              {newOrder.items.length > 0 && (
                <div className="border-t pt-4">
                  <h3 className="text-lg font-medium mb-4">商品明细</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>商品名称</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>数量</TableHead>
                        <TableHead>单价</TableHead>
                        <TableHead>小计</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {newOrder.items.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>{item.product_name}</TableCell>
                          <TableCell>{item.sku}</TableCell>
                          <TableCell>{item.quantity}</TableCell>
                          <TableCell>¥{item.unit_price}</TableCell>
                          <TableCell>¥{(item.quantity * item.unit_price).toFixed(2)}</TableCell>
                          <TableCell>
                            <Button 
                              type="button" 
                              variant="destructive" 
                              size="sm"
                              onClick={() => removeItemFromOrder(index)}
                            >
                              删除
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  <div className="text-right mt-4">
                    <strong>
                      总金额: ¥{newOrder.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0).toFixed(2)}
                    </strong>
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                <Button type="submit">创建入库单</Button>
                <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                  取消
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>入库单列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>入库单号</TableHead>
                <TableHead>入库类型</TableHead>
                <TableHead>供应商</TableHead>
                <TableHead>目标仓库</TableHead>
                <TableHead>总金额</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {inboundOrders.map((order) => (
                <TableRow key={order.inbound_id}>
                  <TableCell>{order.order_number}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {order.order_type === 'purchase' ? '采购入库' : 
                       order.order_type === 'return' ? '退货入库' : '调拨入库'}
                    </Badge>
                  </TableCell>
                  <TableCell>{order.supplier_name}</TableCell>
                  <TableCell>{order.warehouse_name}</TableCell>
                  <TableCell>¥{order.total_amount}</TableCell>
                  <TableCell>
                    <Badge variant={order.status === 'confirmed' ? 'default' : 'secondary'}>
                      {order.status === 'pending' ? '待确认' : '已确认'}
                    </Badge>
                  </TableCell>
                  <TableCell>{new Date(order.created_at).toLocaleDateString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

// 出库管理组件
function OutboundManagement() {
  const [outboundOrders, setOutboundOrders] = useState([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [products, setProducts] = useState([])
  const [warehouses, setWarehouses] = useState([])
  const [customers, setCustomers] = useState([])
  const [newOrder, setNewOrder] = useState({
    customer_id: '',
    warehouse_id: '',
    order_type: 'sales',
    items: []
  })
  const [newItem, setNewItem] = useState({
    product_id: '',
    quantity: '',
    unit_price: ''
  })

  useEffect(() => {
    fetchOutboundOrders()
    fetchProducts()
    fetchWarehouses()
    fetchCustomers()
  }, [])

  const fetchOutboundOrders = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/outbound`)
      const data = await response.json()
      if (data.code === 200) {
        setOutboundOrders(data.data)
      }
    } catch (error) {
      console.error('获取出库单列表失败:', error)
    }
  }

  const fetchProducts = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/products`)
      const data = await response.json()
      if (data.code === 200) {
        setProducts(data.data)
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
    }
  }

  const fetchWarehouses = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/warehouses`)
      const data = await response.json()
      if (data.code === 200) {
        setWarehouses(data.data)
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error)
    }
  }

  const fetchCustomers = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/customers`)
      const data = await response.json()
      if (data.code === 200) {
        setCustomers(data.data)
      }
    } catch (error) {
      console.error('获取客户列表失败:', error)
    }
  }

  const addItemToOrder = () => {
    if (newItem.product_id && newItem.quantity && newItem.unit_price) {
      const product = products.find(p => p.product_id === parseInt(newItem.product_id))
      setNewOrder({
        ...newOrder,
        items: [...newOrder.items, {
          ...newItem,
          product_name: product?.product_name || '',
          sku: product?.sku || ''
        }]
      })
      setNewItem({
        product_id: '',
        quantity: '',
        unit_price: ''
      })
    }
  }

  const removeItemFromOrder = (index) => {
    const updatedItems = newOrder.items.filter((_, i) => i !== index)
    setNewOrder({...newOrder, items: updatedItems})
  }

  const handleCreateOrder = async (e) => {
    e.preventDefault()
    if (newOrder.items.length === 0) {
      alert('请至少添加一个商品')
      return
    }
    
    try {
      const response = await fetch(`${API_BASE_URL}/outbound`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newOrder)
      })
      const data = await response.json()
      if (data.code === 200) {
        setShowAddForm(false)
        setNewOrder({
          customer_id: '',
          warehouse_id: '',
          order_type: 'sales',
          items: []
        })
        fetchOutboundOrders()
      } else {
        alert(data.message)
      }
    } catch (error) {
      console.error('创建出库单失败:', error)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">出库管理</h1>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          创建出库单
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>创建出库单</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCreateOrder} className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="order_type">出库类型</Label>
                  <select
                    id="order_type"
                    className="w-full p-2 border rounded"
                    value={newOrder.order_type}
                    onChange={(e) => setNewOrder({...newOrder, order_type: e.target.value})}
                  >
                    <option value="sales">销售出库</option>
                    <option value="transfer">调拨出库</option>
                    <option value="damage">报损出库</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="customer_id">客户</Label>
                  <select
                    id="customer_id"
                    className="w-full p-2 border rounded"
                    value={newOrder.customer_id}
                    onChange={(e) => setNewOrder({...newOrder, customer_id: e.target.value})}
                    required
                  >
                    <option value="">请选择客户</option>
                    {customers.map(customer => (
                      <option key={customer.customer_id} value={customer.customer_id}>
                        {customer.customer_name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <Label htmlFor="warehouse_id">出库仓库</Label>
                  <select
                    id="warehouse_id"
                    className="w-full p-2 border rounded"
                    value={newOrder.warehouse_id}
                    onChange={(e) => setNewOrder({...newOrder, warehouse_id: e.target.value})}
                    required
                  >
                    <option value="">请选择仓库</option>
                    {warehouses.map(warehouse => (
                      <option key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                        {warehouse.warehouse_name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="text-lg font-medium mb-4">添加商品</h3>
                <div className="grid grid-cols-4 gap-4 items-end">
                  <div>
                    <Label htmlFor="product_id">商品</Label>
                    <select
                      id="product_id"
                      className="w-full p-2 border rounded"
                      value={newItem.product_id}
                      onChange={(e) => setNewItem({...newItem, product_id: e.target.value})}
                    >
                      <option value="">请选择商品</option>
                      {products.map(product => (
                        <option key={product.product_id} value={product.product_id}>
                          {product.product_name} ({product.sku})
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="quantity">数量</Label>
                    <Input
                      id="quantity"
                      type="number"
                      value={newItem.quantity}
                      onChange={(e) => setNewItem({...newItem, quantity: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="unit_price">单价</Label>
                    <Input
                      id="unit_price"
                      type="number"
                      step="0.01"
                      value={newItem.unit_price}
                      onChange={(e) => setNewItem({...newItem, unit_price: e.target.value})}
                    />
                  </div>
                  <div>
                    <Button type="button" onClick={addItemToOrder}>
                      添加
                    </Button>
                  </div>
                </div>
              </div>

              {newOrder.items.length > 0 && (
                <div className="border-t pt-4">
                  <h3 className="text-lg font-medium mb-4">商品明细</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>商品名称</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>数量</TableHead>
                        <TableHead>单价</TableHead>
                        <TableHead>小计</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {newOrder.items.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>{item.product_name}</TableCell>
                          <TableCell>{item.sku}</TableCell>
                          <TableCell>{item.quantity}</TableCell>
                          <TableCell>¥{item.unit_price}</TableCell>
                          <TableCell>¥{(item.quantity * item.unit_price).toFixed(2)}</TableCell>
                          <TableCell>
                            <Button 
                              type="button" 
                              variant="destructive" 
                              size="sm"
                              onClick={() => removeItemFromOrder(index)}
                            >
                              删除
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  <div className="text-right mt-4">
                    <strong>
                      总金额: ¥{newOrder.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0).toFixed(2)}
                    </strong>
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                <Button type="submit">创建出库单</Button>
                <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                  取消
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>出库单列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>出库单号</TableHead>
                <TableHead>出库类型</TableHead>
                <TableHead>客户</TableHead>
                <TableHead>出库仓库</TableHead>
                <TableHead>总金额</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {outboundOrders.map((order) => (
                <TableRow key={order.outbound_id}>
                  <TableCell>{order.order_number}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {order.order_type === 'sales' ? '销售出库' : 
                       order.order_type === 'transfer' ? '调拨出库' : '报损出库'}
                    </Badge>
                  </TableCell>
                  <TableCell>{order.customer_name}</TableCell>
                  <TableCell>{order.warehouse_name}</TableCell>
                  <TableCell>¥{order.total_amount}</TableCell>
                  <TableCell>
                    <Badge variant={order.status === 'confirmed' ? 'default' : 'secondary'}>
                      {order.status === 'pending' ? '待确认' : '已确认'}
                    </Badge>
                  </TableCell>
                  <TableCell>{new Date(order.created_at).toLocaleDateString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

// 库存查询组件
function InventoryQuery() {
  const [inventory, setInventory] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchInventory()
  }, [])

  const fetchInventory = async () => {
    setLoading(true)
    try {
      const response = await fetch(`${API_BASE_URL}/inventory`)
      const data = await response.json()
      if (data.code === 200) {
        setInventory(data.data)
      }
    } catch (error) {
      console.error('获取库存失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-gray-900">库存查询</h1>

      <Card>
        <CardHeader>
          <CardTitle>实时库存</CardTitle>
          <CardDescription>查看所有商品的实时库存情况</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">加载中...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>商品名称</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>仓库</TableHead>
                  <TableHead>总库存</TableHead>
                  <TableHead>可用库存</TableHead>
                  <TableHead>锁定库存</TableHead>
                  <TableHead>预警阈值</TableHead>
                  <TableHead>状态</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {inventory.map((item) => (
                  <TableRow key={item.inventory_id}>
                    <TableCell>{item.product_name}</TableCell>
                    <TableCell>{item.sku}</TableCell>
                    <TableCell>{item.warehouse_name}</TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>{item.available_quantity}</TableCell>
                    <TableCell>{item.locked_quantity}</TableCell>
                    <TableCell>{item.warning_threshold}</TableCell>
                    <TableCell>
                      {item.available_quantity <= item.warning_threshold ? (
                        <Badge variant="destructive">库存不足</Badge>
                      ) : (
                        <Badge variant="default">正常</Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// 库存预警组件
function InventoryWarnings() {
  const [warnings, setWarnings] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchWarnings()
  }, [])

  const fetchWarnings = async () => {
    setLoading(true)
    try {
      const response = await fetch(`${API_BASE_URL}/inventory/warnings`)
      const data = await response.json()
      if (data.code === 200) {
        setWarnings(data.data)
      }
    } catch (error) {
      console.error('获取预警信息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-gray-900">库存预警</h1>

      {warnings.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            发现 {warnings.length} 个商品库存不足，请及时补货！
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>预警列表</CardTitle>
          <CardDescription>库存低于预警阈值的商品</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">加载中...</div>
          ) : warnings.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无库存预警
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>商品名称</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>仓库</TableHead>
                  <TableHead>当前库存</TableHead>
                  <TableHead>预警阈值</TableHead>
                  <TableHead>预警信息</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {warnings.map((warning, index) => (
                  <TableRow key={index}>
                    <TableCell>{warning.product_name}</TableCell>
                    <TableCell>{warning.sku}</TableCell>
                    <TableCell>{warning.warehouse_name}</TableCell>
                    <TableCell className="text-red-600 font-medium">
                      {warning.current_quantity}
                    </TableCell>
                    <TableCell>{warning.warning_threshold}</TableCell>
                    <TableCell>
                      <Badge variant="destructive">{warning.message}</Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-100">
        <Navigation />
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/products" element={<ProductManagement />} />
            <Route path="/warehouses" element={<WarehouseManagement />} />
            <Route path="/inventory" element={<InventoryQuery />} />
            <Route path="/inbound" element={<InboundManagement />} />
            <Route path="/outbound" element={<OutboundManagement />} />
            <Route path="/warnings" element={<InventoryWarnings />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App


