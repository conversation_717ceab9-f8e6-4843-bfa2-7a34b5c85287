from flask import Blueprint, jsonify, request

user_test_bp = Blueprint('user_test', __name__)

@user_test_bp.route('/users-test', methods=['GET'])
def get_users_test():
    """测试获取用户列表"""
    return jsonify({
        'code': 200, 
        'message': '获取用户列表成功', 
        'success': True, 
        'data': [
            {'id': 1, 'username': 'test_user', 'email': '<EMAIL>'}
        ]
    })

@user_test_bp.route('/users-test', methods=['POST'])
def create_user_test():
    """测试创建用户"""
    try:
        data = request.json
        if not data or not data.get('username') or not data.get('email'):
            return jsonify({'code': 400, 'message': '用户名和邮箱不能为空', 'success': False}), 400
        
        return jsonify({
            'code': 200, 
            'message': '用户创建成功', 
            'success': True, 
            'data': {
                'id': 999,
                'username': data.get('username'),
                'email': data.get('email')
            }
        })
    except Exception as e:
        return jsonify({'code': 500, 'message': f'创建用户失败：{str(e)}', 'success': False}), 500
