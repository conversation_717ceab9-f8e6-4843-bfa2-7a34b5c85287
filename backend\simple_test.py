#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Flask应用
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, jsonify
from src.models.inventory import db
from src.models.user import User

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'src', 'database', 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

@app.route('/test/users', methods=['GET'])
def test_get_users():
    """测试获取用户"""
    try:
        users = User.query.all()
        result = []
        for user in users:
            result.append({
                'id': user.id,
                'username': user.username,
                'email': user.email
            })
        return jsonify({'success': True, 'data': result})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/test/users', methods=['POST'])
def test_create_user():
    """测试创建用户"""
    try:
        from flask import request
        data = request.json
        user = User(username=data['username'], email=data['email'])
        db.session.add(user)
        db.session.commit()
        return jsonify({'success': True, 'message': '用户创建成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    print("🚀 启动简单测试服务器...")
    print("📡 测试地址: http://localhost:5001/test/users")
    app.run(host='0.0.0.0', port=5001, debug=True)
