#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试用户API
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/api/test', methods=['GET'])
def test_route():
    """测试路由"""
    return jsonify({'message': '测试成功', 'status': 'ok'})

@app.route('/api/users', methods=['GET'])
def simple_users():
    """简单的用户路由"""
    return jsonify({
        'code': 200,
        'message': '获取用户列表成功',
        'success': True,
        'data': [
            {'id': 1, 'username': 'test', 'email': '<EMAIL>'}
        ]
    })

if __name__ == '__main__':
    print("🚀 启动最小化测试服务器...")
    print("📡 测试地址: http://localhost:5002/api/test")
    print("👤 用户API: http://localhost:5002/api/users")
    app.run(host='0.0.0.0', port=5002, debug=True)
