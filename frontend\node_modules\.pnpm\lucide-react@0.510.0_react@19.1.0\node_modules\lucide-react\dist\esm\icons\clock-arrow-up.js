/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M13.228 21.925A10 10 0 1 1 21.994 12.338", key: "1fzlyi" }],
  ["path", { d: "M12 6v6l1.562.781", key: "1ujuk9" }],
  ["path", { d: "m14 18 4-4 4 4", key: "ftkppy" }],
  ["path", { d: "M18 22v-8", key: "su0gjh" }]
];
const ClockArrowUp = createLucideIcon("clock-arrow-up", __iconNode);

export { __iconNode, ClockArrowUp as default };
//# sourceMappingURL=clock-arrow-up.js.map
