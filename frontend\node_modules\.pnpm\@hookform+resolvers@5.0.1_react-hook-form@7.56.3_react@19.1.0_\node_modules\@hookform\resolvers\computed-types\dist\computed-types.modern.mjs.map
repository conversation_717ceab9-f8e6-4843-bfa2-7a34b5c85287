{"version": 3, "file": "computed-types.modern.mjs", "sources": ["../src/computed-types.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { ValidationError } from 'computed-types';\nimport FunctionType from 'computed-types/lib/schema/FunctionType';\nimport type { FieldErrors, FieldValues, Resolver } from 'react-hook-form';\n\nconst isValidationError = (error: any): error is ValidationError =>\n  error.errors != null;\n\nfunction parseErrorSchema(computedTypesError: ValidationError) {\n  const parsedErrors: FieldErrors = {};\n  return (computedTypesError.errors || []).reduce((acc, error) => {\n    acc[error.path.join('.')] = {\n      type: error.error.name,\n      message: error.error.message,\n    };\n\n    return acc;\n  }, parsedErrors);\n}\n\n/**\n * Creates a resolver for react-hook-form using computed-types schema validation\n * @param {Schema} schema - The computed-types schema to validate against\n * @returns {Resolver<Type<typeof schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = Schema({\n *   name: string,\n *   age: number\n * });\n *\n * useForm({\n *   resolver: computedTypesResolver(schema)\n * });\n */\nexport function computedTypesResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n>(schema: FunctionType<Output, [Input]>): Resolver<Input, Context, Output> {\n  return async (values, _, options) => {\n    try {\n      const data = await schema(values);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {},\n        values: data,\n      };\n    } catch (error: any) {\n      if (isValidationError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(parseErrorSchema(error), options),\n        };\n      }\n\n      throw error;\n    }\n  };\n}\n"], "names": ["computedTypesResolver", "schema", "values", "_", "options", "data", "shouldUseNativeValidation", "validateFieldsNatively", "errors", "error", "isValidationError", "toNestErrors", "computedTypesError", "reduce", "acc", "path", "join", "type", "name", "message"], "mappings": "+EAkCgB,SAAAA,EAIdC,GACA,aAAcC,EAAQC,EAAGC,KACvB,IACE,MAAMC,QAAaJ,EAAOC,GAI1B,OAFAE,EAAQE,2BAA6BC,EAAuB,CAAA,EAAIH,GAEzD,CACLI,OAAQ,CAAE,EACVN,OAAQG,EAEZ,CAAE,MAAOI,GACP,GA7CqBA,IACT,MAAhBA,EAAMD,OA4CEE,CAAkBD,GACpB,MAAO,CACLP,OAAQ,CAAE,EACVM,OAAQG,GA7CQC,EA6CsBH,GA3CtCG,EAAmBJ,QAAU,IAAIK,OAAO,CAACC,EAAKL,KACpDK,EAAIL,EAAMM,KAAKC,KAAK,MAAQ,CAC1BC,KAAMR,EAAMA,MAAMS,KAClBC,QAASV,EAAMA,MAAMU,SAGhBL,GAPyB,KA4CoBV,IAIlD,MAAMK,CACR,CAlDJ,IAA0BG,CAkDtB,CAEJ"}