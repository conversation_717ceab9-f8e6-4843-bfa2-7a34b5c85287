{"version": 3, "names": ["_index", "require", "_t", "_context", "VISITOR_KEYS", "TraversalContext", "constructor", "scope", "opts", "state", "parentPath", "queue", "priorityQueue", "shouldVisit", "node", "enter", "exit", "type", "keys", "length", "key", "create", "container", "<PERSON><PERSON><PERSON>", "NodePath", "get", "parent", "maybeQueue", "path", "notPriority", "push", "visitMultiple", "visitQueue", "visitSingle", "visited", "WeakSet", "stop", "visitIndex", "resync", "call", "contexts", "pushContext", "has", "add", "visit", "i", "popContext", "nodes", "Array", "isArray", "exports", "default"], "sources": ["../src/context.ts"], "sourcesContent": ["import NodePath from \"./path/index.ts\";\nimport { VISITOR_KEYS } from \"@babel/types\";\nimport type Scope from \"./scope/index.ts\";\nimport type { ExplodedTraverseOptions } from \"./index.ts\";\nimport type * as t from \"@babel/types\";\nimport type { Visitor } from \"./types.ts\";\nimport { popContext, pushContext, resync } from \"./path/context.ts\";\n\nexport default class TraversalContext<S = unknown> {\n  constructor(\n    scope: Scope,\n    opts: ExplodedTraverseOptions<S>,\n    state: S,\n    parentPath: NodePath,\n  ) {\n    this.parentPath = parentPath;\n    this.scope = scope;\n    this.state = state;\n    this.opts = opts;\n  }\n\n  declare parentPath: NodePath;\n  declare scope: Scope;\n  declare state: S;\n  declare opts: ExplodedTraverseOptions<S>;\n  queue: Array<NodePath> | null = null;\n  priorityQueue: Array<NodePath> | null = null;\n\n  /**\n   * This method does a simple check to determine whether or not we really need to attempt\n   * visit a node. This will prevent us from constructing a NodePath.\n   */\n\n  shouldVisit(node: t.Node): boolean {\n    const opts = this.opts as Visitor;\n    if (opts.enter || opts.exit) return true;\n\n    // check if we have a visitor for this node\n    if (opts[node.type]) return true;\n\n    // check if we're going to traverse into this node\n    const keys: Array<string> | undefined = VISITOR_KEYS[node.type];\n    if (!keys?.length) return false;\n\n    // we need to traverse into this node so ensure that it has children to traverse into!\n    for (const key of keys) {\n      if (\n        // @ts-expect-error key is from visitor keys\n        node[key]\n      ) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  create(\n    node: t.Node,\n    container: t.Node | t.Node[],\n    key: string | number,\n    listKey?: string,\n  ): NodePath {\n    // We don't need to `.setContext()` here, since `.visitQueue()` already\n    // calls `.pushContext`.\n    return NodePath.get({\n      parentPath: this.parentPath,\n      parent: node,\n      container,\n      key: key,\n      listKey,\n    });\n  }\n\n  maybeQueue(path: NodePath, notPriority?: boolean) {\n    if (this.queue) {\n      if (notPriority) {\n        this.queue.push(path);\n      } else {\n        this.priorityQueue.push(path);\n      }\n    }\n  }\n\n  visitMultiple(container: t.Node[], parent: t.Node, listKey: string) {\n    // nothing to traverse!\n    if (container.length === 0) return false;\n\n    const queue = [];\n\n    // build up initial queue\n    for (let key = 0; key < container.length; key++) {\n      const node = container[key];\n      if (node && this.shouldVisit(node)) {\n        queue.push(this.create(parent, container, key, listKey));\n      }\n    }\n\n    return this.visitQueue(queue);\n  }\n\n  visitSingle(node: t.Node, key: string): boolean {\n    if (\n      this.shouldVisit(\n        // @ts-expect-error key may not index node\n        node[key],\n      )\n    ) {\n      return this.visitQueue([this.create(node, node, key)]);\n    } else {\n      return false;\n    }\n  }\n\n  visitQueue(queue: Array<NodePath>): boolean {\n    // set queue\n    this.queue = queue;\n    this.priorityQueue = [];\n\n    const visited = new WeakSet();\n    let stop = false;\n    let visitIndex = 0;\n\n    // visit the queue\n    for (; visitIndex < queue.length; ) {\n      const path = queue[visitIndex];\n      visitIndex++;\n      resync.call(path);\n\n      if (\n        path.contexts.length === 0 ||\n        path.contexts[path.contexts.length - 1] !== this\n      ) {\n        // The context might already have been pushed when this path was inserted and queued.\n        // If we always re-pushed here, we could get duplicates and risk leaving contexts\n        // on the stack after the traversal has completed, which could break things.\n        pushContext.call(path, this);\n      }\n\n      // this path no longer belongs to the tree\n      if (path.key === null) continue;\n\n      // ensure we don't visit the same node twice\n      const { node } = path;\n      if (visited.has(node)) continue;\n      if (node) visited.add(node);\n\n      if (path.visit()) {\n        stop = true;\n        break;\n      }\n\n      if (this.priorityQueue.length) {\n        stop = this.visitQueue(this.priorityQueue);\n        this.priorityQueue = [];\n        this.queue = queue;\n        if (stop) break;\n      }\n    }\n\n    // pop contexts\n    for (let i = 0; i < visitIndex; i++) {\n      popContext.call(queue[i]);\n    }\n\n    // clear queue\n    this.queue = null;\n\n    return stop;\n  }\n\n  visit(node: t.Node, key: string) {\n    // @ts-expect-error key may not index node\n    const nodes = node[key] as t.Node | t.Node[] | null;\n    if (!nodes) return false;\n\n    if (Array.isArray(nodes)) {\n      return this.visitMultiple(nodes, node, key);\n    } else {\n      return this.visitSingle(node, key);\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAD,OAAA;AAKA,IAAAE,QAAA,GAAAF,OAAA;AAAoE;EAL3DG;AAAY,IAAAF,EAAA;AAON,MAAMG,gBAAgB,CAAc;EACjDC,WAAWA,CACTC,KAAY,EACZC,IAAgC,EAChCC,KAAQ,EACRC,UAAoB,EACpB;IAAA,KAWFC,KAAK,GAA2B,IAAI;IAAA,KACpCC,aAAa,GAA2B,IAAI;IAX1C,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,IAAI,GAAGA,IAAI;EAClB;EAcAK,WAAWA,CAACC,IAAY,EAAW;IACjC,MAAMN,IAAI,GAAG,IAAI,CAACA,IAAe;IACjC,IAAIA,IAAI,CAACO,KAAK,IAAIP,IAAI,CAACQ,IAAI,EAAE,OAAO,IAAI;IAGxC,IAAIR,IAAI,CAACM,IAAI,CAACG,IAAI,CAAC,EAAE,OAAO,IAAI;IAGhC,MAAMC,IAA+B,GAAGd,YAAY,CAACU,IAAI,CAACG,IAAI,CAAC;IAC/D,IAAI,EAACC,IAAI,YAAJA,IAAI,CAAEC,MAAM,GAAE,OAAO,KAAK;IAG/B,KAAK,MAAMC,GAAG,IAAIF,IAAI,EAAE;MACtB,IAEEJ,IAAI,CAACM,GAAG,CAAC,EACT;QACA,OAAO,IAAI;MACb;IACF;IAEA,OAAO,KAAK;EACd;EAEAC,MAAMA,CACJP,IAAY,EACZQ,SAA4B,EAC5BF,GAAoB,EACpBG,OAAgB,EACN;IAGV,OAAOC,cAAQ,CAACC,GAAG,CAAC;MAClBf,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BgB,MAAM,EAAEZ,IAAI;MACZQ,SAAS;MACTF,GAAG,EAAEA,GAAG;MACRG;IACF,CAAC,CAAC;EACJ;EAEAI,UAAUA,CAACC,IAAc,EAAEC,WAAqB,EAAE;IAChD,IAAI,IAAI,CAAClB,KAAK,EAAE;MACd,IAAIkB,WAAW,EAAE;QACf,IAAI,CAAClB,KAAK,CAACmB,IAAI,CAACF,IAAI,CAAC;MACvB,CAAC,MAAM;QACL,IAAI,CAAChB,aAAa,CAACkB,IAAI,CAACF,IAAI,CAAC;MAC/B;IACF;EACF;EAEAG,aAAaA,CAACT,SAAmB,EAAEI,MAAc,EAAEH,OAAe,EAAE;IAElE,IAAID,SAAS,CAACH,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAExC,MAAMR,KAAK,GAAG,EAAE;IAGhB,KAAK,IAAIS,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGE,SAAS,CAACH,MAAM,EAAEC,GAAG,EAAE,EAAE;MAC/C,MAAMN,IAAI,GAAGQ,SAAS,CAACF,GAAG,CAAC;MAC3B,IAAIN,IAAI,IAAI,IAAI,CAACD,WAAW,CAACC,IAAI,CAAC,EAAE;QAClCH,KAAK,CAACmB,IAAI,CAAC,IAAI,CAACT,MAAM,CAACK,MAAM,EAAEJ,SAAS,EAAEF,GAAG,EAAEG,OAAO,CAAC,CAAC;MAC1D;IACF;IAEA,OAAO,IAAI,CAACS,UAAU,CAACrB,KAAK,CAAC;EAC/B;EAEAsB,WAAWA,CAACnB,IAAY,EAAEM,GAAW,EAAW;IAC9C,IACE,IAAI,CAACP,WAAW,CAEdC,IAAI,CAACM,GAAG,CACV,CAAC,EACD;MACA,OAAO,IAAI,CAACY,UAAU,CAAC,CAAC,IAAI,CAACX,MAAM,CAACP,IAAI,EAAEA,IAAI,EAAEM,GAAG,CAAC,CAAC,CAAC;IACxD,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;EAEAY,UAAUA,CAACrB,KAAsB,EAAW;IAE1C,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAG,EAAE;IAEvB,MAAMsB,OAAO,GAAG,IAAIC,OAAO,CAAC,CAAC;IAC7B,IAAIC,IAAI,GAAG,KAAK;IAChB,IAAIC,UAAU,GAAG,CAAC;IAGlB,OAAOA,UAAU,GAAG1B,KAAK,CAACQ,MAAM,GAAI;MAClC,MAAMS,IAAI,GAAGjB,KAAK,CAAC0B,UAAU,CAAC;MAC9BA,UAAU,EAAE;MACZC,eAAM,CAACC,IAAI,CAACX,IAAI,CAAC;MAEjB,IACEA,IAAI,CAACY,QAAQ,CAACrB,MAAM,KAAK,CAAC,IAC1BS,IAAI,CAACY,QAAQ,CAACZ,IAAI,CAACY,QAAQ,CAACrB,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAChD;QAIAsB,oBAAW,CAACF,IAAI,CAACX,IAAI,EAAE,IAAI,CAAC;MAC9B;MAGA,IAAIA,IAAI,CAACR,GAAG,KAAK,IAAI,EAAE;MAGvB,MAAM;QAAEN;MAAK,CAAC,GAAGc,IAAI;MACrB,IAAIM,OAAO,CAACQ,GAAG,CAAC5B,IAAI,CAAC,EAAE;MACvB,IAAIA,IAAI,EAAEoB,OAAO,CAACS,GAAG,CAAC7B,IAAI,CAAC;MAE3B,IAAIc,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAE;QAChBR,IAAI,GAAG,IAAI;QACX;MACF;MAEA,IAAI,IAAI,CAACxB,aAAa,CAACO,MAAM,EAAE;QAC7BiB,IAAI,GAAG,IAAI,CAACJ,UAAU,CAAC,IAAI,CAACpB,aAAa,CAAC;QAC1C,IAAI,CAACA,aAAa,GAAG,EAAE;QACvB,IAAI,CAACD,KAAK,GAAGA,KAAK;QAClB,IAAIyB,IAAI,EAAE;MACZ;IACF;IAGA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,UAAU,EAAEQ,CAAC,EAAE,EAAE;MACnCC,mBAAU,CAACP,IAAI,CAAC5B,KAAK,CAACkC,CAAC,CAAC,CAAC;IAC3B;IAGA,IAAI,CAAClC,KAAK,GAAG,IAAI;IAEjB,OAAOyB,IAAI;EACb;EAEAQ,KAAKA,CAAC9B,IAAY,EAAEM,GAAW,EAAE;IAE/B,MAAM2B,KAAK,GAAGjC,IAAI,CAACM,GAAG,CAA6B;IACnD,IAAI,CAAC2B,KAAK,EAAE,OAAO,KAAK;IAExB,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MACxB,OAAO,IAAI,CAAChB,aAAa,CAACgB,KAAK,EAAEjC,IAAI,EAAEM,GAAG,CAAC;IAC7C,CAAC,MAAM;MACL,OAAO,IAAI,CAACa,WAAW,CAACnB,IAAI,EAAEM,GAAG,CAAC;IACpC;EACF;AACF;AAAC8B,OAAA,CAAAC,OAAA,GAAA9C,gBAAA", "ignoreList": []}