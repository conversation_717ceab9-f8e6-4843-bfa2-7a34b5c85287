<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 电商库存管理系统 - 后端API</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔧</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(16, 185, 129, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            border: 1px solid rgba(16, 185, 129, 0.3);
            margin-bottom: 2rem;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: blink 1.5s infinite;
        }
        
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .api-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .api-info h3 {
            margin-bottom: 1rem;
            color: #fbbf24;
        }
        
        .endpoint {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .endpoint:last-child {
            border-bottom: none;
        }
        
        .method {
            background: #3b82f6;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            min-width: 60px;
            text-align: center;
        }
        
        .method.post { background: #10b981; }
        .method.delete { background: #ef4444; }
        
        .url {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.2);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            flex: 1;
            margin: 0 1rem;
        }
        
        .description {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .footer {
            margin-top: 2rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .version {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .logo {
                font-size: 3rem;
            }
            
            .endpoint {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .url {
                margin: 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔧</div>
        <h1>电商库存管理系统</h1>
        <div class="subtitle">后端API服务</div>
        
        <div class="status">
            <div class="status-dot"></div>
            <span>服务运行中</span>
        </div>
        
        <div class="api-info">
            <h3>📡 主要API端点</h3>
            
            <div class="endpoint">
                <span class="method">GET</span>
                <span class="url">/api/products</span>
                <span class="description">获取商品列表</span>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/products</span>
                <span class="description">创建商品</span>
            </div>
            
            <div class="endpoint">
                <span class="method delete">DELETE</span>
                <span class="url">/api/products/{id}</span>
                <span class="description">删除商品</span>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span>
                <span class="url">/api/warehouses</span>
                <span class="description">获取仓库列表</span>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span>
                <span class="url">/api/inventory</span>
                <span class="description">查询库存</span>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span>
                <span class="url">/api/inventory/warnings</span>
                <span class="description">获取库存预警</span>
            </div>
        </div>
        
        <div class="footer">
            <p>版本: <span class="version">v1.0</span></p>
            <p>🏠 前端应用: <a href="http://localhost:8080" style="color: #fbbf24;">http://localhost:8080</a></p>
            <p>📖 API文档: 查看项目中的API文档.md</p>
        </div>
    </div>
    
    <script>
        // 简单的状态检查
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里添加API状态检查逻辑
            console.log('🔧 后端API服务页面加载完成');
        });
    </script>
</body>
</html>
