!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,(function(t,e){"use strict";function n(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,Object.freeze(e)}var i=n(e),s=React,o=Symbol.for("react.element"),r=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function c(t,e,n){var i,s={},r=null,c=null;for(i in void 0!==n&&(r=""+n),void 0!==e.key&&(r=""+e.key),void 0!==e.ref&&(c=e.ref),e)a.call(e,i)&&!u.hasOwnProperty(i)&&(s[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps)void 0===s[i]&&(s[i]=e[i]);return{$$typeof:o,type:t,key:r,ref:c,props:s,_owner:l.current}}const h=r,d=c,p=c,m=e.createContext({});function f(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}const g="undefined"!=typeof window,y=g?e.useLayoutEffect:e.useEffect,v=e.createContext(null);function x(t,e){-1===t.indexOf(e)&&t.push(e)}function w(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}function T([...t],e,n){const i=e<0?t.length+e:e;if(i>=0&&i<t.length){const i=n<0?t.length+n:n,[s]=t.splice(e,1);t.splice(i,0,s)}return t}const P=(t,e,n)=>n>e?e:n<t?t:n;let S=()=>{},b=()=>{};const A={},E=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function M(t){return"object"==typeof t&&null!==t}const V=t=>/^0[^.\s]+$/u.test(t);function C(t){let e;return()=>(void 0===e&&(e=t()),e)}const R=t=>t,D=(t,e)=>n=>e(t(n)),k=(...t)=>t.reduce(D),L=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class O{constructor(){this.subscriptions=[]}add(t){return x(this.subscriptions,t),()=>w(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const j=t=>1e3*t,B=t=>t/1e3;function F(t,e){return e?t*(1e3/e):0}const I=new Set;const W=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t},U=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function N(t,e,n,i){if(t===e&&n===i)return R;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=U(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:U(s(t),e,i)}const $=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,z=t=>e=>1-t(1-e),X=N(.33,1.53,.69,.99),Y=z(X),H=$(Y),K=t=>(t*=2)<1?.5*Y(t):.5*(2-Math.pow(2,-10*(t-1))),G=t=>1-Math.sin(Math.acos(t)),_=z(G),q=$(G),Z=N(.42,0,1,1),J=N(0,0,.58,1),Q=N(.42,0,.58,1);const tt=t=>Array.isArray(t)&&"number"!=typeof t[0];function et(t,e){return tt(t)?t[W(0,t.length,e)]:t}const nt=t=>Array.isArray(t)&&"number"==typeof t[0],it={linear:R,easeIn:Z,easeInOut:Q,easeOut:J,circIn:G,circInOut:q,circOut:_,backIn:Y,backInOut:H,backOut:X,anticipate:K},st=t=>{if(nt(t)){t.length;const[e,n,i,s]=t;return N(e,n,i,s)}return"string"==typeof t?it[t]:t},ot=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],rt={value:null,addProjectionMetrics:null};function at(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=ot.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){r.has(e)&&(c.schedule(e),t()),l++,e(a)}const c={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(u),e&&rt.value&&rt.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,c.process(t)))}};return c}(o,e?n:void 0),t)),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:h,preRender:d,render:p,postRender:m}=r,f=()=>{const o=A.useManualTiming?s.timestamp:performance.now();n=!1,A.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),h.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(f))};return{schedule:ot.reduce(((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(f)),a.schedule(e,o,r)),e}),{}),cancel:t=>{for(let e=0;e<ot.length;e++)r[ot[e]].cancel(t)},state:s,steps:r}}const{schedule:lt,cancel:ut,state:ct,steps:ht}=at("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:R,!0);let dt;function pt(){dt=void 0}const mt={now:()=>(void 0===dt&&mt.set(ct.isProcessing||A.useManualTiming?ct.timestamp:performance.now()),dt),set:t=>{dt=t,queueMicrotask(pt)}},ft={layout:0,mainThread:0,waapi:0},gt=t=>e=>"string"==typeof e&&e.startsWith(t),yt=gt("--"),vt=gt("var(--"),xt=t=>!!vt(t)&&wt.test(t.split("/*")[0].trim()),wt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Tt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Pt={...Tt,transform:t=>P(0,1,t)},St={...Tt,default:1},bt=t=>Math.round(1e5*t)/1e5,At=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const Et=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Mt=(t,e)=>n=>Boolean("string"==typeof n&&Et.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Vt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(At);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},Ct={...Tt,transform:t=>Math.round((t=>P(0,255,t))(t))},Rt={test:Mt("rgb","red"),parse:Vt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+Ct.transform(t)+", "+Ct.transform(e)+", "+Ct.transform(n)+", "+bt(Pt.transform(i))+")"};const Dt={test:Mt("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:Rt.transform},kt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Lt=kt("deg"),Ot=kt("%"),jt=kt("px"),Bt=kt("vh"),Ft=kt("vw"),It=(()=>({...Ot,parse:t=>Ot.parse(t)/100,transform:t=>Ot.transform(100*t)}))(),Wt={test:Mt("hsl","hue"),parse:Vt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+Ot.transform(bt(e))+", "+Ot.transform(bt(n))+", "+bt(Pt.transform(i))+")"},Ut={test:t=>Rt.test(t)||Dt.test(t)||Wt.test(t),parse:t=>Rt.test(t)?Rt.parse(t):Wt.test(t)?Wt.parse(t):Dt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Rt.transform(t):Wt.transform(t)},Nt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const $t="number",zt="color",Xt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Yt(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Xt,(t=>(Ut.test(t)?(i.color.push(o),s.push(zt),n.push(Ut.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push($t),n.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function Ht(t){return Yt(t).values}function Kt(t){const{split:e,types:n}=Yt(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===$t?bt(t[o]):e===zt?Ut.transform(t[o]):t[o]}return s}}const Gt=t=>"number"==typeof t?0:t;const _t={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(At)?.length||0)+(t.match(Nt)?.length||0)>0},parse:Ht,createTransformer:Kt,getAnimatableNone:function(t){const e=Ht(t);return Kt(t)(e.map(Gt))}};function qt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Zt({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=qt(a,i,t+1/3),o=qt(a,i,t),r=qt(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}function Jt(t,e){return n=>n>0?e:t}const Qt=(t,e,n)=>t+(e-t)*n,te=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},ee=[Dt,Rt,Wt];function ne(t){const e=(n=t,ee.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===Wt&&(i=Zt(i)),i}const ie=(t,e)=>{const n=ne(t),i=ne(e);if(!n||!i)return Jt(t,e);const s={...n};return t=>(s.red=te(n.red,i.red,t),s.green=te(n.green,i.green,t),s.blue=te(n.blue,i.blue,t),s.alpha=Qt(n.alpha,i.alpha,t),Rt.transform(s))},se=new Set(["none","hidden"]);function oe(t,e){return se.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function re(t,e){return n=>Qt(t,e,n)}function ae(t){return"number"==typeof t?re:"string"==typeof t?xt(t)?Jt:Ut.test(t)?ie:ce:Array.isArray(t)?le:"object"==typeof t?Ut.test(t)?ie:ue:Jt}function le(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>ae(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function ue(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=ae(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const ce=(t,e)=>{const n=_t.createTransformer(e),i=Yt(t),s=Yt(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?se.has(t)&&!s.values.length||se.has(e)&&!i.values.length?oe(t,e):k(le(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):Jt(t,e)};function he(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Qt(t,e,n);return ae(t)(t,e)}const de=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>lt.update(e,t),stop:()=>ut(e),now:()=>ct.isProcessing?ct.timestamp:mt.now()}},pe=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=t(e/(s-1))+", ";return`linear(${i.substring(0,i.length-2)})`},me=2e4;function fe(t){let e=0;let n=t.next(e);for(;!n.done&&e<me;)e+=50,n=t.next(e);return e>=me?1/0:e}function ge(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(fe(i),me);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:B(s)}}function ye(t,e,n){const i=Math.max(e-5,0);return F(n-t(i),e-i)}const ve=100,xe=10,we=1,Te=0,Pe=800,Se=.3,be=.3,Ae={granular:.01,default:2},Ee={granular:.005,default:.5},Me=.01,Ve=10,Ce=.05,Re=1,De=.001;function ke({duration:t=Pe,bounce:e=Se,velocity:n=Te,mass:i=we}){let s,o,r=1-e;r=P(Ce,Re,r),t=P(Me,Ve,B(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=Oe(e,r),l=Math.exp(-s);return De-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=Oe(Math.pow(e,2),r);return(-s(e)+De>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<Le;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=j(t),isNaN(a))return{stiffness:ve,damping:xe,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const Le=12;function Oe(t,e){return t*Math.sqrt(1-e*e)}const je=["duration","bounce"],Be=["stiffness","damping","mass"];function Fe(t,e){return e.some((e=>void 0!==t[e]))}function Ie(t=be,e=Se){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:Te,stiffness:ve,damping:xe,mass:we,isResolvedFromDuration:!1,...t};if(!Fe(t,Be)&&Fe(t,je))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*P(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:we,stiffness:s,damping:o}}else{const n=ke(t);e={...e,...n,mass:we},e.isResolvedFromDuration=!0}return e}({...n,velocity:-B(n.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*c)),g=r-o,y=B(Math.sqrt(l/c)),v=Math.abs(g)<5;let x;if(i||(i=v?Ae.granular:Ae.default),s||(s=v?Ee.granular:Ee.default),f<1){const t=Oe(y,f);x=e=>{const n=Math.exp(-f*y*e);return r-n*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-y*t)*(g+(m+y*g)*t);else{const t=y*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*y*e),i=Math.min(t*e,300);return r-n*((m+f*y*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&h||null,next:t=>{const e=x(t);if(p)a.done=t>=h;else{let n=0===t?m:0;f<1&&(n=0===t?j(m):ye(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(fe(w),me),e=pe((e=>w.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return w}function We({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let w,T;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(w=t,T=Ie({keyframes:[d.value,p(d.value)],velocity:ye(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==w||(e=!0,x(t),P(t)),void 0!==w&&t>=w?T.next(t-w):(!e&&x(t),d)}}}function Ue(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||A.mix||he,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||R:e;o=k(t,o)}i.push(o)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=L(t[i],t[i+1],n);return a[i](s)};return n?e=>u(P(t[0],t[o-1],e)):u}function Ne(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=L(0,e,i);t.push(Qt(n,1,s))}}function $e(t){const e=[0];return Ne(e,t.length-1),e}function ze(t,e){return t.map((t=>t*e))}function Xe(t,e){return t.map((()=>e||Q)).splice(0,t.length-1)}function Ye({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=tt(i)?i.map(st):st(i),o={done:!1,value:e[0]},r=Ue(ze(n&&n.length===e.length?n:$e(e),t),e,{ease:Array.isArray(s)?s:Xe(e,s)});return{calculatedDuration:t,next:e=>(o.value=r(e),o.done=e>=t,o)}}Ie.applyToOptions=t=>{const e=ge(t,100,Ie);return t.ease=e.ease,t.duration=j(e.duration),t.type="keyframes",t};const He=t=>null!==t;function Ke(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(He),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const Ge={decay:We,inertia:We,tween:Ye,keyframes:Ye,spring:Ie};function _e(t){"string"==typeof t.type&&(t.type=Ge[t.type])}class qe{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ze=t=>t/100;class Je extends qe{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==mt.now()&&this.tick(mt.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},ft.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;_e(t);const{type:e=Ye,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Ye;a!==Ye&&"number"!=typeof r[0]&&(this.mixKeyframes=k(Ze,he(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=fe(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let v=this.currentTime,x=n;if(c){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===h?(n=1-n,d&&(n-=d/r)):"mirror"===h&&(x=o)),v=P(0,1,n)*r}const w=y?{done:!1,value:u[0]}:x.next(v);s&&(w.value=s(w.value));let{done:T}=w;y||null===a||(T=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return S&&p!==We&&(w.value=Ke(u,this.options,f,this.speed)),m&&m(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return B(this.calculatedDuration)}get time(){return B(this.currentTime)}set time(t){t=j(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(mt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=B(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=de,startTime:e}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(mt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ft.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Qe(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const tn=t=>180*t/Math.PI,en=t=>{const e=tn(Math.atan2(t[1],t[0]));return sn(e)},nn={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:en,rotateZ:en,skewX:t=>tn(Math.atan(t[1])),skewY:t=>tn(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},sn=t=>((t%=360)<0&&(t+=360),t),on=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),rn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),an={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:on,scaleY:rn,scale:t=>(on(t)+rn(t))/2,rotateX:t=>sn(tn(Math.atan2(t[6],t[5]))),rotateY:t=>sn(tn(Math.atan2(-t[2],t[0]))),rotateZ:en,rotate:en,skewX:t=>tn(Math.atan(t[4])),skewY:t=>tn(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ln(t){return t.includes("scale")?1:0}function un(t,e){if(!t||"none"===t)return ln(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=an,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=nn,s=e}if(!s)return ln(e);const o=i[e],r=s[1].split(",").map(hn);return"function"==typeof o?o(r):r[o]}const cn=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return un(n,e)};function hn(t){return parseFloat(t.trim())}const dn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],pn=(()=>new Set(dn))(),mn=t=>t===Tt||t===jt,fn=new Set(["x","y","z"]),gn=dn.filter((t=>!fn.has(t)));const yn={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>un(e,"x"),y:(t,{transform:e})=>un(e,"y")};yn.translateX=yn.x,yn.translateY=yn.y;const vn=new Set;let xn=!1,wn=!1,Tn=!1;function Pn(){if(wn){const t=Array.from(vn).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return gn.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}wn=!1,xn=!1,vn.forEach((t=>t.complete(Tn))),vn.clear()}function Sn(){vn.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(wn=!0)}))}function bn(){Tn=!0,Sn(),Pn(),Tn=!1}class An{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(vn.add(this),xn||(xn=!0,lt.read(Sn),lt.resolveKeyframes(Pn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}Qe(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),vn.delete(this)}cancel(){"scheduled"===this.state&&(vn.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const En=t=>t.startsWith("--");function Mn(t,e,n){En(e)?t.style.setProperty(e,n):t.style[e]=n}const Vn=C((()=>void 0!==window.ScrollTimeline)),Cn={};function Rn(t,e){const n=C(t);return()=>Cn[e]??n()}const Dn=Rn((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),kn=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Ln={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:kn([0,.65,.55,1]),circOut:kn([.55,0,1,.45]),backIn:kn([.31,.01,.66,-.59]),backOut:kn([.33,1.53,.69,.99])};function On(t,e){return t?"function"==typeof t?Dn()?pe(t,e):"ease-out":nt(t)?kn(t):Array.isArray(t)?t.map((t=>On(t,e)||Ln.easeOut)):Ln[t]:void 0}function jn(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=On(a,s);Array.isArray(h)&&(c.easing=h),rt.value&&ft.waapi++;const d={delay:i,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};u&&(d.pseudoElement=u);const p=t.animate(c,d);return rt.value&&p.finished.finally((()=>{ft.waapi--})),p}function Bn(t){return"function"==typeof t&&"applyToOptions"in t}function Fn({type:t,...e}){return Bn(t)&&Dn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class In extends qe{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=Fn(t);this.animation=jn(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=Ke(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):Mn(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return B(Number(t))}get time(){return B(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=j(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Vn()?(this.animation.timeline=t,R):e(this)}}const Wn={anticipate:K,backInOut:H,circInOut:q};function Un(t){"string"==typeof t.ease&&t.ease in Wn&&(t.ease=Wn[t.ease])}class Nn extends In{constructor(t){Un(t),_e(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new Je({...o,autoplay:!1}),a=j(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const $n=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!_t.test(t)&&"0"!==t||t.startsWith("url(")));function zn(t){return M(t)&&"offsetHeight"in t}const Xn=new Set(["opacity","clipPath","filter","transform"]),Yn=C((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));function Hn(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!zn(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Yn()&&n&&Xn.has(n)&&("transform"!==n||!l)&&!a&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}class Kn extends qe{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=mt.now();const h={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||An;this.keyframeResolver=new d(r,((t,e,n)=>this.onKeyframesResolved(t,e,h,!n)),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=mt.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=$n(s,e),a=$n(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||Bn(n))&&i)}(t,s,o,r)||(!A.instantAnimations&&a||u?.(Ke(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const c={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},h=!l&&Hn(c)?new Nn({...c,element:c.motionValue.owner.current}):new Je(c);h.finished.then((()=>this.notifyFinished())).catch(R),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(this.keyframeResolver?.resume(),bn()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class Gn{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>t.finished)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map((e=>e.attachTimeline(t)));return()=>{e.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class _n extends Gn{then(t,e){return this.finished.finally(t).then((()=>{}))}}class qn extends In{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const Zn=new WeakMap,Jn=(t,e="")=>`${t}:${e}`;function Qn(t){const e=Zn.get(t)||new Map;return Zn.set(t,e),e}const ti=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ei(t){const e=ti.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}function ni(t,e,n=1){const[i,s]=ei(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return E(t)?parseFloat(t):t}return xt(s)?ni(s,e,n+1):s}function ii(t,e){return t?.[e]??t?.default??t}const si=new Set(["width","height","top","left","right","bottom",...dn]),oi=t=>e=>e.test(t),ri=[Tt,jt,Ot,Lt,Ft,Bt,{test:t=>"auto"===t,parse:t=>t}],ai=t=>ri.find(oi(t));const li=new Set(["brightness","contrast","saturate","opacity"]);function ui(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(At)||[];if(!i)return t;const s=n.replace(i,"");let o=li.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const ci=/\b([a-z-]*)\(.*?\)/gu,hi={..._t,getAnimatableNone:t=>{const e=t.match(ci);return e?e.map(ui).join(" "):t}},di={...Tt,transform:Math.round},pi={rotate:Lt,rotateX:Lt,rotateY:Lt,rotateZ:Lt,scale:St,scaleX:St,scaleY:St,scaleZ:St,skew:Lt,skewX:Lt,skewY:Lt,distance:jt,translateX:jt,translateY:jt,translateZ:jt,x:jt,y:jt,z:jt,perspective:jt,transformPerspective:jt,opacity:Pt,originX:It,originY:It,originZ:jt},mi={borderWidth:jt,borderTopWidth:jt,borderRightWidth:jt,borderBottomWidth:jt,borderLeftWidth:jt,borderRadius:jt,radius:jt,borderTopLeftRadius:jt,borderTopRightRadius:jt,borderBottomRightRadius:jt,borderBottomLeftRadius:jt,width:jt,maxWidth:jt,height:jt,maxHeight:jt,top:jt,right:jt,bottom:jt,left:jt,padding:jt,paddingTop:jt,paddingRight:jt,paddingBottom:jt,paddingLeft:jt,margin:jt,marginTop:jt,marginRight:jt,marginBottom:jt,marginLeft:jt,backgroundPositionX:jt,backgroundPositionY:jt,...pi,zIndex:di,fillOpacity:Pt,strokeOpacity:Pt,numOctaves:di},fi={...mi,color:Ut,backgroundColor:Ut,outlineColor:Ut,fill:Ut,stroke:Ut,borderColor:Ut,borderTopColor:Ut,borderRightColor:Ut,borderBottomColor:Ut,borderLeftColor:Ut,filter:hi,WebkitFilter:hi},gi=t=>fi[t];function yi(t,e){let n=gi(t);return n!==hi&&(n=_t),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const vi=new Set(["auto","none","0"]);class xi extends An{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),xt(i))){const s=ni(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!si.has(n)||2!==t.length)return;const[i,s]=t,o=ai(i),r=ai(s);if(o!==r)if(mn(o)&&mn(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else yn[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||V(i)))&&n.push(e);var i;n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!vi.has(e)&&Yt(e).values.length&&(i=t[s]),s++}if(i&&n)for(const s of e)t[s]=yi(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=yn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,o=n[s];n[s]=yn[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}}const wi=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function Ti(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&wi.has(e)&&(t[n]=t[n]+"px")}const Pi=C((()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0})),Si=new Set(["opacity","clipPath","filter","transform"]);function bi(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}function Ai(t){return(e,n)=>{const i=bi(e),s=[];for(const e of i){const i=t(e,n);s.push(i)}return()=>{for(const t of s)t()}}}const Ei=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class Mi{constructor(){this.latest={},this.values=new Map}set(t,e,n,i,s=!0){const o=this.values.get(t);o&&o.onRemove();const r=()=>{const i=e.get();this.latest[t]=s?Ei(i,mi[t]):i,n&&lt.render(n)};r();const a=e.on("change",r);i&&e.addDependent(i);const l=()=>{a(),n&&ut(n),this.values.delete(t),i&&e.removeDependent(i)};return this.values.set(t,{value:e,onRemove:l}),l}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}function Vi(t){const e=new WeakMap,n=[];return(i,s)=>{const o=e.get(i)??new Mi;e.set(i,o);for(const e in s){const r=s[e],a=t(i,o,e,r);n.push(a)}return()=>{for(const t of n)t()}}}const Ci=(t,e,n,i)=>{const s=function(t,e){if(!(e in t))return!1;const n=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(t),e)||Object.getOwnPropertyDescriptor(t,e);return n&&"function"==typeof n.set}(t,n),o=s?n:n.startsWith("data")||n.startsWith("aria")?n.replace(/([A-Z])/g,(t=>`-${t.toLowerCase()}`)):n;const r=s?()=>{t[o]=e.latest[n]}:()=>{const i=e.latest[n];null==i?t.removeAttribute(o):t.setAttribute(o,String(i))};return e.set(n,i,r)},Ri=Ai(Vi(Ci)),Di=Vi(((t,e,n,i)=>e.set(n,i,(()=>{t[n]=e.latest[n]}),void 0,!1))),ki={current:void 0};class Li{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=mt.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=mt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new O);const n=this.events[t].add(e);return"change"===t?()=>{n(),lt.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return ki.current&&ki.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=mt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return F(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Oi(t,e){return new Li(t,e)}const ji={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const Bi=new Set(["originX","originY","originZ"]),Fi=(t,e,n,i)=>{let s,o;return pn.has(n)?(e.get("transform")||(zn(t)||e.get("transformBox")||Fi(t,e,"transformBox",new Li("fill-box")),e.set("transform",new Li("none"),(()=>{t.style.transform=function(t){let e="",n=!0;for(let i=0;i<dn.length;i++){const s=dn[i],o=t.latest[s];if(void 0===o)continue;let r=!0;r="number"==typeof o?o===(s.startsWith("scale")?1:0):0===parseFloat(o),r||(n=!1,e+=`${ji[s]||s}(${t.latest[s]}) `)}return n?"none":e.trim()}(e)}))),o=e.get("transform")):Bi.has(n)?(e.get("transformOrigin")||e.set("transformOrigin",new Li(""),(()=>{const n=e.latest.originX??"50%",i=e.latest.originY??"50%",s=e.latest.originZ??0;t.style.transformOrigin=`${n} ${i} ${s}`})),o=e.get("transformOrigin")):s=En(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,i,s,o)},Ii=Ai(Vi(Fi)),Wi=jt.transform;const Ui=Ai(Vi(((t,e,n,i)=>{if(n.startsWith("path"))return function(t,e,n,i){return lt.render((()=>t.setAttribute("pathLength","1"))),"pathOffset"===n?e.set(n,i,(()=>t.setAttribute("stroke-dashoffset",Wi(-e.latest[n])))):(e.get("stroke-dasharray")||e.set("stroke-dasharray",new Li("1 1"),(()=>{const{pathLength:n=1,pathSpacing:i}=e.latest;t.setAttribute("stroke-dasharray",`${Wi(n)} ${Wi(i??1-Number(n))}`)})),e.set(n,i,void 0,e.get("stroke-dasharray")))}(t,e,n,i);if(n.startsWith("attr"))return Ci(t,e,function(t){return t.replace(/^attr([A-Z])/,((t,e)=>e.toLowerCase()))}(n),i);return(n in t.style?Fi:Ci)(t,e,n,i)})));const{schedule:Ni,cancel:$i}=at(queueMicrotask,!1),zi={x:!1,y:!1};function Xi(){return zi.x||zi.y}function Yi(t){return"x"===t||"y"===t?zi[t]?null:(zi[t]=!0,()=>{zi[t]=!1}):zi.x||zi.y?null:(zi.x=zi.y=!0,()=>{zi.x=zi.y=!1})}function Hi(t,e){const n=bi(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Ki(t){return!("touch"===t.pointerType||Xi())}function Gi(t,e,n={}){const[i,s,o]=Hi(t,n),r=t=>{if(!Ki(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{Ki(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach((t=>{t.addEventListener("pointerenter",r,s)})),o}const _i=(t,e)=>!!e&&(t===e||_i(t,e.parentElement)),qi=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Zi=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Ji=new WeakSet;function Qi(t){return e=>{"Enter"===e.key&&t(e)}}function ts(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function es(t){return qi(t)&&!Xi()}function ns(t,e,n={}){const[i,s,o]=Hi(t,n),r=t=>{const i=t.currentTarget;if(!es(t))return;Ji.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Ji.has(i)&&Ji.delete(i),es(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||_i(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),zn(t)&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=Qi((()=>{if(Ji.has(n))return;ts(n,"down");const t=Qi((()=>{ts(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>ts(n,"cancel")),e)}));n.addEventListener("keydown",i,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",i)),e)})(t,s))),e=t,Zi.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),o}function is(t,e){const n=window.getComputedStyle(t);return En(e)?n.getPropertyValue(e):n[e]}function ss(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return lt.preUpdate(i,!0),()=>ut(i)}function os(){const{value:t}=rt;null!==t?(t.frameloop.rate.push(ct.delta),t.animations.mainThread.push(ft.mainThread),t.animations.waapi.push(ft.waapi),t.animations.layout.push(ft.layout)):ut(os)}function rs(t){return t.reduce(((t,e)=>t+e),0)/t.length}function as(t,e=rs){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const ls=t=>Math.round(1e3/t);function us(){rt.value=null,rt.addProjectionMetrics=null}function cs(){const{value:t}=rt;if(!t)throw new Error("Stats are not being measured");us(),ut(os);const e={frameloop:{setup:as(t.frameloop.setup),rate:as(t.frameloop.rate),read:as(t.frameloop.read),resolveKeyframes:as(t.frameloop.resolveKeyframes),preUpdate:as(t.frameloop.preUpdate),update:as(t.frameloop.update),preRender:as(t.frameloop.preRender),render:as(t.frameloop.render),postRender:as(t.frameloop.postRender)},animations:{mainThread:as(t.animations.mainThread),waapi:as(t.animations.waapi),layout:as(t.animations.layout)},layoutProjection:{nodes:as(t.layoutProjection.nodes),calculatedTargetDeltas:as(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:as(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=ls(n.min),n.max=ls(n.max),n.avg=ls(n.avg),[n.min,n.max]=[n.max,n.min],e}function hs(t){return M(t)&&"ownerSVGElement"in t}function ds(t){return hs(t)&&"svg"===t.tagName}function ps(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=Ue(t[1+n],t[2+n],t[3+n]);return e?s(i):s}function ms(t){const e=[];ki.current=e;const n=t();ki.current=void 0;const i=Oi(n);return function(t,e,n){const i=()=>e.set(n()),s=()=>lt.preRender(i,!1,!0),o=t.map((t=>t.on("change",s)));e.on("destroy",(()=>{o.forEach((t=>t())),ut(i)}))}(e,i,t),i}const fs=t=>Boolean(t&&t.getVelocity);function gs(t,e,n){const i=t.get();let s,o=null,r=i;const a="string"==typeof i?i.replace(/[\d.-]/g,""):void 0,l=()=>{o&&(o.stop(),o=null)},u=()=>{l(),o=new Je({keyframes:[vs(t.get()),vs(r)],velocity:t.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:s})};let c;return t.attach(((e,n)=>(r=e,s=t=>n(ys(t,a)),lt.postRender(u),t.get())),l),fs(e)&&(c=e.on("change",(e=>t.set(ys(e,a)))),t.on("destroy",c)),c}function ys(t,e){return e?t+e:t}function vs(t){return"number"==typeof t?t:parseFloat(t)}const xs=[...ri,Ut,_t],ws=t=>xs.find(oi(t));function Ts(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let Ps={},Ss=null;const bs=(t,e)=>{Ps[t]=e},As=()=>{Ss||(Ss=document.createElement("style"),Ss.id="motion-view");let t="";for(const e in Ps){const n=Ps[e];t+=`${e} {\n`;for(const[e,i]of Object.entries(n))t+=`  ${e}: ${i};\n`;t+="}\n"}Ss.textContent=t,document.head.appendChild(Ss),Ps={}},Es=()=>{Ss&&Ss.parentElement&&Ss.parentElement.removeChild(Ss)};function Ms(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function Vs(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}const Cs=["layout","enter","exit","new","old"];function Rs(t){const{update:e,targets:n,options:i}=t;if(!document.startViewTransition)return new Promise((async t=>{await e(),t(new Gn([]))}));(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",n)||bs(":root",{"view-transition-name":"none"}),bs("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),As();const s=document.startViewTransition((async()=>{await e()}));return s.finished.finally((()=>{Es()})),new Promise((t=>{s.ready.then((()=>{const e=document.getAnimations().filter(Vs),s=[];n.forEach(((t,e)=>{for(const n of Cs){if(!t[n])continue;const{keyframes:o,options:r}=t[n];for(let[t,a]of Object.entries(o)){if(!a)continue;const o={...ii(i,t),...ii(r,t)},l=Ts(n);if("opacity"===t&&!Array.isArray(a)){a=["new"===l?0:1,a]}"function"==typeof o.delay&&(o.delay=o.delay(0,1)),o.duration&&(o.duration=j(o.duration)),o.delay&&(o.delay=j(o.delay));const u=new In({...o,element:document.documentElement,name:t,pseudoElement:`::view-transition-${l}(${e})`,keyframes:a});s.push(u)}}}));for(const t of e){if("finished"===t.playState)continue;const{effect:e}=t;if(!(e&&e instanceof KeyframeEffect))continue;const{pseudoElement:o}=e;if(!o)continue;const r=Ms(o);if(!r)continue;const a=n.get(r.layer);if(a)Ds(a,"enter")&&Ds(a,"exit")&&e.getKeyframes().some((t=>t.mixBlendMode))?s.push(new qn(t)):t.cancel();else{const n="group"===r.type?"layout":"";let o={...ii(i,n)};o.duration&&(o.duration=j(o.duration)),o=Fn(o);const a=On(o.ease,o.duration);e.updateTiming({delay:j(o.delay??0),duration:o.duration,easing:a}),s.push(new qn(t))}}t(new Gn(s))}))}))}function Ds(t,e){return t?.[e]?.keyframes.opacity}let ks=[],Ls=null;function Os(){Ls=null;const[t]=ks;var e;t&&(w(ks,e=t),Ls=e,Rs(e).then((t=>{e.notifyReady(t),t.finished.finally(Os)})))}function js(){for(let t=ks.length-1;t>=0;t--){const e=ks[t],{interrupt:n}=e.options;if("immediate"===n){const n=ks.slice(0,t+1).map((t=>t.update)),i=ks.slice(t+1);e.update=()=>{n.forEach((t=>t()))},ks=[e,...i];break}}Ls&&"immediate"!==ks[0]?.options.interrupt||Os()}class Bs{constructor(t,e={}){var n;this.currentTarget="root",this.targets=new Map,this.notifyReady=R,this.readyPromise=new Promise((t=>{this.notifyReady=t})),this.update=t,this.options={interrupt:"wait",...e},n=this,ks.push(n),Ni.render(js)}get(t){return this.currentTarget=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentTarget:i,targets:s}=this;s.has(i)||s.set(i,{});s.get(i)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const Fs=lt,Is=ot.reduce(((t,e)=>(t[e]=t=>ut(t),t)),{}),Ws=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class Us extends i.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,n=zn(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Ns({children:t,isPresent:n,anchorX:s}){const o=e.useId(),r=e.useRef(null),a=e.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=e.useContext(Ws);return e.useInsertionEffect((()=>{const{width:t,height:e,top:i,left:u,right:c}=a.current;if(n||!r.current||!t||!e)return;const h="left"===s?`left: ${u}`:`right: ${c}`;r.current.dataset.motionPopId=o;const d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`\n          [data-motion-pop-id="${o}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${h}px !important;\n            top: ${i}px !important;\n          }\n        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}}),[n]),d(Us,{isPresent:n,childRef:r,sizeRef:a,children:i.cloneElement(t,{ref:r})})}const $s=({children:t,initial:n,isPresent:s,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:l,anchorX:u})=>{const c=f(zs),h=e.useId();let p=!0,m=e.useMemo((()=>(p=!1,{id:h,initial:n,isPresent:s,custom:r,onExitComplete:t=>{c.set(t,!0);for(const t of c.values())if(!t)return;o&&o()},register:t=>(c.set(t,!1),()=>c.delete(t))})),[s,c,o]);return a&&p&&(m={...m}),e.useMemo((()=>{c.forEach(((t,e)=>c.set(e,!1)))}),[s]),i.useEffect((()=>{!s&&!c.size&&o&&o()}),[s]),"popLayout"===l&&(t=d(Ns,{isPresent:s,anchorX:u,children:t})),d(v.Provider,{value:m,children:t})};function zs(){return new Map}function Xs(t=!0){const n=e.useContext(v);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,r=e.useId();e.useEffect((()=>{if(t)return o(r)}),[t]);const a=e.useCallback((()=>t&&s&&s(r)),[r,s,t]);return!i&&s?[!1,a]:[!0]}const Ys=t=>t.key||"";function Hs(t){const n=[];return e.Children.forEach(t,(t=>{e.isValidElement(t)&&n.push(t)})),n}const Ks=e.createContext(null);function Gs(t){return t.max-t.min}function _s(t,e,n,i=.5){t.origin=i,t.originPoint=Qt(e.min,e.max,t.origin),t.scale=Gs(n)/Gs(e),t.translate=Qt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function qs(t,e,n,i){_s(t.x,e.x,n.x,i?i.originX:void 0),_s(t.y,e.y,n.y,i?i.originY:void 0)}function Zs(t,e,n){t.min=n.min+e.min,t.max=t.min+Gs(e)}function Js(t,e,n){t.min=e.min-n.min,t.max=t.min+Gs(e)}function Qs(t,e,n){Js(t.x,e.x,n.x),Js(t.y,e.y,n.y)}const to=t=>!t.isLayoutDirty&&t.willUpdate(!1);function eo(){const t=new Set,e=new WeakMap,n=()=>t.forEach(to);return{add:i=>{t.add(i),e.set(i,i.addEventListener("willUpdate",n))},remove:i=>{t.delete(i);const s=e.get(i);s&&(s(),e.delete(i)),n()},dirty:n}}const no=t=>null!==t;const io={type:"spring",stiffness:500,damping:25,restSpeed:10},so={type:"keyframes",duration:.8},oo={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ro=(t,{keyframes:e})=>e.length>2?so:pn.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:io:oo;const ao=(t,e,n,i={},s,o)=>r=>{const a=ii(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=j(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||Object.assign(c,ro(t,c)),c.duration&&(c.duration=j(c.duration)),c.repeatDelay&&(c.repeatDelay=j(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(h=!0)),(A.instantAnimations||A.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(no),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}(c.keyframes,a);if(void 0!==t)return void lt.update((()=>{c.onUpdate(t),c.onComplete()}))}return a.isSync?new Je(c):new Kn(c)};function lo(t,e,n){const i=fs(t)?t:Oi(t);return i.start(ao("",i,e,n)),i.animation}const uo=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),co="framerAppearId",ho="data-"+uo(co);function po(t){return t.props[ho]}const mo=(t,e)=>t.depth-e.depth;class fo{constructor(){this.children=[],this.isDirty=!1}add(t){x(this.children,t),this.isDirty=!0}remove(t){w(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(mo),this.isDirty=!1,this.children.forEach(t)}}function go(t,e){const n=mt.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(ut(i),t(o-e))};return lt.setup(i,!0),()=>ut(i)}function yo(t){return fs(t)?t.get():t}const vo=["TopLeft","TopRight","BottomLeft","BottomRight"],xo=vo.length,wo=t=>"string"==typeof t?parseFloat(t):t,To=t=>"number"==typeof t||jt.test(t);function Po(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const So=Ao(0,.5,_),bo=Ao(.5,.95,R);function Ao(t,e,n){return i=>i<t?0:i>e?1:n(L(t,e,i))}function Eo(t,e){t.min=e.min,t.max=e.max}function Mo(t,e){Eo(t.x,e.x),Eo(t.y,e.y)}function Vo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Co(t){return void 0===t||1===t}function Ro({scale:t,scaleX:e,scaleY:n}){return!Co(t)||!Co(e)||!Co(n)}function Do(t){return Ro(t)||ko(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function ko(t){return Lo(t.x)||Lo(t.y)}function Lo(t){return t&&"0%"!==t}function Oo(t,e,n){return n+e*(t-n)}function jo(t,e,n,i,s){return void 0!==s&&(t=Oo(t,s,i)),Oo(t,n,i)+e}function Bo(t,e=0,n=1,i,s){t.min=jo(t.min,e,n,i,s),t.max=jo(t.max,e,n,i,s)}function Fo(t,{x:e,y:n}){Bo(t.x,e.translate,e.scale,e.originPoint),Bo(t.y,n.translate,n.scale,n.originPoint)}const Io=.999999999999,Wo=1.0000000000001;function Uo(t,e){t.min=t.min+e,t.max=t.max+e}function No(t,e,n,i,s=.5){Bo(t,e,n,Qt(t.min,t.max,s),i)}function $o(t,e){No(t.x,e.x,e.scaleX,e.scale,e.originX),No(t.y,e.y,e.scaleY,e.scale,e.originY)}function zo(t,e,n,i,s){return t=Oo(t-=e,1/n,i),void 0!==s&&(t=Oo(t,1/s,i)),t}function Xo(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){Ot.test(e)&&(e=parseFloat(e),e=Qt(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Qt(o.min,o.max,i);t===o&&(a-=e),t.min=zo(t.min,e,n,a,s),t.max=zo(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Yo=["x","scaleX","originX"],Ho=["y","scaleY","originY"];function Ko(t,e,n,i){Xo(t.x,e,Yo,n?n.x:void 0,i?i.x:void 0),Xo(t.y,e,Ho,n?n.y:void 0,i?i.y:void 0)}const Go=()=>({x:{min:0,max:0},y:{min:0,max:0}});function _o(t){return 0===t.translate&&1===t.scale}function qo(t){return _o(t.x)&&_o(t.y)}function Zo(t,e){return t.min===e.min&&t.max===e.max}function Jo(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Qo(t,e){return Jo(t.x,e.x)&&Jo(t.y,e.y)}function tr(t){return Gs(t.x)/Gs(t.y)}function er(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nr{constructor(){this.members=[]}add(t){x(this.members,t),t.scheduleRender()}remove(t){if(w(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const ir={};function sr(t){for(const e in t)ir[e]=t[e],yt(e)&&(ir[e].isCSSVariable=!0)}function or(t){return[t("x"),t("y")]}const rr={hasAnimatedSinceResize:!0,hasEverUpdated:!1},ar={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},lr=["","X","Y","Z"],ur={visibility:"hidden"};let cr=0;function hr(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function dr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=po(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",lt,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&dr(i)}function pr({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=e?.()){this.id=cr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rt.value&&(ar.nodes=ar.calculatedTargetDeltas=ar.calculatedProjections=0),this.nodes.forEach(gr),this.nodes.forEach(Sr),this.nodes.forEach(br),this.nodes.forEach(yr),rt.addProjectionMetrics&&rt.addProjectionMetrics(ar)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new fo)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new O),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=hs(e)&&!ds(e),this.instance=e;const{layoutId:n,layout:i,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||n)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=go(i,250),rr.hasAnimatedSinceResize&&(rr.hasAnimatedSinceResize=!1,this.nodes.forEach(Pr))}))}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||i)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const o=this.options.transition||s.getDefaultTransition()||Rr,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!Qo(this.targetLayout,i),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e={...ii(o,"layout"),onPlay:r,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||Pr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ut(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Ar),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&dr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(xr);this.isUpdating||this.nodes.forEach(wr),this.isUpdating=!1,this.nodes.forEach(Tr),this.nodes.forEach(mr),this.nodes.forEach(fr),this.clearAllSnapshots();const t=mt.now();ct.delta=P(0,1e3/60,t-ct.timestamp),ct.timestamp=t,ct.isProcessing=!0,ht.update.process(ct),ht.preRender.process(ct),ht.render.process(ct),ct.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ni.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(vr),this.sharedNodes.forEach(Er)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,lt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){lt.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Gs(this.snapshot.measuredBox.x)||Gs(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!qo(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||Do(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Lr((i=n).x),Lr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(jr))){const{scroll:t}=this.root;t&&(Uo(e.x,t.offset.x),Uo(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(Mo(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Mo(e,t),Uo(e.x,s.offset.x),Uo(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Mo(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&$o(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),Do(i.latestValues)&&$o(n,i.latestValues)}return Do(this.latestValues)&&$o(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Mo(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!Do(n.latestValues))continue;Ro(n.latestValues)&&n.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};Mo(i,n.measurePageBox()),Ko(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return Do(this.latestValues)&&Ko(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ct.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const n=Boolean(this.resumingFrom)||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:s}=this.options;if(this.layout&&(i||s)){if(this.resolvedRelativeTargetAt=ct.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Qs(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Mo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var o,r,a;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,Zs(o.x,r.x,a.x),Zs(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Mo(this.target,this.layout.layoutBox),Fo(this.target,this.targetDelta)):Mo(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Qs(this.relativeTargetOrigin,this.target,t.target),Mo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rt.value&&ar.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!Ro(this.parent.latestValues)&&!ko(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===ct.timestamp&&(n=!1),n)return;const{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!s)return;Mo(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&$o(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Fo(t,r)),i&&Do(o.latestValues)&&$o(t,o.latestValues))}e.x<Wo&&e.x>Io&&(e.x=1),e.y<Wo&&e.y>Io&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(Vo(this.prevProjectionDelta.x,this.projectionDelta.x),Vo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),qs(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&er(this.projectionDelta.x,this.prevProjectionDelta.x)&&er(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),rt.value&&ar.calculatedProjections++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(Cr));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d;Mr(o.x,t.x,n),Mr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Qs(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),function(t,e,n,i){Vr(t.x,e.x,n.x,i),Vr(t.y,e.y,n.y,i)}(this.relativeTarget,this.relativeTargetOrigin,r,n),h&&(l=this.relativeTarget,d=h,Zo(l.x,d.x)&&Zo(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),Mo(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Qt(0,n.opacity??1,So(i)),t.opacityExit=Qt(e.opacity??1,0,bo(i))):o&&(t.opacity=Qt(e.opacity??1,n.opacity??1,i));for(let s=0;s<xo;s++){const o=`border${vo[s]}Radius`;let r=Po(e,o),a=Po(n,o);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||To(r)===To(a)?(t[o]=Math.max(Qt(wo(r),wo(a),i),0),(Ot.test(a)||Ot.test(r))&&(t[o]+="%")):t[o]=a)}(e.rotate||n.rotate)&&(t.rotate=Qt(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ut(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=lt.update((()=>{rr.hasAnimatedSinceResize=!0,ft.layout++,this.motionValue||(this.motionValue=Oi(0)),this.currentAnimation=lo(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{ft.layout--},onComplete:()=>{ft.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Or(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Gs(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Gs(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Mo(e,n),$o(e,s),qs(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nr);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&hr("z",t,i,this.animationValues);for(let e=0;e<lr.length;e++)hr(`rotate${lr[e]}`,t,i,this.animationValues),hr(`skew${lr[e]}`,t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ur;const e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=yo(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=yo(t?.pointerEvents)||""),this.hasProjected&&!Do(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}const s=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),n&&(e.transform=n(s,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,i.animationValues?e.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const t in ir){if(void 0===s[t])continue;const{correct:n,applyTo:o,isCSSVariable:r}=ir[t],a="none"===e.transform?s[t]:n(s[t],i);if(o){const t=o.length;for(let n=0;n<t;n++)e[o[n]]=a}else r?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=i===this?yo(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>t.currentAnimation?.stop())),this.root.nodes.forEach(xr),this.root.sharedNodes.clear()}}}function mr(t){t.updateLayout()}function fr(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:i}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?or((t=>{const i=o?e.measuredBox[t]:e.layoutBox[t],s=Gs(i);i.min=n[t].min,i.max=i.min+s})):Or(s,e.layoutBox,n)&&or((i=>{const s=o?e.measuredBox[i]:e.layoutBox[i],r=Gs(n[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};qs(r,n,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?qs(a,t.applyTransform(i,!0),e.measuredBox):qs(a,n,e.layoutBox);const l=!qo(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Qs(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Qs(a,n,o.layoutBox),Qo(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function gr(t){rt.value&&ar.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function yr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function vr(t){t.clearSnapshot()}function xr(t){t.clearMeasurements()}function wr(t){t.isLayoutDirty=!1}function Tr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Pr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Sr(t){t.resolveTargetDelta()}function br(t){t.calcProjection()}function Ar(t){t.resetSkewAndRotation()}function Er(t){t.removeLeadSnapshot()}function Mr(t,e,n){t.translate=Qt(e.translate,0,n),t.scale=Qt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Vr(t,e,n,i){t.min=Qt(e.min,n.min,i),t.max=Qt(e.max,n.max,i)}function Cr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Rr={duration:.45,ease:[.4,0,.1,1]},Dr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),kr=Dr("applewebkit/")&&!Dr("chrome/")?Math.round:R;function Lr(t){t.min=kr(t.min),t.max=kr(t.max)}function Or(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=tr(e),s=tr(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function jr(t){return t!==t.root&&t.scroll?.wasRoot}function Br(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}const Fr=pr({attachResizeListener:(t,e)=>Br(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ir={current:void 0},Wr=pr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Ir.current){const t=new Fr({});t.mount(window),t.setOptions({layoutScroll:!0}),Ir.current=t}return Ir.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function Ur(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Nr={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!jt.test(t))return t;t=parseFloat(t)}return`${Ur(t,e.target.x)}% ${Ur(t,e.target.y)}%`}},$r={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=_t.parse(t);if(s.length>5)return i;const o=_t.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=Qt(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};function zr({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Xr(t,e){return zr(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const Yr={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Hr={};for(const t in Yr)Hr[t]={isEnabled:e=>Yr[t].some((t=>!!e[t]))};const Kr={current:null},Gr={current:!1};function _r(){if(Gr.current=!0,g)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Kr.current=t.matches;t.addListener(e),e()}else Kr.current=!1}const qr=new WeakMap;function Zr(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function Jr(t){return"string"==typeof t||Array.isArray(t)}const Qr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ta=["initial",...Qr];function ea(t){return Zr(t.animate)||ta.some((e=>Jr(t[e])))}function na(t){return Boolean(ea(t)||t.variants)}function ia(t){const e=[{},{}];return t?.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function sa(t,e,n,i){if("function"==typeof e){const[s,o]=ia(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=ia(i);e=e(void 0!==n?n:t.custom,s,o)}return e}const oa=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ra{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=An,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=mt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,lt.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=ea(e),this.isVariantNode=na(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in c){const e=c[t];void 0!==a[t]&&fs(e)&&e.set(a[t],!1)}}mount(t){this.current=t,qr.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),Gr.current||_r(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Kr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),ut(this.notifyUpdate),ut(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=pn.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&lt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{i(),s(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in Hr){const e=Hr[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<oa.length;e++){const n=oa[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(fs(s))t.addValue(i,s);else if(fs(o))t.addValue(i,Oi(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,Oi(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Oi(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(E(n)||V(n))?n=parseFloat(n):!ws(n)&&_t.test(e)&&(n=yi(t,e)),this.setBaseTarget(t,fs(n)?n.get():n)),fs(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const i=sa(this.props,e,this.presenceContext?.custom);i&&(n=i[t])}if(e&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,t);return void 0===i||fs(i)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new O),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class aa extends ra{constructor(){super(...arguments),this.KeyframeResolver=xi}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;fs(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}const la={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ua=dn.length;function ca(t,e,n){let i="",s=!0;for(let o=0;o<ua;o++){const r=dn[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=Ei(a,mi[r]);if(!l){s=!1;i+=`${la[r]||r}(${t}) `}n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}function ha(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const n=e[t];if(pn.has(t))r=!0;else if(yt(t))s[t]=n;else{const e=Ei(n,mi[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(e.transform||(r||n?i.transform=ca(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}function da(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const e in n)t.style.setProperty(e,n[e])}function pa(t,{layout:e,layoutId:n}){return pn.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!ir[t]||"opacity"===t)}function ma(t,e,n){const{style:i}=t,s={};for(const o in i)(fs(i[o])||e.style&&fs(e.style[o])||pa(o,t)||void 0!==n?.getValue(o)?.liveStyle)&&(s[o]=i[o]);return s}class fa extends aa{constructor(){super(...arguments),this.type="html",this.renderInstance=da}readValueFromInstance(t,e){if(pn.has(e))return this.projection?.isProjecting?ln(e):cn(t,e);{const i=(n=t,window.getComputedStyle(n)),s=(yt(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return Xr(t,e)}build(t,e,n){ha(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return ma(t,e,n)}}function ga(){const t=function(){const t=e.useRef(!1);return y((()=>(t.current=!0,()=>{t.current=!1})),[]),t}(),[n,i]=e.useState(0),s=e.useCallback((()=>{t.current&&i(n+1)}),[n]);return[e.useCallback((()=>lt.postRender(s)),[s]),n]}const ya=t=>!0===t,va=({children:t,id:n,inherit:i=!0})=>{const s=e.useContext(m),o=e.useContext(Ks),[r,a]=ga(),l=e.useRef(null),u=s.id||o;null===l.current&&((t=>ya(!0===t)||"id"===t)(i)&&u&&(n=n?u+"-"+n:u),l.current={id:n,group:ya(i)&&s.group||eo()});const c=e.useMemo((()=>({...l.current,forceRender:r})),[a]);return d(m.Provider,{value:c,children:t})},xa=e.createContext({strict:!1});function wa(t){for(const e in t)Hr[e]={...Hr[e],...t[e]}}function Ta(t){return"function"==typeof t}const Pa=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Sa(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Pa.has(t)}let ba=t=>!Sa(t);function Aa(t){t&&(ba=e=>e.startsWith("on")?!Sa(e):t(e))}try{Aa(require("@emotion/is-prop-valid").default)}catch{}function Ea(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(ba(s)||!0===n&&Sa(s)||!e&&!Sa(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}const Ma=e.createContext(null);function Va(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}function Ca(t,e,n){const i=t.getProps();return sa(i,e,void 0!==n?n:i.custom,t)}const Ra=t=>Array.isArray(t);function Da(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Oi(n))}function ka(t,e){const n=Ca(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o){Da(t,e,(r=o[e],Ra(r)?r[r.length-1]||0:r))}var r}function La(t,e){const n=t.getValue("willChange");if(i=n,Boolean(fs(i)&&i.add))return n.add(e);if(!n&&A.WillChange){const n=new A.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}function Oa({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function ja(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const i=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||u&&Oa(u,e))continue;const r={delay:n,...ii(o||{},e)},c=i.get();if(void 0!==c&&!i.isAnimating&&!Array.isArray(s)&&s===c&&!r.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const n=po(t);if(n){const t=window.MotionHandoffAnimation(n,e,lt);null!==t&&(r.startTime=t,h=!0)}}La(t,e),i.start(ao(e,i,s,t.shouldReduceMotion&&si.has(e)?{type:!1}:r,t,h));const d=i.animation;d&&l.push(d)}return r&&Promise.all(l).then((()=>{lt.update((()=>{r&&ka(t,r)}))})),l}function Ba(t,e,n={}){const i=Ca(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(ja(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(Fa).forEach(((t,i)=>{t.notify("AnimationStart",e),r.push(Ba(t,e,{...o,delay:n+l(i)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,o+i,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then((()=>e()))}return Promise.all([o(),r(n.delay)])}function Fa(t,e){return t.sortNodePosition(e)}function Ia(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map((e=>Ba(t,e,n)));i=Promise.all(s)}else if("string"==typeof e)i=Ba(t,e,n);else{const s="function"==typeof e?Ca(t,e,n.custom):e;i=Promise.all(ja(t,s,n))}return i.then((()=>{t.notify("AnimationComplete",e)}))}function Wa(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const Ua=ta.length;function Na(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Na(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Ua;n++){const i=ta[n],s=t.props[i];(Jr(s)||!1===s)&&(e[i]=s)}return e}const $a=[...Qr].reverse(),za=Qr.length;function Xa(t){let e=function(t){return e=>Promise.all(e.map((({animation:e,options:n})=>Ia(t,e,n))))}(t),n=Ka(),i=!0;const s=e=>(n,i)=>{const s=Ca(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...i}=s;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=Na(t.parent)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<za;e++){const d=$a[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=Jr(m),g=d===o?p.isActive:null;!1===g&&(h=e);let y=m===a[d]&&m!==r[d]&&f;if(y&&i&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...c},!p.isActive&&null===g||!m&&!p.prevProp||Zr(m)||"boolean"==typeof m)continue;const v=Ya(p.prevProp,m);let x=v||d===o&&p.isActive&&!y&&f||e>h&&f,w=!1;const T=Array.isArray(m)?m:[m];let P=T.reduce(s(d),{});!1===g&&(P={});const{prevResolvedValues:S={}}=p,b={...S,...P},A=e=>{x=!0,u.has(e)&&(w=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=P[t],n=S[t];if(c.hasOwnProperty(t))continue;let i=!1;i=Ra(e)&&Ra(n)?!Wa(e,n):e!==n,i?null!=e?A(t):u.add(t):void 0!==e&&u.has(t)?A(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=P,p.isActive&&(c={...c,...P}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(y&&v)||w)&&l.push(...T.map((t=>({animation:t,options:{type:d}}))))}if(u.size){const e={};if("boolean"!=typeof r.initial){const n=Ca(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach((n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null})),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){if(n[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach((t=>t.animationState?.setActive(e,i))),n[e].isActive=i;const s=o(e);for(const t in n)n[t].protectedKeys={};return s},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Ka(),i=!0}}}function Ya(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Wa(e,t)}function Ha(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ka(){return{animate:Ha(!0),whileInView:Ha(),whileHover:Ha(),whileTap:Ha(),whileDrag:Ha(),whileFocus:Ha(),exit:Ha()}}class Ga{constructor(t){this.isMounted=!1,this.node=t}update(){}}let _a=0;const qa={animation:{Feature:class extends Ga{constructor(t){super(t),t.animationState||(t.animationState=Xa(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Zr(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Ga{constructor(){super(...arguments),this.id=_a++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then((()=>{e(this.id)}))}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Za(t){return{point:{x:t.pageX,y:t.pageY}}}const Ja=t=>e=>qi(e)&&t(e,Za(e));function Qa(t,e,n,i){return Br(t,e,Ja(n),i)}const tl=({current:t})=>t?t.ownerDocument.defaultView:null;function el(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}const nl=(t,e)=>Math.abs(t-e);function il(t,e){const n=nl(t.x,e.x),i=nl(t.y,e.y);return Math.sqrt(n**2+i**2)}class sl{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=al(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=il(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=ct;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ol(e,this.transformPagePoint),lt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=al("pointercancel"===t.type?this.lastMoveEventInfo:ol(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!qi(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=ol(Za(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=ct;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,al(o,this.history)),this.removeListeners=k(Qa(this.contextWindow,"pointermove",this.handlePointerMove),Qa(this.contextWindow,"pointerup",this.handlePointerUp),Qa(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),ut(this.updatePoint)}}function ol(t,e){return e?{point:e(t.point)}:t}function rl(t,e){return{x:t.x-e.x,y:t.y-e.y}}function al({point:t},e){return{point:t,delta:rl(t,ul(e)),offset:rl(t,ll(e)),velocity:cl(e,.1)}}function ll(t){return t[0]}function ul(t){return t[t.length-1]}function cl(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=ul(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>j(e)));)n--;if(!i)return{x:0,y:0};const o=B(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function hl(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function dl(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const pl=.35;function ml(t,e,n){return{min:fl(t,e),max:fl(t,n)}}function fl(t,e){return"number"==typeof t?t:t[e]||0}const gl=new WeakMap;class yl{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new sl(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Za(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Yi(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),or((t=>{let e=this.getAxisMotionValue(t).get()||0;if(Ot.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Gs(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),s&&lt.postRender((()=>s(t,e))),La(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>or((t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:tl(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&lt.postRender((()=>s(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!vl(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Qt(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Qt(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&el(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!n)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:hl(t.x,n,s),y:hl(t.y,e,i)}}(n.layoutBox,t),this.elastic=function(t=pl){return!1===t?t=0:!0===t&&(t=pl),{x:ml(t,"left","right"),y:ml(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&or((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!el(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=Xr(t,n),{scroll:s}=e;return s&&(Uo(i.x,s.offset.x),Uo(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:dl(t.x,e.x),y:dl(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=zr(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=or((r=>{if(!vl(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)}));return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return La(this.visualElement,t),n.start(ao(t,n,0,e,this.visualElement,!1))}stopAnimation(){or((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){or((t=>this.getAxisMotionValue(t).animation?.pause()))}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){or((e=>{const{drag:n}=this.getProps();if(!vl(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Qt(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!el(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};or((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Gs(t),s=Gs(e);return s>i?n=L(e.min,e.max-i,t.min):i>s&&(n=L(t.min,t.max-s,e.min)),P(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),or((e=>{if(!vl(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Qt(s,o,i[e]))}))}addListeners(){if(!this.visualElement.current)return;gl.set(this.visualElement,this);const t=Qa(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();el(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),lt.read(e);const s=Br(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(or((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=pl,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function vl(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const xl=t=>(e,n)=>{t&&lt.postRender((()=>t(e,n)))};const wl=e.createContext({});class Tl extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;sr(Sl),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",(()=>{this.safeToRemove()})),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),rr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||lt.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Ni.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Pl(t){const[n,i]=Xs(),s=e.useContext(m);return d(Tl,{...t,layoutGroup:s,switchLayoutGroup:e.useContext(wl),isPresent:n,safeToRemove:i})}const Sl={borderRadius:{...Nr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Nr,borderTopRightRadius:Nr,borderBottomLeftRadius:Nr,borderBottomRightRadius:Nr,boxShadow:$r},bl={pan:{Feature:class extends Ga{constructor(){super(...arguments),this.removePointerDownListener=R}onPointerDown(t){this.session=new sl(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:tl(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:xl(t),onStart:xl(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&lt.postRender((()=>i(t,e)))}}}mount(){this.removePointerDownListener=Qa(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Ga{constructor(t){super(t),this.removeGroupControls=R,this.removeListeners=R,this.controls=new yl(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||R}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Wr,MeasureLayout:Pl}};function Al(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&lt.postRender((()=>s(e,Za(e))))}function El(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&lt.postRender((()=>s(e,Za(e))))}const Ml=new WeakMap,Vl=new WeakMap,Cl=t=>{const e=Ml.get(t.target);e&&e(t)},Rl=t=>{t.forEach(Cl)};function Dl(t,e,n){const i=function({root:t,...e}){const n=t||document;Vl.has(n)||Vl.set(n,{});const i=Vl.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Rl,{root:t,...e})),i[s]}(e);return Ml.set(t,n),i.observe(t),()=>{Ml.delete(t),i.unobserve(t)}}const kl={some:0,all:1};const Ll={inView:{Feature:class extends Ga{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:kl[i]};return Dl(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Ga{mount(){const{current:t}=this.node;t&&(this.unmount=ns(t,((t,e)=>(El(this.node,e,"Start"),(t,{success:e})=>El(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Ga{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=k(Br(this.node.current,"focus",(()=>this.onFocus())),Br(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Ga{mount(){const{current:t}=this.node;t&&(this.unmount=Gi(t,((t,e)=>(Al(this.node,e,"Start"),t=>Al(this.node,t,"End")))))}unmount(){}}}},Ol={layout:{ProjectionNode:Wr,MeasureLayout:Pl}},jl=e.createContext({});function Bl(t){const{initial:n,animate:i}=function(t,e){if(ea(t)){const{initial:e,animate:n}=t;return{initial:!1===e||Jr(e)?e:void 0,animate:Jr(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(jl));return e.useMemo((()=>({initial:n,animate:i})),[Fl(n),Fl(i)])}function Fl(t){return Array.isArray(t)?t.join(" "):t}const Il=Symbol.for("motionComponentSymbol");function Wl(t,n,i){return e.useCallback((e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):el(i)&&(i.current=e))}),[n])}function Ul(t,n,i,s,o){const{visualElement:r}=e.useContext(jl),a=e.useContext(xa),l=e.useContext(v),u=e.useContext(Ws).reducedMotion,c=e.useRef(null);s=s||a.renderer,!c.current&&s&&(c.current=s(t,{visualState:n,parent:r,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));const h=c.current,d=e.useContext(wl);!h||h.projection||!o||"html"!==h.type&&"svg"!==h.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Nl(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&el(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:c,layoutScroll:l,layoutRoot:u})}(c.current,i,o,d);const p=e.useRef(!1);e.useInsertionEffect((()=>{h&&p.current&&h.update(i,l)}));const m=i[ho],f=e.useRef(Boolean(m)&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return y((()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),Ni.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())})),e.useEffect((()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask((()=>{window.MotionHandoffMarkAsComplete?.(m)})),f.current=!1))})),h}function Nl(t){if(t)return!1!==t.options.allowProjection?t.projection:Nl(t.parent)}function $l({preloadedFeatures:t,createVisualElement:n,useRender:i,useVisualState:s,Component:o}){function r(t,r){let a;const l={...e.useContext(Ws),...t,layoutId:zl(t)},{isStatic:u}=l,c=Bl(t),h=s(t,u);if(!u&&g){e.useContext(xa).strict;const t=function(t){const{drag:e,layout:n}=Hr;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(l);a=t.MeasureLayout,c.visualElement=Ul(o,h,l,n,t.ProjectionNode)}return p(jl.Provider,{value:c,children:[a&&c.visualElement?d(a,{visualElement:c.visualElement,...l}):null,i(o,t,Wl(h,c.visualElement,r),h,u,c.visualElement)]})}t&&wa(t),r.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const a=e.forwardRef(r);return a[Il]=o,a}function zl({layoutId:t}){const n=e.useContext(m).id;return n&&void 0!==t?n+"-"+t:t}const Xl=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Yl(t,e,n){for(const i in e)fs(e[i])||pa(i,n)||(t[i]=e[i])}function Hl(t,n){const i={};return Yl(i,t.style||{},t),Object.assign(i,function({transformTemplate:t},n){return e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return ha(e,n,t),Object.assign({},e.vars,e.style)}),[n])}(t,n)),i}function Kl(t,e){const n={},i=Hl(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Gl={offset:"stroke-dashoffset",array:"stroke-dasharray"},_l={offset:"strokeDashoffset",array:"strokeDasharray"};function ql(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,u,c){if(ha(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==n&&(h.y=n),void 0!==i&&(h.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Gl:_l;t[o.offset]=jt.transform(-i);const r=jt.transform(e),a=jt.transform(n);t[o.array]=`${r} ${a}`}(h,s,o,r,!1)}const Zl=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Jl=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Ql(t,n,i,s){const o=e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return ql(e,n,Jl(s),t.transformTemplate,t.style),{...e.attrs,style:{...e.style}}}),[n]);if(t.style){const e={};Yl(e,t.style,t),o.style={...e,...o.style}}return o}const tu=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eu(t){return"string"==typeof t&&!t.includes("-")&&!!(tu.indexOf(t)>-1||/[A-Z]/u.test(t))}function nu(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(eu(n)?Ql:Kl)(i,o,r,n),l=Ea(i,"string"==typeof n,t),u=n!==e.Fragment?{...l,...a,ref:s}:{},{children:c}=i,h=e.useMemo((()=>fs(c)?c.get():c),[c]);return e.createElement(n,{...u,children:h})}}const iu=t=>(n,i)=>{const s=e.useContext(jl),o=e.useContext(v),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:su(n,i,s,t),renderState:e()}}(t,n,s,o);return i?r():f(r)};function su(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=yo(o[t]);let{initial:r,animate:a}=t;const l=ea(t),u=na(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!Zr(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const i=sa(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}const ou={useVisualState:iu({scrapeMotionValuesFromProps:ma,createRenderState:Xl})};function ru(t,e,n){const i=ma(t,e,n);for(const n in t)if(fs(t[n])||fs(e[n])){i[-1!==dn.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}const au={useVisualState:iu({scrapeMotionValuesFromProps:ru,createRenderState:Zl})};function lu(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return $l({...eu(n)?au:ou,preloadedFeatures:t,useRender:nu(i),createVisualElement:e,Component:n})}}const uu=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class cu extends aa{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Go}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(pn.has(e)){const t=gi(e);return t&&t.default||0}return e=uu.has(e)?e:uo(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return ru(t,e,n)}build(t,e,n){ql(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){da(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(uu.has(n)?n:uo(n),e.attrs[n])}(t,e,0,i)}mount(t){this.isSVGTag=Jl(t.tagName),super.mount(t)}}const hu=(t,n)=>eu(t)?new cu(n):new fa(n,{allowProjection:t!==e.Fragment}),du=Va(lu({...qa,...Ll,...bl,...Ol},hu));function pu({children:t,as:n="ul",axis:i="y",onReorder:s,values:o,...r},a){const l=f((()=>du[n])),u=[],c=e.useRef(!1),h={axis:i,registerItem:(t,e)=>{const n=u.findIndex((e=>t===e.value));-1!==n?u[n].layout=e[i]:u.push({value:t,layout:e[i]}),u.sort(gu)},updateOrder:(t,e,n)=>{if(c.current)return;const i=function(t,e,n,i){if(!i)return t;const s=t.findIndex((t=>t.value===e));if(-1===s)return t;const o=i>0?1:-1,r=t[s+o];if(!r)return t;const a=t[s],l=r.layout,u=Qt(l.min,l.max,.5);return 1===o&&a.layout.max+n>u||-1===o&&a.layout.min+n<u?T(t,s,s+o):t}(u,t,e,n);u!==i&&(c.current=!0,s(i.map(fu).filter((t=>-1!==o.indexOf(t)))))}};return e.useEffect((()=>{c.current=!1})),d(l,{...r,ref:a,ignoreStrict:!0,children:d(Ma.Provider,{value:h,children:t})})}const mu=e.forwardRef(pu);function fu(t){return t.value}function gu(t,e){return t.layout.min-e.layout.min}function yu(t){const n=f((()=>Oi(t))),{isStatic:i}=e.useContext(Ws);if(i){const[,i]=e.useState(t);e.useEffect((()=>n.on("change",i)),[])}return n}function vu(t,e){const n=yu(e()),i=()=>n.set(e());return i(),y((()=>{const e=()=>lt.preRender(i,!1,!0),n=t.map((t=>t.on("change",e)));return()=>{n.forEach((t=>t())),ut(i)}})),n}function xu(t,e,n,i){if("function"==typeof t)return function(t){ki.current=[],t();const e=vu(ki.current,t);return ki.current=void 0,e}(t);const s="function"==typeof e?e:ps(e,n,i);return Array.isArray(t)?wu(t,s):wu([t],(([t])=>s(t)))}function wu(t,e){const n=f((()=>[]));return vu(t,(()=>{n.length=0;const i=t.length;for(let e=0;e<i;e++)n[e]=t[e].get();return e(n)}))}function Tu(t,e=0){return fs(t)?t:yu(e)}function Pu({children:t,style:n={},value:i,as:s="li",onDrag:o,layout:r=!0,...a},l){const u=f((()=>du[s])),c=e.useContext(Ma),h={x:Tu(n.x),y:Tu(n.y)},p=xu([h.x,h.y],(([t,e])=>t||e?1:"unset")),{axis:m,registerItem:g,updateOrder:y}=c;return d(u,{drag:m,...a,dragSnapToOrigin:!0,style:{...n,x:h.x,y:h.y,zIndex:p},layout:r,onDrag:(t,e)=>{const{velocity:n}=e;n[m]&&y(i,h[m].get(),n[m]),o&&o(t,e)},onLayoutMeasure:t=>g(i,t),ref:l,ignoreStrict:!0,children:t})}const Su=e.forwardRef(Pu);var bu=Object.freeze({__proto__:null,Group:mu,Item:Su});function Au(t){return"object"==typeof t&&!Array.isArray(t)}function Eu(t,e,n,i){return"string"==typeof t&&Au(e)?bi(t,n,i):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function Mu(t,e,n){return t*(e+1)}function Vu(t,e,n,i){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:i.get(e)??t}function Cu(t,e,n,i,s,o){!function(t,e,n){for(let i=0;i<t.length;i++){const s=t[i];s.at>e&&s.at<n&&(w(t,s),i--)}}(t,s,o);for(let r=0;r<e.length;r++)t.push({value:e[r],at:Qt(s,o,i[r]),easing:et(n,r)})}function Ru(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function Du(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function ku(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function Lu(t,e){return e[t]||(e[t]=[]),e[t]}function Ou(t){return Array.isArray(t)?t:[t]}function ju(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const Bu=t=>"number"==typeof t,Fu=t=>t.every(Bu);class Iu extends ra{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}function Wu(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=hs(t)&&!ds(t)?new cu(e):new fa(e);n.mount(t),qr.set(t,n)}function Uu(t){const e=new Iu({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),qr.set(t,e)}function Nu(t,e,n,i){const s=[];if(function(t,e){return fs(t)||"number"==typeof t||"string"==typeof t&&!Au(e)}(t,e))s.push(lo(t,Au(e)&&e.default||e,n&&n.default||n));else{const o=Eu(t,e,i),r=o.length;for(let t=0;t<r;t++){const i=o[t],a=i instanceof Element?Wu:Uu;qr.has(i)||a(i);const l=qr.get(i),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,r)),s.push(...ja(l,{...e,transition:u},{}))}}return s}function $u(t,e,n){const i=[],s=function(t,{defaultTransition:e={},...n}={},i,s){const o=e.duration||.3,r=new Map,a=new Map,l={},u=new Map;let c=0,h=0,d=0;for(let n=0;n<t.length;n++){const r=t[n];if("string"==typeof r){u.set(r,h);continue}if(!Array.isArray(r)){u.set(r.name,Vu(h,r.at,c,u));continue}let[p,m,f={}]=r;void 0!==f.at&&(h=Vu(h,f.at,c,u));let g=0;const y=(t,n,i,r=0,a=0)=>{const l=Ou(t),{delay:u=0,times:c=$e(l),type:p="keyframes",repeat:m,repeatType:f,repeatDelay:y=0,...v}=n;let{ease:x=e.ease||"easeOut",duration:w}=n;const T="function"==typeof u?u(r,a):u,P=l.length,S=Bn(p)?p:s?.[p];if(P<=2&&S){let t=100;if(2===P&&Fu(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...v};void 0!==w&&(e.duration=j(w));const n=ge(e,t,S);x=n.ease,w=n.duration}w??(w=o);const b=h+T;1===c.length&&0===c[0]&&(c[1]=1);const A=c.length-l.length;if(A>0&&Ne(c,A),1===l.length&&l.unshift(null),m){w=Mu(w,m);const t=[...l],e=[...c];x=Array.isArray(x)?[...x]:[x];const n=[...x];for(let i=0;i<m;i++){l.push(...t);for(let s=0;s<t.length;s++)c.push(e[s]+(i+1)),x.push(0===s?"linear":et(n,s-1))}Ru(c,m)}const E=b+w;Cu(i,l,x,c,b,E),g=Math.max(T+w,g),d=Math.max(E,d)};if(fs(p))y(m,f,Lu("default",ku(p,a)));else{const t=Eu(p,m,i,l),e=t.length;for(let n=0;n<e;n++){const i=ku(t[n],a);for(const t in m)y(m[t],ju(f,t),Lu(t,i),n,e)}}c=h,h+=g}return a.forEach(((t,i)=>{for(const s in t){const o=t[s];o.sort(Du);const a=[],l=[],u=[];for(let t=0;t<o.length;t++){const{at:e,value:n,easing:i}=o[t];a.push(n),l.push(L(0,d,e)),u.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),r.has(i)||r.set(i,{keyframes:{},transition:{}});const c=r.get(i);c.keyframes[s]=a,c.transition[s]={...e,duration:d,ease:u,times:l,...n}}})),r}(t,e,n,{spring:Ie});return s.forEach((({keyframes:t,transition:e},n)=>{i.push(...Nu(n,t,e))})),i}function zu(t){return function(e,n,i){let s=[];var o;o=e,s=Array.isArray(o)&&o.some(Array.isArray)?$u(e,n,t):Nu(e,n,i,t);const r=new _n(s);return t&&t.animations.push(r),r}}const Xu=zu();const Yu=t=>function(e,n,i){return new _n(function(t,e,n,i){const s=bi(t,i),o=s.length,r=[];for(let t=0;t<o;t++){const i=s[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,o));for(const t in e){let n=e[t];Array.isArray(n)||(n=[n]);const s={...ii(a,t)};s.duration&&(s.duration=j(s.duration)),s.delay&&(s.delay=j(s.delay));const o=Qn(i),l=Jn(t,s.pseudoElement||""),u=o.get(l);u&&u.stop(),r.push({map:o,key:l,unresolvedKeyframes:n,options:{...s,element:i,name:t,allowFlatten:!a.type&&!a.ease}})}}for(let t=0;t<r.length;t++){const{unresolvedKeyframes:e,options:n}=r[t],{element:i,name:s,pseudoElement:o}=n;o||null!==e[0]||(e[0]=is(i,s)),Qe(e),Ti(e,s),!o&&e.length<2&&e.unshift(is(i,s)),n.keyframes=e}const a=[];for(let t=0;t<r.length;t++){const{map:e,key:n,options:i}=r[t],s=new In(i);e.set(n,s),s.finished.finally((()=>e.delete(n))),a.push(s)}return a}(e,n,i,t))},Hu=Yu(),Ku=new WeakMap;let Gu;function _u({target:t,contentRect:e,borderBoxSize:n}){Ku.get(t)?.forEach((i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return hs(t)&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function qu(t){t.forEach(_u)}function Zu(t,e){Gu||"undefined"!=typeof ResizeObserver&&(Gu=new ResizeObserver(qu));const n=bi(t);return n.forEach((t=>{let n=Ku.get(t);n||(n=new Set,Ku.set(t,n)),n.add(e),Gu?.observe(t)})),()=>{n.forEach((t=>{const n=Ku.get(t);n?.delete(e),n?.size||Gu?.unobserve(t)}))}}const Ju=new Set;let Qu;function tc(t){return Ju.add(t),Qu||(Qu=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Ju.forEach((t=>t(e)))},window.addEventListener("resize",Qu)),()=>{Ju.delete(t),!Ju.size&&Qu&&(Qu=void 0)}}const ec={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function nc(t,e,n,i){const s=n[e],{length:o,position:r}=ec[e],a=s.current,l=n.time;s.current=t[`scroll${r}`],s.scrollLength=t[`scroll${o}`]-t[`client${o}`],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=L(0,s.scrollLength,s.current);const u=i-l;s.velocity=u>50?0:F(s.current-a,u)}const ic={start:0,center:.5,end:1};function sc(t,e,n=0){let i=0;if(t in ic&&(t=ic[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(i=e*t),n+i}const oc=[0,0];function rc(t,e,n,i){let s=Array.isArray(t)?t:oc,o=0,r=0;return"number"==typeof t?s=[t,t]:"string"==typeof t&&(s=(t=t.trim()).includes(" ")?t.split(" "):[t,ic[t]?t:"0"]),o=sc(s[0],n,i),r=sc(s[1],e),o-r}const ac={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},lc={x:0,y:0};function uc(t,e,n){const{offset:i=ac.All}=n,{target:s=t,axis:o="y"}=n,r="y"===o?"height":"width",a=s!==t?function(t,e){const n={x:0,y:0};let i=t;for(;i&&i!==e;)if(zn(i))n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if("svg"===i.tagName){const t=i.getBoundingClientRect();i=i.parentElement;const e=i.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(i instanceof SVGGraphicsElement))break;{const{x:t,y:e}=i.getBBox();n.x+=t,n.y+=e;let s=null,o=i.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=i.parentNode;i=s}}return n}(s,t):lc,l=s===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(s),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=i.length;for(let t=0;t<h;t++){const n=rc(i[t],u[r],l[r],a[o]);c||n===e[o].interpolatorOffsets[t]||(c=!0),e[o].offset[t]=n}c&&(e[o].interpolate=Ue(e[o].offset,$e(i),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=P(0,1,e[o].interpolate(e[o].current))}function cc(t,e,n,i={}){return{measure:e=>{!function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!==t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),function(t,e,n){nc(t,"x",e,n),nc(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&uc(t,n,i)},notify:()=>e(n)}}const hc=new WeakMap,dc=new WeakMap,pc=new WeakMap,mc=t=>t===document.scrollingElement?window:t;function fc(t,{container:e=document.scrollingElement,...n}={}){if(!e)return R;let i=pc.get(e);i||(i=new Set,pc.set(e,i));const s=cc(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(i.add(s),!hc.has(e)){const t=()=>{for(const t of i)t.measure(ct.timestamp);lt.preUpdate(n)},n=()=>{for(const t of i)t.notify()},s=()=>lt.read(t);hc.set(e,s);const a=mc(e);window.addEventListener("resize",s,{passive:!0}),e!==document.documentElement&&dc.set(e,(r=s,"function"==typeof(o=e)?tc(o):Zu(o,r))),a.addEventListener("scroll",s,{passive:!0}),s()}var o,r;const a=hc.get(e);return lt.read(a,!1,!0),()=>{ut(a);const t=pc.get(e);if(!t)return;if(t.delete(s),t.size)return;const n=hc.get(e);hc.delete(e),n&&(mc(e).removeEventListener("scroll",n),dc.get(e)?.(),window.removeEventListener("resize",n))}}const gc=new Map;function yc({source:t,container:e,...n}){const{axis:i}=n;t&&(e=t);const s=gc.get(e)??new Map;gc.set(e,s);const o=n.target??"self",r=s.get(o)??{},a=i+(n.offset??[]).join(",");return r[a]||(r[a]=!n.target&&Vn()?new ScrollTimeline({source:e,axis:i}):function(t){const e={value:0},n=fc((n=>{e.value=100*n[t.axis].progress}),t);return{currentTime:e,cancel:n}}({container:e,...n})),r[a]}function vc(t,{axis:e="y",container:n=document.scrollingElement,...i}={}){if(!n)return R;const s={axis:e,container:n,...i};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)?fc((n=>{t(n[e.axis].progress,n)}),e):ss(t,yc(e))}(t,s):function(t,e){const n=yc(e);return t.attachTimeline({timeline:e.target?void 0:n,observe:t=>(t.pause(),ss((e=>{t.time=t.duration*e}),n))})}(t,s)}const xc={some:0,all:1};function wc(t,e,{root:n,margin:i,amount:s="some"}={}){const o=bi(t),r=new WeakMap,a=new IntersectionObserver((t=>{t.forEach((t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),r.delete(t.target))}))}),{root:n,rootMargin:i,threshold:"number"==typeof s?s:xc[s]});return o.forEach((t=>a.observe(t))),()=>a.disconnect()}const Tc=Va(lu());function Pc(t){return e.useEffect((()=>()=>t()),[])}const Sc={renderer:hu,...qa,...Ll},bc={...Sc,...bl,...Ol},Ac={renderer:hu,...qa};function Ec(t,n,i){e.useInsertionEffect((()=>t.on(n,i)),[t,n,i])}function Mc(t,e){Boolean(!e||e.current)}const Vc=()=>({scrollX:Oi(0),scrollY:Oi(0),scrollXProgress:Oi(0),scrollYProgress:Oi(0)});function Cc({container:t,target:n,layoutEffect:i=!0,...s}={}){const o=f(Vc);return(i?y:e.useEffect)((()=>(Mc(0,n),Mc(0,t),vc(((t,{x:e,y:n})=>{o.scrollX.set(e.current),o.scrollXProgress.set(e.progress),o.scrollY.set(n.current),o.scrollYProgress.set(n.progress)}),{...s,container:t?.current||void 0,target:n?.current||void 0}))),[t,n,JSON.stringify(s.offset)]),o}function Rc(t){const n=e.useRef(0),{isStatic:i}=e.useContext(Ws);e.useEffect((()=>{if(i)return;const e=({timestamp:e,delta:i})=>{n.current||(n.current=e),t(e-n.current,i)};return lt.update(e,!0),()=>ut(e)}),[t])}class Dc extends Li{constructor(){super(...arguments),this.isEnabled=!1}add(t){(pn.has(t)||Si.has(t))&&(this.isEnabled=!0,this.update())}update(){this.set(this.isEnabled?"transform":"auto")}}function kc(){!Gr.current&&_r();const[t]=e.useState(Kr.current);return t}function Lc(t,e){[...e].reverse().forEach((n=>{const i=t.getVariant(n);i&&ka(t,i),t.variantChildren&&t.variantChildren.forEach((t=>{Lc(t,e)}))}))}function Oc(){const t=new Set,e={subscribe:e=>(t.add(e),()=>{t.delete(e)}),start(e,n){const i=[];return t.forEach((t=>{i.push(Ia(t,e,{transitionOverride:n}))})),Promise.all(i)},set:e=>t.forEach((t=>{!function(t,e){Array.isArray(e)?Lc(t,e):"string"==typeof e?Lc(t,[e]):ka(t,e)}(t,e)})),stop(){t.forEach((t=>{!function(t){t.values.forEach((t=>t.stop()))}(t)}))},mount:()=>()=>{e.stop()}};return e}function jc(){const t=f(Oc);return y(t.mount,[]),t}const Bc=jc;class Fc{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach((n=>{n.start(t.nativeEvent||t,e)}))}}const Ic=()=>new Fc;function Wc(t){return null!==t&&"object"==typeof t&&Il in t}function Uc(){return Nc}function Nc(t){Ir.current&&(Ir.current.isUpdating=!1,Ir.current.blockUpdate(),t&&t())}const $c=new Map,zc=new Map,Xc=(t,e)=>`${t}: ${pn.has(e)?"transform":e}`;function Yc(t,e,n){const i=Xc(t,e),s=$c.get(i);if(!s)return null;const{animation:o,startTime:r}=s;function a(){window.MotionCancelOptimisedAnimation?.(t,e,n)}return o.onfinish=a,null===r||window.MotionHandoffIsComplete?.(t)?(a(),null):r}let Hc,Kc;const Gc=new Set;function _c(){Gc.forEach((t=>{t.animation.play(),t.animation.startTime=t.startTime})),Gc.clear()}const qc=()=>({});class Zc extends ra{constructor(){super(...arguments),this.measureInstanceViewportBox=Go}build(){}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return{}}getBaseTargetFromProps(){}readValueFromInstance(t,e,n){return n.initialState[e]||0}sortInstanceNodePosition(){return 0}}const Jc=iu({scrapeMotionValuesFromProps:qc,createRenderState:qc});let Qc=0;const th=t=>t>.001?1/t:1e5;t.AnimatePresence=({children:t,custom:n,initial:i=!0,onExitComplete:s,presenceAffectsLayout:o=!0,mode:r="sync",propagate:a=!1,anchorX:l="left"})=>{const[u,c]=Xs(a),p=e.useMemo((()=>Hs(t)),[t]),g=a&&!u?[]:p.map(Ys),v=e.useRef(!0),x=e.useRef(p),w=f((()=>new Map)),[T,P]=e.useState(p),[S,b]=e.useState(p);y((()=>{v.current=!1,x.current=p;for(let t=0;t<S.length;t++){const e=Ys(S[t]);g.includes(e)?w.delete(e):!0!==w.get(e)&&w.set(e,!1)}}),[S,g.length,g.join("-")]);const A=[];if(p!==T){let t=[...p];for(let e=0;e<S.length;e++){const n=S[e],i=Ys(n);g.includes(i)||(t.splice(e,0,n),A.push(n))}return"wait"===r&&A.length&&(t=A),b(Hs(t)),P(p),null}const{forceRender:E}=e.useContext(m);return d(h,{children:S.map((t=>{const e=Ys(t),h=!(a&&!u)&&(p===S||g.includes(e));return d($s,{isPresent:h,initial:!(v.current&&!i)&&void 0,custom:n,presenceAffectsLayout:o,mode:r,onExitComplete:h?void 0:()=>{if(!w.has(e))return;w.set(e,!0);let t=!0;w.forEach((e=>{e||(t=!1)})),t&&(E?.(),b(x.current),a&&c?.(),s&&s())},anchorX:l,children:t},e)}))})},t.AnimateSharedLayout=({children:t})=>(i.useEffect((()=>{}),[]),d(va,{id:f((()=>"asl-"+Qc++)),children:t})),t.AsyncMotionValueAnimation=Kn,t.DOMKeyframesResolver=xi,t.DeprecatedLayoutGroupContext=Ks,t.DragControls=Fc,t.FlatTree=fo,t.GroupAnimation=Gn,t.GroupAnimationWithThen=_n,t.JSAnimation=Je,t.KeyframeResolver=An,t.LayoutGroup=va,t.LayoutGroupContext=m,t.LazyMotion=function({children:t,features:n,strict:i=!1}){const[,s]=e.useState(!Ta(n)),o=e.useRef(void 0);if(!Ta(n)){const{renderer:t,...e}=n;o.current=t,wa(e)}return e.useEffect((()=>{Ta(n)&&n().then((({renderer:t,...e})=>{wa(e),o.current=t,s(!0)}))}),[]),d(xa.Provider,{value:{renderer:o.current,strict:i},children:t})},t.MotionConfig=function({children:t,isValidProp:n,...i}){n&&Aa(n),(i={...e.useContext(Ws),...i}).isStatic=f((()=>i.isStatic));const s=e.useMemo((()=>i),[JSON.stringify(i.transition),i.transformPagePoint,i.reducedMotion]);return d(Ws.Provider,{value:s,children:t})},t.MotionConfigContext=Ws,t.MotionContext=jl,t.MotionGlobalConfig=A,t.MotionValue=Li,t.NativeAnimation=In,t.NativeAnimationExtended=Nn,t.NativeAnimationWrapper=qn,t.PresenceContext=v,t.Reorder=bu,t.SubscriptionManager=O,t.SwitchLayoutGroupContext=wl,t.ViewTransitionBuilder=Bs,t.VisualElement=ra,t.WillChangeMotionValue=Dc,t.acceleratedValues=Si,t.activeAnimations=ft,t.addAttrValue=Ci,t.addPointerEvent=Qa,t.addPointerInfo=Ja,t.addScaleCorrector=sr,t.addStyleValue=Fi,t.addUniqueItem=x,t.alpha=Pt,t.analyseComplexValue=Yt,t.animate=Xu,t.animateMini=Hu,t.animateValue=function(t){return new Je(t)},t.animateView=function(t,e={}){return new Bs(t,e)},t.animateVisualElement=Ia,t.animationControls=Oc,t.animationMapKey=Jn,t.animations=qa,t.anticipate=K,t.applyPxDefaults=Ti,t.attachSpring=gs,t.attrEffect=Ri,t.backIn=Y,t.backInOut=H,t.backOut=X,t.buildTransform=ca,t.calcGeneratorDuration=fe,t.calcLength=Gs,t.cancelFrame=ut,t.cancelMicrotask=$i,t.cancelSync=Is,t.circIn=G,t.circInOut=q,t.circOut=_,t.clamp=P,t.collectMotionValues=ki,t.color=Ut,t.complex=_t,t.convertOffsetToTimes=ze,t.createBox=Go,t.createGeneratorEasing=ge,t.createRenderBatcher=at,t.createRendererMotionComponent=$l,t.createScopedAnimate=zu,t.cubicBezier=N,t.cubicBezierAsString=kn,t.defaultEasing=Xe,t.defaultOffset=$e,t.defaultTransformValue=ln,t.defaultValueTypes=fi,t.degrees=Lt,t.delay=go,t.dimensionValueTypes=ri,t.disableInstantTransitions=function(){A.instantAnimations=!1},t.distance=nl,t.distance2D=il,t.domAnimation=Sc,t.domMax=bc,t.domMin=Ac,t.easeIn=Z,t.easeInOut=Q,t.easeOut=J,t.easingDefinitionToFunction=st,t.fillOffset=Ne,t.fillWildcards=Qe,t.filterProps=Ea,t.findDimensionValueType=ai,t.findValueType=ws,t.flushKeyframeResolvers=bn,t.frame=lt,t.frameData=ct,t.frameSteps=ht,t.generateLinearEasing=pe,t.getAnimatableNone=yi,t.getAnimationMap=Qn,t.getComputedStyle=is,t.getDefaultValueType=gi,t.getEasingForSegment=et,t.getMixer=ae,t.getValueAsType=Ei,t.getValueTransition=ii,t.getVariableValue=ni,t.hasWarned=function(t){return I.has(t)},t.hex=Dt,t.hover=Gi,t.hsla=Wt,t.hslaToRgba=Zt,t.inView=wc,t.inertia=We,t.interpolate=Ue,t.invariant=b,t.invisibleValues=se,t.isBezierDefinition=nt,t.isBrowser=g,t.isCSSVariableName=yt,t.isCSSVariableToken=xt,t.isDragActive=Xi,t.isDragging=zi,t.isEasingArray=tt,t.isGenerator=Bn,t.isHTMLElement=zn,t.isMotionComponent=Wc,t.isMotionValue=fs,t.isNodeOrChild=_i,t.isNumericalString=E,t.isObject=M,t.isPrimaryPointer=qi,t.isSVGElement=hs,t.isSVGSVGElement=ds,t.isValidMotionProp=Sa,t.isWaapiSupportedEasing=function t(e){return Boolean("function"==typeof e&&Dn()||!e||"string"==typeof e&&(e in Ln||Dn())||nt(e)||Array.isArray(e)&&e.every(t))},t.isZeroValueString=V,t.keyframes=Ye,t.m=Tc,t.makeUseVisualState=iu,t.mapEasingToNativeEasing=On,t.mapValue=function(t,e,n,i){const s=ps(e,n,i);return ms((()=>s(t.get())))},t.maxGeneratorDuration=me,t.memo=C,t.microtask=Ni,t.millisecondsToSeconds=B,t.mirrorEasing=$,t.mix=he,t.mixArray=le,t.mixColor=ie,t.mixComplex=ce,t.mixImmediate=Jt,t.mixLinearColor=te,t.mixNumber=Qt,t.mixObject=ue,t.mixVisibility=oe,t.motion=du,t.motionValue=Oi,t.moveItem=T,t.noop=R,t.number=Tt,t.numberValueTypes=mi,t.observeTimeline=ss,t.optimizedAppearDataAttribute=ho,t.parseCSSVariable=ei,t.parseValueFromTransform=un,t.percent=Ot,t.pipe=k,t.positionalKeys=si,t.press=ns,t.progress=L,t.progressPercentage=It,t.propEffect=Di,t.px=jt,t.readTransformValue=cn,t.recordStats=function(){if(rt.value)throw us(),new Error("Stats are already being measured");const t=rt;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},lt.postRender(os,!0),cs},t.removeItem=w,t.resolveElements=bi,t.resolveMotionValue=yo,t.reverseEasing=z,t.rgbUnit=Ct,t.rgba=Rt,t.scale=St,t.scroll=vc,t.scrollInfo=fc,t.secondsToMilliseconds=j,t.setDragLock=Yi,t.setStyle=Mn,t.spring=Ie,t.springValue=function(t,e){const n=Oi(fs(t)?t.get():t);return gs(n,t,e),n},t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:i}={}){return(s,o)=>{const r="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,o),a=Math.abs(r-s);let l=t*a;if(i){const e=o*t;l=st(i)(l/e)*e}return e+l}},t.startOptimizedAppearAnimation=function(t,e,n,i,s){if(window.MotionIsMounted)return;const o=t.dataset[co];if(!o)return;window.MotionHandoffAnimation=Yc;const r=Xc(o,e);Kc||(Kc=jn(t,e,[n[0],n[0]],{duration:1e4,ease:"linear"}),$c.set(r,{animation:Kc,startTime:null}),window.MotionHandoffAnimation=Yc,window.MotionHasOptimisedAnimation=(t,e)=>{if(!t)return!1;if(!e)return zc.has(t);const n=Xc(t,e);return Boolean($c.get(n))},window.MotionHandoffMarkAsComplete=t=>{zc.has(t)&&zc.set(t,!0)},window.MotionHandoffIsComplete=t=>!0===zc.get(t),window.MotionCancelOptimisedAnimation=(t,e,n,i)=>{const s=Xc(t,e),o=$c.get(s);o&&(n&&void 0===i?n.postRender((()=>{n.postRender((()=>{o.animation.cancel()}))})):o.animation.cancel(),n&&i?(Gc.add(o),n.render(_c)):($c.delete(s),$c.size||(window.MotionCancelOptimisedAnimation=void 0)))},window.MotionCheckAppearSync=(t,e,n)=>{const i=po(t);if(!i)return;const s=window.MotionHasOptimisedAnimation?.(i,e),o=t.props.values?.[e];if(!s||!o)return;const r=n.on("change",(t=>{o.get()!==t&&(window.MotionCancelOptimisedAnimation?.(i,e),r())}));return r});const a=()=>{Kc.cancel();const o=jn(t,e,n,i);void 0===Hc&&(Hc=performance.now()),o.startTime=Hc,$c.set(r,{animation:o,startTime:Hc}),s&&s(o)};zc.set(o,!1),Kc.ready?Kc.ready.then(a).catch(R):a()},t.startWaapiAnimation=jn,t.statsBuffer=rt,t.steps=function(t,e="end"){return n=>{const i=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,s="end"===e?Math.floor(i):Math.ceil(i);return P(0,1,s/t)}},t.styleEffect=Ii,t.supportedWaapiEasing=Ln,t.supportsBrowserAnimation=Hn,t.supportsFlags=Cn,t.supportsLinearEasing=Dn,t.supportsPartialKeyframes=Pi,t.supportsScrollTimeline=Vn,t.svgEffect=Ui,t.sync=Fs,t.testValueType=oi,t.time=mt,t.transform=ps,t.transformPropOrder=dn,t.transformProps=pn,t.transformValue=ms,t.transformValueTypes=pi,t.unwrapMotionComponent=function(t){if(Wc(t))return t[Il]},t.useAnimate=function(){const t=f((()=>({current:null,animations:[]}))),e=f((()=>zu(t)));return Pc((()=>{t.animations.forEach((t=>t.stop()))})),[t,e]},t.useAnimateMini=function(){const t=f((()=>({current:null,animations:[]}))),e=f((()=>Yu(t)));return Pc((()=>{t.animations.forEach((t=>t.stop()))})),[t,e]},t.useAnimation=Bc,t.useAnimationControls=jc,t.useAnimationFrame=Rc,t.useCycle=function(...t){const n=e.useRef(0),[i,s]=e.useState(t[n.current]),o=e.useCallback((e=>{n.current="number"!=typeof e?W(0,t.length,n.current+1):e,s(t[n.current])}),[t.length,...t]);return[i,o]},t.useDeprecatedAnimatedState=function(t){const[n,i]=e.useState(t),s=Jc({},!1),o=f((()=>new Zc({props:{onUpdate:t=>{i({...t})}},visualState:s,presenceContext:null},{initialState:t})));return e.useLayoutEffect((()=>(o.mount({}),()=>o.unmount())),[o]),[n,f((()=>t=>Ia(o,t)))]},t.useDeprecatedInvertedScale=function(t){let n=yu(1),i=yu(1);const{visualElement:s}=e.useContext(jl);return t?(n=t.scaleX||n,i=t.scaleY||i):s&&(n=s.getValue("scaleX",1),i=s.getValue("scaleY",1)),{scaleX:xu(n,th),scaleY:xu(i,th)}},t.useDomEvent=function(t,n,i,s){e.useEffect((()=>{const e=t.current;if(i&&e)return Br(e,n,i,s)}),[t,n,i,s])},t.useDragControls=function(){return f(Ic)},t.useElementScroll=function(t){return Cc({container:t})},t.useForceUpdate=ga,t.useInView=function(t,{root:n,margin:i,amount:s,once:o=!1,initial:r=!1}={}){const[a,l]=e.useState(r);return e.useEffect((()=>{if(!t.current||o&&a)return;const e={root:n&&n.current||void 0,margin:i,amount:s};return wc(t.current,(()=>(l(!0),o?void 0:()=>l(!1))),e)}),[n,t,i,o,s]),a},t.useInstantLayoutTransition=Uc,t.useInstantTransition=function(){const[t,n]=ga(),i=Uc(),s=e.useRef(-1);return e.useEffect((()=>{lt.postRender((()=>lt.postRender((()=>{n===s.current&&(A.instantAnimations=!1)}))))}),[n]),e=>{i((()=>{A.instantAnimations=!0,t(),e(),s.current=n+1}))}},t.useIsPresent=function(){return null===(t=e.useContext(v))||t.isPresent;var t},t.useIsomorphicLayoutEffect=y,t.useMotionTemplate=function(t,...e){const n=t.length;return vu(e.filter(fs),(function(){let i="";for(let s=0;s<n;s++){i+=t[s];const n=e[s];n&&(i+=fs(n)?n.get():n)}return i}))},t.useMotionValue=yu,t.useMotionValueEvent=Ec,t.usePresence=Xs,t.usePresenceData=function(){const t=e.useContext(v);return t?t.custom:void 0},t.useReducedMotion=kc,t.useReducedMotionConfig=function(){const t=kc(),{reducedMotion:n}=e.useContext(Ws);return"never"!==n&&("always"===n||t)},t.useResetProjection=function(){return e.useCallback((()=>{const t=Ir.current;t&&t.resetTree()}),[])},t.useScroll=Cc,t.useSpring=function(t,n={}){const{isStatic:i}=e.useContext(Ws),s=()=>fs(t)?t.get():t;if(i)return xu(s);const o=yu(s());return e.useInsertionEffect((()=>gs(o,t,n)),[o,JSON.stringify(n)]),o},t.useTime=function(){const t=yu(0);return Rc((e=>t.set(e))),t},t.useTransform=xu,t.useUnmountEffect=Pc,t.useVelocity=function(t){const e=yu(t.getVelocity()),n=()=>{const i=t.getVelocity();e.set(i),i&&lt.update(n)};return Ec(t,"change",(()=>{lt.update(n,!1,!0)})),e},t.useViewportScroll=function(){return Cc()},t.useWillChange=function(){return f((()=>new Dc("auto")))},t.velocityPerSecond=F,t.vh=Bt,t.visualElementStore=qr,t.vw=Ft,t.warnOnce=function(t,e,n){t||I.has(e)||(console.warn(e),n&&console.warn(n),I.add(e))},t.warning=S,t.wrap=W}));
