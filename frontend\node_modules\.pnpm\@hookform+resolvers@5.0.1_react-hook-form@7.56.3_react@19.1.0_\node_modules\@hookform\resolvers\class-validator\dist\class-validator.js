var r=require("@hookform/resolvers"),e=require("class-transformer"),t=require("class-validator");function s(r,e,t,a){return void 0===t&&(t={}),void 0===a&&(a=""),r.reduce(function(r,t){var i=a?a+"."+t.property:t.property;if(t.constraints){var o=Object.keys(t.constraints)[0];r[i]={type:o,message:t.constraints[o]};var n=r[i];e&&n&&Object.assign(n,{types:t.constraints})}return t.children&&t.children.length&&s(t.children,e,r,i),r},t)}exports.classValidatorResolver=function(a,i,o){return void 0===i&&(i={}),void 0===o&&(o={}),function(n,l,c){try{var v=i.validator,d=e.plainToClass(a,n,i.transformer);return Promise.resolve(("sync"===o.mode?t.validateSync:t.validate)(d,v)).then(function(e){return e.length?{values:{},errors:r.toNestErrors(s(e,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)}:(c.shouldUseNativeValidation&&r.validateFieldsNatively({},c),{values:o.raw?Object.assign({},n):d,errors:{}})})}catch(r){return Promise.reject(r)}}};
//# sourceMappingURL=class-validator.js.map
