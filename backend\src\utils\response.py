#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API响应工具类
提供统一的API响应格式和状态码
"""

from flask import jsonify

class APIResponse:
    """API响应工具类"""
    
    # 状态码定义
    SUCCESS = 200
    BAD_REQUEST = 400
    NOT_FOUND = 404
    INTERNAL_ERROR = 500
    
    # 状态码对应的中文描述
    STATUS_MESSAGES = {
        200: '操作成功',
        400: '请求参数错误',
        404: '资源不存在',
        500: '服务器内部错误'
    }
    
    @staticmethod
    def success(message='操作成功', data=None):
        """成功响应"""
        response = {
            'code': APIResponse.SUCCESS,
            'message': message,
            'success': True
        }
        if data is not None:
            response['data'] = data
        return jsonify(response)
    
    @staticmethod
    def error(code=500, message=None):
        """错误响应"""
        if message is None:
            message = APIResponse.STATUS_MESSAGES.get(code, '未知错误')
        
        return jsonify({
            'code': code,
            'message': message,
            'success': False
        })
    
    @staticmethod
    def bad_request(message='请求参数错误'):
        """400错误响应"""
        return APIResponse.error(APIResponse.BAD_REQUEST, message)
    
    @staticmethod
    def not_found(message='资源不存在'):
        """404错误响应"""
        return APIResponse.error(APIResponse.NOT_FOUND, message)
    
    @staticmethod
    def internal_error(message='服务器内部错误'):
        """500错误响应"""
        return APIResponse.error(APIResponse.INTERNAL_ERROR, message)

# 业务相关的响应消息
class BusinessMessages:
    """业务相关的中文消息"""
    
    # 商品管理
    PRODUCT_CREATED = '商品创建成功'
    PRODUCT_LIST_SUCCESS = '获取商品列表成功'
    PRODUCT_NAME_REQUIRED = '商品名称不能为空'
    PRODUCT_SKU_REQUIRED = 'SKU不能为空'
    PRODUCT_SKU_EXISTS = 'SKU已存在，请使用其他SKU'
    
    # 仓库管理
    WAREHOUSE_CREATED = '仓库创建成功'
    WAREHOUSE_LIST_SUCCESS = '获取仓库列表成功'
    WAREHOUSE_NAME_REQUIRED = '仓库名称不能为空'
    
    # 库存管理
    INVENTORY_QUERY_SUCCESS = '查询库存成功'
    INVENTORY_WARNING_SUCCESS = '获取库存预警成功'
    INVENTORY_INSUFFICIENT = '库存不足'
    
    # 入库管理
    INBOUND_CREATED = '入库单创建成功'
    INBOUND_LIST_SUCCESS = '获取入库单列表成功'
    INBOUND_CONFIRMED = '入库确认成功'
    INBOUND_NOT_FOUND = '入库单不存在'
    INBOUND_STATUS_ERROR = '入库单状态不正确'
    INBOUND_WAREHOUSE_REQUIRED = '目标仓库不能为空'
    INBOUND_ITEMS_REQUIRED = '入库商品明细不能为空'
    
    # 出库管理
    OUTBOUND_CREATED = '出库单创建成功'
    OUTBOUND_LIST_SUCCESS = '获取出库单列表成功'
    OUTBOUND_CONFIRMED = '出库确认成功'
    OUTBOUND_NOT_FOUND = '出库单不存在'
    OUTBOUND_STATUS_ERROR = '出库单状态不正确'
    OUTBOUND_WAREHOUSE_REQUIRED = '出库仓库不能为空'
    OUTBOUND_ITEMS_REQUIRED = '出库商品明细不能为空'
    
    # 供应商管理
    SUPPLIER_CREATED = '供应商创建成功'
    SUPPLIER_LIST_SUCCESS = '获取供应商列表成功'
    SUPPLIER_NAME_REQUIRED = '供应商名称不能为空'
    
    # 客户管理
    CUSTOMER_CREATED = '客户创建成功'
    CUSTOMER_LIST_SUCCESS = '获取客户列表成功'
    CUSTOMER_NAME_REQUIRED = '客户名称不能为空'
    
    # 通用消息
    FIELD_REQUIRED = '必填字段不能为空'
    PRODUCT_QUANTITY_REQUIRED = '商品和数量不能为空'
    OPERATION_FAILED = '操作失败'
