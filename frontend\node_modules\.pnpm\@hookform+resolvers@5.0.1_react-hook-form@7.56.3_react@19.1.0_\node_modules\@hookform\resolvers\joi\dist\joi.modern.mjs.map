{"version": 3, "file": "joi.modern.mjs", "sources": ["../src/joi.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type { ValidationError } from 'joi';\nimport { FieldError, appendErrors } from 'react-hook-form';\nimport { Resolver } from './types';\n\nconst parseErrorSchema = (\n  error: ValidationError,\n  validateAllFieldCriteria: boolean,\n) =>\n  error.details.length\n    ? error.details.reduce<Record<string, FieldError>>((previous, error) => {\n        const _path = error.path.join('.');\n\n        if (!previous[_path]) {\n          previous[_path] = { message: error.message, type: error.type };\n        }\n\n        if (validateAllFieldCriteria) {\n          const types = previous[_path].types;\n          const messages = types && types[error.type!];\n\n          previous[_path] = appendErrors(\n            _path,\n            validateAllFieldCriteria,\n            previous,\n            error.type,\n            messages\n              ? ([] as string[]).concat(messages as string[], error.message)\n              : error.message,\n          ) as FieldError;\n        }\n\n        return previous;\n      }, {})\n    : {};\n\n/**\n * Creates a resolver for react-hook-form using Joi schema validation\n * @param {Joi.ObjectSchema<TFieldValues>} schema - The Joi schema to validate against\n * @param {Joi.ValidationOptions} [schemaOptions] - Optional Joi validation options\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {string} [resolverOptions.mode='async'] - Validation mode\n * @returns {Resolver<TFieldValues>} A resolver function compatible with react-hook-form\n * @example\n * const schema = Joi.object({\n *   name: Joi.string().required(),\n *   age: Joi.number().required()\n * });\n *\n * useForm({\n *   resolver: joiResolver(schema)\n * });\n */\nexport const joiResolver: Resolver =\n  (\n    schema,\n    schemaOptions = {\n      abortEarly: false,\n    },\n    resolverOptions = {},\n  ) =>\n  async (values, context, options) => {\n    const _schemaOptions = Object.assign({}, schemaOptions, {\n      context,\n    });\n\n    let result: Record<string, any> = {};\n    if (resolverOptions.mode === 'sync') {\n      result = schema.validate(values, _schemaOptions);\n    } else {\n      try {\n        result.value = await schema.validateAsync(values, _schemaOptions);\n      } catch (e) {\n        result.error = e;\n      }\n    }\n\n    if (result.error) {\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrorSchema(\n            result.error,\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      errors: {},\n      values: result.value,\n    };\n  };\n"], "names": ["joiResolver", "schema", "schemaOptions", "abort<PERSON><PERSON><PERSON>", "resolverOptions", "async", "values", "context", "options", "_schemaOptions", "Object", "assign", "result", "mode", "validate", "value", "validateAsync", "e", "error", "errors", "toNestErrors", "validateAllFieldCriteria", "shouldUseNativeValidation", "criteriaMode", "details", "length", "reduce", "previous", "_path", "path", "join", "message", "type", "types", "messages", "appendErrors", "concat", "validateFieldsNatively", "parseErrorSchema"], "mappings": "8HAKA,MAgDaA,EACXA,CACEC,EACAC,EAAgB,CACdC,YAAY,GAEdC,EAAkB,KAEpBC,MAAOC,EAAQC,EAASC,KACtB,MAAMC,EAAiBC,OAAOC,OAAO,CAAA,EAAIT,EAAe,CACtDK,YAGF,IAAIK,EAA8B,CAAA,EAClC,GAA6B,SAAzBR,EAAgBS,KAClBD,EAASX,EAAOa,SAASR,EAAQG,QAEjC,IACEG,EAAOG,YAAcd,EAAOe,cAAcV,EAAQG,EACpD,CAAE,MAAOQ,GACPL,EAAOM,MAAQD,CACjB,CAGF,OAAIL,EAAOM,MACF,CACLZ,OAAQ,CAAE,EACVa,OAAQC,GA1EdF,EA4EUN,EAAOM,MA3EjBG,GA4EWb,EAAQc,2BACkB,QAAzBd,EAAQe,aA3EpBL,EAAMM,QAAQC,OACVP,EAAMM,QAAQE,OAAmC,CAACC,EAAUT,KAC1D,MAAMU,EAAQV,EAAMW,KAAKC,KAAK,KAM9B,GAJKH,EAASC,KACZD,EAASC,GAAS,CAAEG,QAASb,EAAMa,QAASC,KAAMd,EAAMc,OAGtDX,EAA0B,CAC5B,MAAMY,EAAQN,EAASC,GAAOK,MACxBC,EAAWD,GAASA,EAAMf,EAAMc,MAEtCL,EAASC,GAASO,EAChBP,EACAP,EACAM,EACAT,EAAMc,KACNE,EACK,GAAgBE,OAAOF,EAAsBhB,EAAMa,SACpDb,EAAMa,QAEd,CAEA,OAAOJ,GACN,CAAE,GACL,CAAE,GAoDEnB,KAKNA,EAAQc,2BAA6Be,EAAuB,CAAA,EAAI7B,GAEzD,CACLW,OAAQ,CAAA,EACRb,OAAQM,EAAOG,QA1FIuB,IACvBpB,EACAG"}