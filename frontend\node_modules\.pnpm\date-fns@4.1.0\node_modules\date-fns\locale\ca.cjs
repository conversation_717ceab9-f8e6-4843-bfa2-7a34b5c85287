"use strict";
exports.ca = void 0;
var _index = require("./ca/_lib/formatDistance.cjs");
var _index2 = require("./ca/_lib/formatLong.cjs");
var _index3 = require("./ca/_lib/formatRelative.cjs");
var _index4 = require("./ca/_lib/localize.cjs");
var _index5 = require("./ca/_lib/match.cjs");

/**
 * @category Locales
 * @summary Catalan locale.
 * @language Catalan
 * @iso-639-2 cat
 * <AUTHOR> [@guigrpa](https://github.com/guigrpa)
 * <AUTHOR> [@avizcaino](https://github.com/avizcaino)
 */
const ca = (exports.ca = {
  code: "ca",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
