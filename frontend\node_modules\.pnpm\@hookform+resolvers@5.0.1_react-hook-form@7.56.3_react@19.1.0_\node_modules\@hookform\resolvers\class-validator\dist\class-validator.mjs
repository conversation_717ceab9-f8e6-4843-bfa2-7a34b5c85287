import{toNestErrors as r,validateFieldsNatively as e}from"@hookform/resolvers";import{plainToClass as t}from"class-transformer";import{validateSync as o,validate as n}from"class-validator";function s(r,e,t,o){return void 0===t&&(t={}),void 0===o&&(o=""),r.reduce(function(r,t){var n=o?o+"."+t.property:t.property;if(t.constraints){var i=Object.keys(t.constraints)[0];r[n]={type:i,message:t.constraints[i]};var a=r[n];e&&a&&Object.assign(a,{types:t.constraints})}return t.children&&t.children.length&&s(t.children,e,r,n),r},t)}function i(i,a,c){return void 0===a&&(a={}),void 0===c&&(c={}),function(l,d,u){try{var v=a.validator,m=t(i,l,a.transformer);return Promise.resolve(("sync"===c.mode?o:n)(m,v)).then(function(t){return t.length?{values:{},errors:r(s(t,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)}:(u.shouldUseNativeValidation&&e({},u),{values:c.raw?Object.assign({},l):m,errors:{}})})}catch(r){return Promise.reject(r)}}}export{i as classValidatorResolver};
//# sourceMappingURL=class-validator.module.js.map
