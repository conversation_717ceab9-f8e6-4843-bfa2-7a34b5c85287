"use strict";
exports.uzCyrl = void 0;
var _index = require("./uz-Cyrl/_lib/formatDistance.cjs");
var _index2 = require("./uz-Cyrl/_lib/formatLong.cjs");
var _index3 = require("./uz-Cyrl/_lib/formatRelative.cjs");
var _index4 = require("./uz-Cyrl/_lib/localize.cjs");
var _index5 = require("./uz-Cyrl/_lib/match.cjs");

/**
 * @category Locales
 * @summary Uzbek Cyrillic locale.
 * @language Uzbek
 * @iso-639-2 uzb
 * <AUTHOR> [@kamronbek28](https://github.com/kamronbek28)
 */
const uzCyrl = (exports.uzCyrl = {
  code: "uz-Cyrl",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
