import type { ContextOptions, DateArg } from "./types.js";
/**
 * The {@link getISOWeek} function options.
 */
export interface GetISOWeekOptions extends ContextOptions<Date> {}
/**
 * @name getISOWeek
 * @category ISO Week Helpers
 * @summary Get the ISO week of the given date.
 *
 * @description
 * Get the ISO week of the given date.
 *
 * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date
 *
 * @param date - The given date
 * @param options - The options
 *
 * @returns The ISO week
 *
 * @example
 * // Which week of the ISO-week numbering year is 2 January 2005?
 * const result = getISOWeek(new Date(2005, 0, 2))
 * //=> 53
 */
export declare function getISOWeek(
  date: DateArg<Date> & {},
  options?: GetISOWeekOptions | undefined,
): number;
