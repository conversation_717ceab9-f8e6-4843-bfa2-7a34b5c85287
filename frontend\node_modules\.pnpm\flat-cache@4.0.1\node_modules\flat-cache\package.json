{"name": "flat-cache", "version": "4.0.1", "description": "A stupidly simple key/value storage using files to persist some data", "repository": "jaredwray/flat-cache", "license": "MIT", "author": {"name": "<PERSON>", "url": "https://jaredwray.com"}, "main": "src/cache.js", "files": ["src/cache.js", "src/del.js", "src/utils.js"], "engines": {"node": ">=16"}, "precommit": ["npm run verify --silent"], "prepush": ["npm run verify --silent"], "scripts": {"eslint": "eslint --cache --cache-location=node_modules/.cache/ ./src/**/*.js ./test/**/*.js", "clean": "rimraf ./node_modules ./package-lock.json ./yarn.lock ./coverage", "eslint-fix": "npm run eslint -- --fix", "autofix": "npm run eslint-fix", "check": "npm run eslint", "verify": "npm run eslint && npm run test:cache", "test:cache": "c8 mocha -R spec test/specs", "test:ci:cache": "c8 --reporter=lcov mocha -R spec test/specs", "test": "npm run verify --silent", "format": "prettier --write ."}, "keywords": ["json cache", "simple cache", "file cache", "key par", "key value", "cache"], "devDependencies": {"c8": "^9.1.0", "chai": "^4.3.10", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-mocha": "^10.2.0", "glob-expand": "^0.2.1", "mocha": "^10.3.0", "prettier": "^3.2.4", "rimraf": "^5.0.5", "sinon": "^17.0.1", "write": "^2.0.0"}, "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}}