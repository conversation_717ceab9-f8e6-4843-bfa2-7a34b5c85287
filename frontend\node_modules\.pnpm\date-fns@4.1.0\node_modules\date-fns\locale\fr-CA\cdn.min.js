(()=>{var K;function Q(J){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(Y){return typeof Y}:function(Y){return Y&&typeof Symbol=="function"&&Y.constructor===Symbol&&Y!==Symbol.prototype?"symbol":typeof Y},Q(J)}function A(J,Y){var X=Object.keys(J);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(J);Y&&(Z=Z.filter(function(G){return Object.getOwnPropertyDescriptor(J,G).enumerable})),X.push.apply(X,Z)}return X}function $(J){for(var Y=1;Y<arguments.length;Y++){var X=arguments[Y]!=null?arguments[Y]:{};Y%2?A(Object(X),!0).forEach(function(Z){D(J,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(X)):A(Object(X)).forEach(function(Z){Object.defineProperty(J,Z,Object.getOwnPropertyDescriptor(X,Z))})}return J}function D(J,Y,X){if(Y=E(Y),Y in J)Object.defineProperty(J,Y,{value:X,enumerable:!0,configurable:!0,writable:!0});else J[Y]=X;return J}function E(J){var Y=W(J,"string");return Q(Y)=="symbol"?Y:String(Y)}function W(J,Y){if(Q(J)!="object"||!J)return J;var X=J[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(J,Y||"default");if(Q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(Y==="string"?String:Number)(J)}var V=Object.defineProperty,YJ=function J(Y,X){for(var Z in X)V(Y,Z,{get:X[Z],enumerable:!0,configurable:!0,set:function G(H){return X[Z]=function(){return H}}})},x={lessThanXSeconds:{one:"moins d\u2019une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d\u2019une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d\u2019un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu\u2019un an",other:"presque {{count}} ans"}},N=function J(Y,X,Z){var G,H=x[Y];if(typeof H==="string")G=H;else if(X===1)G=H.one;else G=H.other.replace("{{count}}",String(X));if(Z!==null&&Z!==void 0&&Z.addSuffix)if(Z.comparison&&Z.comparison>0)return"dans "+G;else return"il y a "+G;return G},R={lastWeek:"eeee 'dernier \xE0' p",yesterday:"'hier \xE0' p",today:"'aujourd\u2019hui \xE0' p",tomorrow:"'demain \xE0' p'",nextWeek:"eeee 'prochain \xE0' p",other:"P"},S=function J(Y,X,Z,G){return R[Y]};function U(J){return function(Y,X){var Z=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",G;if(Z==="formatting"&&J.formattingValues){var H=J.defaultFormattingWidth||J.defaultWidth,B=X!==null&&X!==void 0&&X.width?String(X.width):H;G=J.formattingValues[B]||J.formattingValues[H]}else{var C=J.defaultWidth,T=X!==null&&X!==void 0&&X.width?String(X.width):J.defaultWidth;G=J.values[T]||J.values[C]}var I=J.argumentCallback?J.argumentCallback(Y):Y;return G[I]}}var M={narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant J\xE9sus-Christ","apr\xE8s J\xE9sus-Christ"]},L={narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2\xE8me trim.","3\xE8me trim.","4\xE8me trim."],wide:["1er trimestre","2\xE8me trimestre","3\xE8me trimestre","4\xE8me trimestre"]},j={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","f\xE9vr.","mars","avr.","mai","juin","juil.","ao\xFBt","sept.","oct.","nov.","d\xE9c."],wide:["janvier","f\xE9vrier","mars","avril","mai","juin","juillet","ao\xFBt","septembre","octobre","novembre","d\xE9cembre"]},w={narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},_={narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"apr\xE8s-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l\u2019apr\xE8s-midi",evening:"du soir",night:"du matin"}},F=function J(Y,X){var Z=Number(Y),G=X===null||X===void 0?void 0:X.unit;if(Z===0)return"0";var H=["year","week","hour","minute","second"],B;if(Z===1)B=G&&H.includes(G)?"\xE8re":"er";else B="\xE8me";return Z+B},P=["MMM","MMMM"],v={preprocessor:function J(Y,X){if(Y.getDate()===1)return X;var Z=X.some(function(G){return G.isToken&&P.includes(G.value)});if(!Z)return X;return X.map(function(G){return G.isToken&&G.value==="do"?{isToken:!0,value:"d"}:G})},ordinalNumber:F,era:U({values:M,defaultWidth:"wide"}),quarter:U({values:L,defaultWidth:"wide",argumentCallback:function J(Y){return Y-1}}),month:U({values:j,defaultWidth:"wide"}),day:U({values:w,defaultWidth:"wide"}),dayPeriod:U({values:_,defaultWidth:"wide"})};function q(J){return function(Y){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=X.width,G=Z&&J.matchPatterns[Z]||J.matchPatterns[J.defaultMatchWidth],H=Y.match(G);if(!H)return null;var B=H[0],C=Z&&J.parsePatterns[Z]||J.parsePatterns[J.defaultParseWidth],T=Array.isArray(C)?f(C,function(z){return z.test(B)}):k(C,function(z){return z.test(B)}),I;I=J.valueCallback?J.valueCallback(T):T,I=X.valueCallback?X.valueCallback(I):I;var XJ=Y.slice(B.length);return{value:I,rest:XJ}}}function k(J,Y){for(var X in J)if(Object.prototype.hasOwnProperty.call(J,X)&&Y(J[X]))return X;return}function f(J,Y){for(var X=0;X<J.length;X++)if(Y(J[X]))return X;return}function b(J){return function(Y){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=Y.match(J.matchPattern);if(!Z)return null;var G=Z[0],H=Y.match(J.parsePattern);if(!H)return null;var B=J.valueCallback?J.valueCallback(H[0]):H[0];B=X.valueCallback?X.valueCallback(B):B;var C=Y.slice(G.length);return{value:B,rest:C}}}var h=/^(\d+)(ième|ère|ème|er|e)?/i,m=/\d+/i,y={narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},c={any:[/^av/i,/^ap/i]},g={narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},d={any:[/1/i,/2/i,/3/i,/4/i]},u={narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},l={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},p={narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},i={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},n={narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},s={any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},o={ordinalNumber:b({matchPattern:h,parsePattern:m,valueCallback:function J(Y){return parseInt(Y)}}),era:q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:c,defaultParseWidth:"any"}),quarter:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function J(Y){return Y+1}}),month:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),day:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:n,defaultMatchWidth:"any",parsePatterns:s,defaultParseWidth:"any"})};function O(J){return function(){var Y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=Y.width?String(Y.width):J.defaultWidth,Z=J.formats[X]||J.formats[J.defaultWidth];return Z}}var e={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"yy-MM-dd"},r={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},a={full:"{{date}} '\xE0' {{time}}",long:"{{date}} '\xE0' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},t={date:O({formats:e,defaultWidth:"full"}),time:O({formats:r,defaultWidth:"full"}),dateTime:O({formats:a,defaultWidth:"full"})},JJ={code:"fr-CA",formatDistance:N,formatLong:t,formatRelative:S,localize:v,match:o,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=$($({},window.dateFns),{},{locale:$($({},(K=window.dateFns)===null||K===void 0?void 0:K.locale),{},{frCA:JJ})})})();

//# debugId=A7C180C26884843464756E2164756E21
