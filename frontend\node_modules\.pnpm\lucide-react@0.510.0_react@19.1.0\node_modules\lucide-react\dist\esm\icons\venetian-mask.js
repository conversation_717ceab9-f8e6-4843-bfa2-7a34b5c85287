/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M18 11c-1.5 0-2.5.5-3 2", key: "1fod00" }],
  [
    "path",
    {
      d: "M4 6a2 2 0 0 0-2 2v4a5 5 0 0 0 5 5 8 8 0 0 1 5 2 8 8 0 0 1 5-2 5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3a8 8 0 0 0-5 2 8 8 0 0 0-5-2z",
      key: "d70hit"
    }
  ],
  ["path", { d: "M6 11c1.5 0 2.5.5 3 2", key: "136fht" }]
];
const VenetianMask = createLucideIcon("venetian-mask", __iconNode);

export { __iconNode, VenetianMask as default };
//# sourceMappingURL=venetian-mask.js.map
