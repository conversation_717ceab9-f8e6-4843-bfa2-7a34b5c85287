{"version": 3, "file": "eslintrc.cjs", "sources": ["../lib/config-array/ignore-pattern.js", "../lib/config-array/extracted-config.js", "../lib/config-array/config-array.js", "../lib/config-array/config-dependency.js", "../lib/config-array/override-tester.js", "../lib/config-array/index.js", "../lib/shared/config-ops.js", "../lib/shared/deprecation-warnings.js", "../lib/shared/ajv.js", "../lib/shared/deep-merge-arrays.js", "../conf/config-schema.js", "../conf/environments.js", "../lib/shared/config-validator.js", "../lib/shared/naming.js", "../lib/shared/relative-module-resolver.js", "../lib/config-array-factory.js", "../lib/cascading-config-array-factory.js", "../lib/flat-compat.js", "../lib/index.js"], "sourcesContent": ["/**\n * @fileoverview `IgnorePattern` class.\n *\n * `IgnorePattern` class has the set of glob patterns and the base path.\n *\n * It provides two static methods.\n *\n * - `IgnorePattern.createDefaultIgnore(cwd)`\n *      Create the default predicate function.\n * - `IgnorePattern.createIgnore(ignorePatterns)`\n *      Create the predicate function from multiple `IgnorePattern` objects.\n *\n * It provides two properties and a method.\n *\n * - `patterns`\n *      The glob patterns that ignore to lint.\n * - `basePath`\n *      The base path of the glob patterns. If absolute paths existed in the\n *      glob patterns, those are handled as relative paths to the base path.\n * - `getPatternsRelativeTo(basePath)`\n *      Get `patterns` as modified for a given base path. It modifies the\n *      absolute paths in the patterns as prepending the difference of two base\n *      paths.\n *\n * `ConfigArrayFactory` creates `IgnorePattern` objects when it processes\n * `ignorePatterns` properties.\n *\n * <AUTHOR> <https://github.com/mysticatea>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport assert from \"node:assert\";\nimport path from \"node:path\";\nimport ignore from \"ignore\";\nimport debugOrig from \"debug\";\n\nconst debug = debugOrig(\"eslintrc:ignore-pattern\");\n\n/** @typedef {ReturnType<import(\"ignore\").default>} Ignore */\n\n//------------------------------------------------------------------------------\n// Helpers\n//------------------------------------------------------------------------------\n\n/**\n * Get the path to the common ancestor directory of given paths.\n * @param {string[]} sourcePaths The paths to calculate the common ancestor.\n * @returns {string} The path to the common ancestor directory.\n */\nfunction getCommonAncestorPath(sourcePaths) {\n    let result = sourcePaths[0];\n\n    for (let i = 1; i < sourcePaths.length; ++i) {\n        const a = result;\n        const b = sourcePaths[i];\n\n        // Set the shorter one (it's the common ancestor if one includes the other).\n        result = a.length < b.length ? a : b;\n\n        // Set the common ancestor.\n        for (let j = 0, lastSepPos = 0; j < a.length && j < b.length; ++j) {\n            if (a[j] !== b[j]) {\n                result = a.slice(0, lastSepPos);\n                break;\n            }\n            if (a[j] === path.sep) {\n                lastSepPos = j;\n            }\n        }\n    }\n\n    let resolvedResult = result || path.sep;\n\n    // if Windows common ancestor is root of drive must have trailing slash to be absolute.\n    if (resolvedResult && resolvedResult.endsWith(\":\") && process.platform === \"win32\") {\n        resolvedResult += path.sep;\n    }\n    return resolvedResult;\n}\n\n/**\n * Make relative path.\n * @param {string} from The source path to get relative path.\n * @param {string} to The destination path to get relative path.\n * @returns {string} The relative path.\n */\nfunction relative(from, to) {\n    const relPath = path.relative(from, to);\n\n    if (path.sep === \"/\") {\n        return relPath;\n    }\n    return relPath.split(path.sep).join(\"/\");\n}\n\n/**\n * Get the trailing slash if existed.\n * @param {string} filePath The path to check.\n * @returns {string} The trailing slash if existed.\n */\nfunction dirSuffix(filePath) {\n    const isDir = (\n        filePath.endsWith(path.sep) ||\n        (process.platform === \"win32\" && filePath.endsWith(\"/\"))\n    );\n\n    return isDir ? \"/\" : \"\";\n}\n\nconst DefaultPatterns = Object.freeze([\"/**/node_modules/*\"]);\nconst DotPatterns = Object.freeze([\".*\", \"!.eslintrc.*\", \"!../\"]);\n\n//------------------------------------------------------------------------------\n// Public\n//------------------------------------------------------------------------------\n\n/**\n * Represents a set of glob patterns to ignore against a base path.\n */\nclass IgnorePattern {\n\n    /**\n     * The default patterns.\n     * @type {string[]}\n     */\n    static get DefaultPatterns() {\n        return DefaultPatterns;\n    }\n\n    /**\n     * Create the default predicate function.\n     * @param {string} cwd The current working directory.\n     * @returns {((filePath:string, dot:boolean) => boolean) & {basePath:string; patterns:string[]}}\n     * The preficate function.\n     * The first argument is an absolute path that is checked.\n     * The second argument is the flag to not ignore dotfiles.\n     * If the predicate function returned `true`, it means the path should be ignored.\n     */\n    static createDefaultIgnore(cwd) {\n        return this.createIgnore([new IgnorePattern(DefaultPatterns, cwd)]);\n    }\n\n    /**\n     * Create the predicate function from multiple `IgnorePattern` objects.\n     * @param {IgnorePattern[]} ignorePatterns The list of ignore patterns.\n     * @returns {((filePath:string, dot?:boolean) => boolean) & {basePath:string; patterns:string[]}}\n     * The preficate function.\n     * The first argument is an absolute path that is checked.\n     * The second argument is the flag to not ignore dotfiles.\n     * If the predicate function returned `true`, it means the path should be ignored.\n     */\n    static createIgnore(ignorePatterns) {\n        debug(\"Create with: %o\", ignorePatterns);\n\n        const basePath = getCommonAncestorPath(ignorePatterns.map(p => p.basePath));\n        const patterns = ignorePatterns.flatMap(p => p.getPatternsRelativeTo(basePath));\n        const ig = ignore({ allowRelativePaths: true }).add([...DotPatterns, ...patterns]);\n        const dotIg = ignore({ allowRelativePaths: true }).add(patterns);\n\n        debug(\"  processed: %o\", { basePath, patterns });\n\n        return Object.assign(\n            (filePath, dot = false) => {\n                assert(path.isAbsolute(filePath), \"'filePath' should be an absolute path.\");\n                const relPathRaw = relative(basePath, filePath);\n                const relPath = relPathRaw && (relPathRaw + dirSuffix(filePath));\n                const adoptedIg = dot ? dotIg : ig;\n                const result = relPath !== \"\" && adoptedIg.ignores(relPath);\n\n                debug(\"Check\", { filePath, dot, relativePath: relPath, result });\n                return result;\n            },\n            { basePath, patterns }\n        );\n    }\n\n    /**\n     * Initialize a new `IgnorePattern` instance.\n     * @param {string[]} patterns The glob patterns that ignore to lint.\n     * @param {string} basePath The base path of `patterns`.\n     */\n    constructor(patterns, basePath) {\n        assert(path.isAbsolute(basePath), \"'basePath' should be an absolute path.\");\n\n        /**\n         * The glob patterns that ignore to lint.\n         * @type {string[]}\n         */\n        this.patterns = patterns;\n\n        /**\n         * The base path of `patterns`.\n         * @type {string}\n         */\n        this.basePath = basePath;\n\n        /**\n         * If `true` then patterns which don't start with `/` will match the paths to the outside of `basePath`. Defaults to `false`.\n         *\n         * It's set `true` for `.eslintignore`, `package.json`, and `--ignore-path` for backward compatibility.\n         * It's `false` as-is for `ignorePatterns` property in config files.\n         * @type {boolean}\n         */\n        this.loose = false;\n    }\n\n    /**\n     * Get `patterns` as modified for a given base path. It modifies the\n     * absolute paths in the patterns as prepending the difference of two base\n     * paths.\n     * @param {string} newBasePath The base path.\n     * @returns {string[]} Modifired patterns.\n     */\n    getPatternsRelativeTo(newBasePath) {\n        assert(path.isAbsolute(newBasePath), \"'newBasePath' should be an absolute path.\");\n        const { basePath, loose, patterns } = this;\n\n        if (newBasePath === basePath) {\n            return patterns;\n        }\n        const prefix = `/${relative(newBasePath, basePath)}`;\n\n        return patterns.map(pattern => {\n            const negative = pattern.startsWith(\"!\");\n            const head = negative ? \"!\" : \"\";\n            const body = negative ? pattern.slice(1) : pattern;\n\n            if (body.startsWith(\"/\") || body.startsWith(\"../\")) {\n                return `${head}${prefix}${body}`;\n            }\n            return loose ? pattern : `${head}${prefix}/**/${body}`;\n        });\n    }\n}\n\nexport { IgnorePattern };\n", "/**\n * @fileoverview `ExtractedConfig` class.\n *\n * `ExtractedConfig` class expresses a final configuration for a specific file.\n *\n * It provides one method.\n *\n * - `toCompatibleObjectAsConfigFileContent()`\n *      Convert this configuration to the compatible object as the content of\n *      config files. It converts the loaded parser and plugins to strings.\n *      `CLIEngine#getConfigForFile(filePath)` method uses this method.\n *\n * `ConfigArray#extractConfig(filePath)` creates a `ExtractedConfig` instance.\n *\n * <AUTHOR> <https://github.com/mysticatea>\n */\n\nimport { IgnorePattern } from \"./ignore-pattern.js\";\n\n// For VSCode intellisense\n/** @typedef {import(\"../../shared/types\").ConfigData} ConfigData */\n/** @typedef {import(\"../../shared/types\").GlobalConf} GlobalConf */\n/** @typedef {import(\"../../shared/types\").SeverityConf} SeverityConf */\n/** @typedef {import(\"./config-dependency\").DependentParser} DependentParser */\n/** @typedef {import(\"./config-dependency\").DependentPlugin} DependentPlugin */\n\n/**\n * Check if `xs` starts with `ys`.\n * @template T\n * @param {T[]} xs The array to check.\n * @param {T[]} ys The array that may be the first part of `xs`.\n * @returns {boolean} `true` if `xs` starts with `ys`.\n */\nfunction startsWith(xs, ys) {\n    return xs.length >= ys.length && ys.every((y, i) => y === xs[i]);\n}\n\n/**\n * The class for extracted config data.\n */\nclass ExtractedConfig {\n    constructor() {\n\n        /**\n         * The config name what `noInlineConfig` setting came from.\n         * @type {string}\n         */\n        this.configNameOfNoInlineConfig = \"\";\n\n        /**\n         * Environments.\n         * @type {Record<string, boolean>}\n         */\n        this.env = {};\n\n        /**\n         * Global variables.\n         * @type {Record<string, GlobalConf>}\n         */\n        this.globals = {};\n\n        /**\n         * The glob patterns that ignore to lint.\n         * @type {(((filePath:string, dot?:boolean) => boolean) & { basePath:string; patterns:string[] }) | undefined}\n         */\n        this.ignores = void 0;\n\n        /**\n         * The flag that disables directive comments.\n         * @type {boolean|undefined}\n         */\n        this.noInlineConfig = void 0;\n\n        /**\n         * Parser definition.\n         * @type {DependentParser|null}\n         */\n        this.parser = null;\n\n        /**\n         * Options for the parser.\n         * @type {Object}\n         */\n        this.parserOptions = {};\n\n        /**\n         * Plugin definitions.\n         * @type {Record<string, DependentPlugin>}\n         */\n        this.plugins = {};\n\n        /**\n         * Processor ID.\n         * @type {string|null}\n         */\n        this.processor = null;\n\n        /**\n         * The flag that reports unused `eslint-disable` directive comments.\n         * @type {boolean|undefined}\n         */\n        this.reportUnusedDisableDirectives = void 0;\n\n        /**\n         * Rule settings.\n         * @type {Record<string, [SeverityConf, ...any[]]>}\n         */\n        this.rules = {};\n\n        /**\n         * Shared settings.\n         * @type {Object}\n         */\n        this.settings = {};\n    }\n\n    /**\n     * Convert this config to the compatible object as a config file content.\n     * @returns {ConfigData} The converted object.\n     */\n    toCompatibleObjectAsConfigFileContent() {\n        const {\n            /* eslint-disable no-unused-vars -- needed to make `config` correct */\n            configNameOfNoInlineConfig: _ignore1,\n            processor: _ignore2,\n            /* eslint-enable no-unused-vars -- needed to make `config` correct */\n            ignores,\n            ...config\n        } = this;\n\n        config.parser = config.parser && config.parser.filePath;\n        config.plugins = Object.keys(config.plugins).filter(Boolean).reverse();\n        config.ignorePatterns = ignores ? ignores.patterns : [];\n\n        // Strip the default patterns from `ignorePatterns`.\n        if (startsWith(config.ignorePatterns, IgnorePattern.DefaultPatterns)) {\n            config.ignorePatterns =\n                config.ignorePatterns.slice(IgnorePattern.DefaultPatterns.length);\n        }\n\n        return config;\n    }\n}\n\nexport { ExtractedConfig };\n", "/**\n * @fileoverview `ConfigArray` class.\n *\n * `ConfigArray` class expresses the full of a configuration. It has the entry\n * config file, base config files that were extended, loaded parsers, and loaded\n * plugins.\n *\n * `ConfigArray` class provides three properties and two methods.\n *\n * - `pluginEnvironments`\n * - `pluginProcessors`\n * - `pluginRules`\n *      The `Map` objects that contain the members of all plugins that this\n *      config array contains. Those map objects don't have mutation methods.\n *      Those keys are the member ID such as `pluginId/memberName`.\n * - `isRoot()`\n *      If `true` then this configuration has `root:true` property.\n * - `extractConfig(filePath)`\n *      Extract the final configuration for a given file. This means merging\n *      every config array element which that `criteria` property matched. The\n *      `filePath` argument must be an absolute path.\n *\n * `ConfigArrayFactory` provides the loading logic of config files.\n *\n * <AUTHOR> <https://github.com/mysticatea>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport { ExtractedConfig } from \"./extracted-config.js\";\nimport { IgnorePattern } from \"./ignore-pattern.js\";\n\n//------------------------------------------------------------------------------\n// Helpers\n//------------------------------------------------------------------------------\n\n// Define types for VSCode IntelliSense.\n/** @typedef {import(\"../../shared/types\").Environment} Environment */\n/** @typedef {import(\"../../shared/types\").GlobalConf} GlobalConf */\n/** @typedef {import(\"../../shared/types\").RuleConf} RuleConf */\n/** @typedef {import(\"../../shared/types\").Rule} Rule */\n/** @typedef {import(\"../../shared/types\").Plugin} Plugin */\n/** @typedef {import(\"../../shared/types\").Processor} Processor */\n/** @typedef {import(\"./config-dependency\").DependentParser} DependentParser */\n/** @typedef {import(\"./config-dependency\").DependentPlugin} DependentPlugin */\n/** @typedef {import(\"./override-tester\")[\"OverrideTester\"]} OverrideTester */\n\n/**\n * @typedef {Object} ConfigArrayElement\n * @property {string} name The name of this config element.\n * @property {string} filePath The path to the source file of this config element.\n * @property {InstanceType<OverrideTester>|null} criteria The tester for the `files` and `excludedFiles` of this config element.\n * @property {Record<string, boolean>|undefined} env The environment settings.\n * @property {Record<string, GlobalConf>|undefined} globals The global variable settings.\n * @property {IgnorePattern|undefined} ignorePattern The ignore patterns.\n * @property {boolean|undefined} noInlineConfig The flag that disables directive comments.\n * @property {DependentParser|undefined} parser The parser loader.\n * @property {Object|undefined} parserOptions The parser options.\n * @property {Record<string, DependentPlugin>|undefined} plugins The plugin loaders.\n * @property {string|undefined} processor The processor name to refer plugin's processor.\n * @property {boolean|undefined} reportUnusedDisableDirectives The flag to report unused `eslint-disable` comments.\n * @property {boolean|undefined} root The flag to express root.\n * @property {Record<string, RuleConf>|undefined} rules The rule settings\n * @property {Object|undefined} settings The shared settings.\n * @property {\"config\" | \"ignore\" | \"implicit-processor\"} type The element type.\n */\n\n/**\n * @typedef {Object} ConfigArrayInternalSlots\n * @property {Map<string, ExtractedConfig>} cache The cache to extract configs.\n * @property {ReadonlyMap<string, Environment>|null} envMap The map from environment ID to environment definition.\n * @property {ReadonlyMap<string, Processor>|null} processorMap The map from processor ID to environment definition.\n * @property {ReadonlyMap<string, Rule>|null} ruleMap The map from rule ID to rule definition.\n */\n\n/** @type {WeakMap<ConfigArray, ConfigArrayInternalSlots>} */\nconst internalSlotsMap = new class extends WeakMap {\n    get(key) {\n        let value = super.get(key);\n\n        if (!value) {\n            value = {\n                cache: new Map(),\n                envMap: null,\n                processorMap: null,\n                ruleMap: null\n            };\n            super.set(key, value);\n        }\n\n        return value;\n    }\n}();\n\n/**\n * Get the indices which are matched to a given file.\n * @param {ConfigArrayElement[]} elements The elements.\n * @param {string} filePath The path to a target file.\n * @returns {number[]} The indices.\n */\nfunction getMatchedIndices(elements, filePath) {\n    const indices = [];\n\n    for (let i = elements.length - 1; i >= 0; --i) {\n        const element = elements[i];\n\n        if (!element.criteria || (filePath && element.criteria.test(filePath))) {\n            indices.push(i);\n        }\n    }\n\n    return indices;\n}\n\n/**\n * Check if a value is a non-null object.\n * @param {any} x The value to check.\n * @returns {boolean} `true` if the value is a non-null object.\n */\nfunction isNonNullObject(x) {\n    return typeof x === \"object\" && x !== null;\n}\n\n/**\n * Merge two objects.\n *\n * Assign every property values of `y` to `x` if `x` doesn't have the property.\n * If `x`'s property value is an object, it does recursive.\n * @param {Object} target The destination to merge\n * @param {Object|undefined} source The source to merge.\n * @returns {void}\n */\nfunction mergeWithoutOverwrite(target, source) {\n    if (!isNonNullObject(source)) {\n        return;\n    }\n\n    for (const key of Object.keys(source)) {\n        if (key === \"__proto__\") {\n            continue;\n        }\n\n        if (isNonNullObject(target[key])) {\n            mergeWithoutOverwrite(target[key], source[key]);\n        } else if (target[key] === void 0) {\n            if (isNonNullObject(source[key])) {\n                target[key] = Array.isArray(source[key]) ? [] : {};\n                mergeWithoutOverwrite(target[key], source[key]);\n            } else if (source[key] !== void 0) {\n                target[key] = source[key];\n            }\n        }\n    }\n}\n\n/**\n * The error for plugin conflicts.\n */\nclass PluginConflictError extends Error {\n\n    /**\n     * Initialize this error object.\n     * @param {string} pluginId The plugin ID.\n     * @param {{filePath:string, importerName:string}[]} plugins The resolved plugins.\n     */\n    constructor(pluginId, plugins) {\n        super(`Plugin \"${pluginId}\" was conflicted between ${plugins.map(p => `\"${p.importerName}\"`).join(\" and \")}.`);\n        this.messageTemplate = \"plugin-conflict\";\n        this.messageData = { pluginId, plugins };\n    }\n}\n\n/**\n * Merge plugins.\n * `target`'s definition is prior to `source`'s.\n * @param {Record<string, DependentPlugin>} target The destination to merge\n * @param {Record<string, DependentPlugin>|undefined} source The source to merge.\n * @returns {void}\n * @throws {PluginConflictError} When a plugin was conflicted.\n */\nfunction mergePlugins(target, source) {\n    if (!isNonNullObject(source)) {\n        return;\n    }\n\n    for (const key of Object.keys(source)) {\n        if (key === \"__proto__\") {\n            continue;\n        }\n        const targetValue = target[key];\n        const sourceValue = source[key];\n\n        // Adopt the plugin which was found at first.\n        if (targetValue === void 0) {\n            if (sourceValue.error) {\n                throw sourceValue.error;\n            }\n            target[key] = sourceValue;\n        } else if (sourceValue.filePath !== targetValue.filePath) {\n            throw new PluginConflictError(key, [\n                {\n                    filePath: targetValue.filePath,\n                    importerName: targetValue.importerName\n                },\n                {\n                    filePath: sourceValue.filePath,\n                    importerName: sourceValue.importerName\n                }\n            ]);\n        }\n    }\n}\n\n/**\n * Merge rule configs.\n * `target`'s definition is prior to `source`'s.\n * @param {Record<string, Array>} target The destination to merge\n * @param {Record<string, RuleConf>|undefined} source The source to merge.\n * @returns {void}\n */\nfunction mergeRuleConfigs(target, source) {\n    if (!isNonNullObject(source)) {\n        return;\n    }\n\n    for (const key of Object.keys(source)) {\n        if (key === \"__proto__\") {\n            continue;\n        }\n        const targetDef = target[key];\n        const sourceDef = source[key];\n\n        // Adopt the rule config which was found at first.\n        if (targetDef === void 0) {\n            if (Array.isArray(sourceDef)) {\n                target[key] = [...sourceDef];\n            } else {\n                target[key] = [sourceDef];\n            }\n\n        /*\n         * If the first found rule config is severity only and the current rule\n         * config has options, merge the severity and the options.\n         */\n        } else if (\n            targetDef.length === 1 &&\n            Array.isArray(sourceDef) &&\n            sourceDef.length >= 2\n        ) {\n            targetDef.push(...sourceDef.slice(1));\n        }\n    }\n}\n\n/**\n * Create the extracted config.\n * @param {ConfigArray} instance The config elements.\n * @param {number[]} indices The indices to use.\n * @returns {ExtractedConfig} The extracted config.\n * @throws {Error} When a plugin is conflicted.\n */\nfunction createConfig(instance, indices) {\n    const config = new ExtractedConfig();\n    const ignorePatterns = [];\n\n    // Merge elements.\n    for (const index of indices) {\n        const element = instance[index];\n\n        // Adopt the parser which was found at first.\n        if (!config.parser && element.parser) {\n            if (element.parser.error) {\n                throw element.parser.error;\n            }\n            config.parser = element.parser;\n        }\n\n        // Adopt the processor which was found at first.\n        if (!config.processor && element.processor) {\n            config.processor = element.processor;\n        }\n\n        // Adopt the noInlineConfig which was found at first.\n        if (config.noInlineConfig === void 0 && element.noInlineConfig !== void 0) {\n            config.noInlineConfig = element.noInlineConfig;\n            config.configNameOfNoInlineConfig = element.name;\n        }\n\n        // Adopt the reportUnusedDisableDirectives which was found at first.\n        if (config.reportUnusedDisableDirectives === void 0 && element.reportUnusedDisableDirectives !== void 0) {\n            config.reportUnusedDisableDirectives = element.reportUnusedDisableDirectives;\n        }\n\n        // Collect ignorePatterns\n        if (element.ignorePattern) {\n            ignorePatterns.push(element.ignorePattern);\n        }\n\n        // Merge others.\n        mergeWithoutOverwrite(config.env, element.env);\n        mergeWithoutOverwrite(config.globals, element.globals);\n        mergeWithoutOverwrite(config.parserOptions, element.parserOptions);\n        mergeWithoutOverwrite(config.settings, element.settings);\n        mergePlugins(config.plugins, element.plugins);\n        mergeRuleConfigs(config.rules, element.rules);\n    }\n\n    // Create the predicate function for ignore patterns.\n    if (ignorePatterns.length > 0) {\n        config.ignores = IgnorePattern.createIgnore(ignorePatterns.reverse());\n    }\n\n    return config;\n}\n\n/**\n * Collect definitions.\n * @template T, U\n * @param {string} pluginId The plugin ID for prefix.\n * @param {Record<string,T>} defs The definitions to collect.\n * @param {Map<string, U>} map The map to output.\n * @returns {void}\n */\nfunction collect(pluginId, defs, map) {\n    if (defs) {\n        const prefix = pluginId && `${pluginId}/`;\n\n        for (const [key, value] of Object.entries(defs)) {\n            map.set(`${prefix}${key}`, value);\n        }\n    }\n}\n\n/**\n * Delete the mutation methods from a given map.\n * @param {Map<any, any>} map The map object to delete.\n * @returns {void}\n */\nfunction deleteMutationMethods(map) {\n    Object.defineProperties(map, {\n        clear: { configurable: true, value: void 0 },\n        delete: { configurable: true, value: void 0 },\n        set: { configurable: true, value: void 0 }\n    });\n}\n\n/**\n * Create `envMap`, `processorMap`, `ruleMap` with the plugins in the config array.\n * @param {ConfigArrayElement[]} elements The config elements.\n * @param {ConfigArrayInternalSlots} slots The internal slots.\n * @returns {void}\n */\nfunction initPluginMemberMaps(elements, slots) {\n    const processed = new Set();\n\n    slots.envMap = new Map();\n    slots.processorMap = new Map();\n    slots.ruleMap = new Map();\n\n    for (const element of elements) {\n        if (!element.plugins) {\n            continue;\n        }\n\n        for (const [pluginId, value] of Object.entries(element.plugins)) {\n            const plugin = value.definition;\n\n            if (!plugin || processed.has(pluginId)) {\n                continue;\n            }\n            processed.add(pluginId);\n\n            collect(pluginId, plugin.environments, slots.envMap);\n            collect(pluginId, plugin.processors, slots.processorMap);\n            collect(pluginId, plugin.rules, slots.ruleMap);\n        }\n    }\n\n    deleteMutationMethods(slots.envMap);\n    deleteMutationMethods(slots.processorMap);\n    deleteMutationMethods(slots.ruleMap);\n}\n\n/**\n * Create `envMap`, `processorMap`, `ruleMap` with the plugins in the config array.\n * @param {ConfigArray} instance The config elements.\n * @returns {ConfigArrayInternalSlots} The extracted config.\n */\nfunction ensurePluginMemberMaps(instance) {\n    const slots = internalSlotsMap.get(instance);\n\n    if (!slots.ruleMap) {\n        initPluginMemberMaps(instance, slots);\n    }\n\n    return slots;\n}\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\n/**\n * The Config Array.\n *\n * `ConfigArray` instance contains all settings, parsers, and plugins.\n * You need to call `ConfigArray#extractConfig(filePath)` method in order to\n * extract, merge and get only the config data which is related to an arbitrary\n * file.\n * @extends {Array<ConfigArrayElement>}\n */\nclass ConfigArray extends Array {\n\n    /**\n     * Get the plugin environments.\n     * The returned map cannot be mutated.\n     * @type {ReadonlyMap<string, Environment>} The plugin environments.\n     */\n    get pluginEnvironments() {\n        return ensurePluginMemberMaps(this).envMap;\n    }\n\n    /**\n     * Get the plugin processors.\n     * The returned map cannot be mutated.\n     * @type {ReadonlyMap<string, Processor>} The plugin processors.\n     */\n    get pluginProcessors() {\n        return ensurePluginMemberMaps(this).processorMap;\n    }\n\n    /**\n     * Get the plugin rules.\n     * The returned map cannot be mutated.\n     * @returns {ReadonlyMap<string, Rule>} The plugin rules.\n     */\n    get pluginRules() {\n        return ensurePluginMemberMaps(this).ruleMap;\n    }\n\n    /**\n     * Check if this config has `root` flag.\n     * @returns {boolean} `true` if this config array is root.\n     */\n    isRoot() {\n        for (let i = this.length - 1; i >= 0; --i) {\n            const root = this[i].root;\n\n            if (typeof root === \"boolean\") {\n                return root;\n            }\n        }\n        return false;\n    }\n\n    /**\n     * Extract the config data which is related to a given file.\n     * @param {string} filePath The absolute path to the target file.\n     * @returns {ExtractedConfig} The extracted config data.\n     */\n    extractConfig(filePath) {\n        const { cache } = internalSlotsMap.get(this);\n        const indices = getMatchedIndices(this, filePath);\n        const cacheKey = indices.join(\",\");\n\n        if (!cache.has(cacheKey)) {\n            cache.set(cacheKey, createConfig(this, indices));\n        }\n\n        return cache.get(cacheKey);\n    }\n\n    /**\n     * Check if a given path is an additional lint target.\n     * @param {string} filePath The absolute path to the target file.\n     * @returns {boolean} `true` if the file is an additional lint target.\n     */\n    isAdditionalTargetPath(filePath) {\n        for (const { criteria, type } of this) {\n            if (\n                type === \"config\" &&\n                criteria &&\n                !criteria.endsWithWildcard &&\n                criteria.test(filePath)\n            ) {\n                return true;\n            }\n        }\n        return false;\n    }\n}\n\n/**\n * Get the used extracted configs.\n * CLIEngine will use this method to collect used deprecated rules.\n * @param {ConfigArray} instance The config array object to get.\n * @returns {ExtractedConfig[]} The used extracted configs.\n * @private\n */\nfunction getUsedExtractedConfigs(instance) {\n    const { cache } = internalSlotsMap.get(instance);\n\n    return Array.from(cache.values());\n}\n\n\nexport {\n    ConfigArray,\n    getUsedExtractedConfigs\n};\n", "/**\n * @fileoverview `ConfigDependency` class.\n *\n * `ConfigDependency` class expresses a loaded parser or plugin.\n *\n * If the parser or plugin was loaded successfully, it has `definition` property\n * and `filePath` property. Otherwise, it has `error` property.\n *\n * When `JSON.stringify()` converted a `ConfigDependency` object to a JSON, it\n * omits `definition` property.\n *\n * `ConfigArrayFactory` creates `ConfigDependency` objects when it loads parsers\n * or plugins.\n *\n * <AUTHOR> <https://github.com/mysticatea>\n */\n\nimport util from \"node:util\";\n\n/**\n * The class is to store parsers or plugins.\n * This class hides the loaded object from `JSON.stringify()` and `console.log`.\n * @template T\n */\nclass ConfigDependency {\n\n    /**\n     * Initialize this instance.\n     * @param {Object} data The dependency data.\n     * @param {T} [data.definition] The dependency if the loading succeeded.\n     * @param {T} [data.original] The original, non-normalized dependency if the loading succeeded.\n     * @param {Error} [data.error] The error object if the loading failed.\n     * @param {string} [data.filePath] The actual path to the dependency if the loading succeeded.\n     * @param {string} data.id The ID of this dependency.\n     * @param {string} data.importerName The name of the config file which loads this dependency.\n     * @param {string} data.importerPath The path to the config file which loads this dependency.\n     */\n    constructor({\n        definition = null,\n        original = null,\n        error = null,\n        filePath = null,\n        id,\n        importerName,\n        importerPath\n    }) {\n\n        /**\n         * The loaded dependency if the loading succeeded.\n         * @type {T|null}\n         */\n        this.definition = definition;\n\n        /**\n         * The original dependency as loaded directly from disk if the loading succeeded.\n         * @type {T|null}\n         */\n        this.original = original;\n\n        /**\n         * The error object if the loading failed.\n         * @type {Error|null}\n         */\n        this.error = error;\n\n        /**\n         * The loaded dependency if the loading succeeded.\n         * @type {string|null}\n         */\n        this.filePath = filePath;\n\n        /**\n         * The ID of this dependency.\n         * @type {string}\n         */\n        this.id = id;\n\n        /**\n         * The name of the config file which loads this dependency.\n         * @type {string}\n         */\n        this.importerName = importerName;\n\n        /**\n         * The path to the config file which loads this dependency.\n         * @type {string}\n         */\n        this.importerPath = importerPath;\n    }\n\n    /**\n     * Converts this instance to a JSON compatible object.\n     * @returns {Object} a JSON compatible object.\n     */\n    toJSON() {\n        const obj = this[util.inspect.custom]();\n\n        // Display `error.message` (`Error#message` is unenumerable).\n        if (obj.error instanceof Error) {\n            obj.error = { ...obj.error, message: obj.error.message };\n        }\n\n        return obj;\n    }\n\n    /**\n     * Custom inspect method for Node.js `console.log()`.\n     * @returns {Object} an object to display by `console.log()`.\n     */\n    [util.inspect.custom]() {\n        const {\n            definition: _ignore1, // eslint-disable-line no-unused-vars -- needed to make `obj` correct\n            original: _ignore2, // eslint-disable-line no-unused-vars -- needed to make `obj` correct\n            ...obj\n        } = this;\n\n        return obj;\n    }\n}\n\n/** @typedef {ConfigDependency<import(\"../../shared/types\").Parser>} DependentParser */\n/** @typedef {ConfigDependency<import(\"../../shared/types\").Plugin>} DependentPlugin */\n\nexport { ConfigDependency };\n", "/**\n * @fileoverview `OverrideTester` class.\n *\n * `OverrideTester` class handles `files` property and `excludedFiles` property\n * of `overrides` config.\n *\n * It provides one method.\n *\n * - `test(filePath)`\n *      Test if a file path matches the pair of `files` property and\n *      `excludedFiles` property. The `filePath` argument must be an absolute\n *      path.\n *\n * `ConfigArrayFactory` creates `OverrideTester` objects when it processes\n * `overrides` properties.\n *\n * <AUTHOR> Na<PERSON>hima <https://github.com/mysticatea>\n */\n\nimport assert from \"node:assert\";\nimport path from \"node:path\";\nimport util from \"node:util\";\nimport minimatch from \"minimatch\";\n\nconst { Minimatch } = minimatch;\n\nconst minimatchOpts = { dot: true, matchBase: true };\n\n/**\n * @typedef {Object} Pattern\n * @property {InstanceType<Minimatch>[] | null} includes The positive matchers.\n * @property {InstanceType<Minimatch>[] | null} excludes The negative matchers.\n */\n\n/**\n * Normalize a given pattern to an array.\n * @param {string|string[]|undefined} patterns A glob pattern or an array of glob patterns.\n * @returns {string[]|null} Normalized patterns.\n * @private\n */\nfunction normalizePatterns(patterns) {\n    if (Array.isArray(patterns)) {\n        return patterns.filter(Boolean);\n    }\n    if (typeof patterns === \"string\" && patterns) {\n        return [patterns];\n    }\n    return [];\n}\n\n/**\n * Create the matchers of given patterns.\n * @param {string[]} patterns The patterns.\n * @returns {InstanceType<Minimatch>[] | null} The matchers.\n */\nfunction toMatcher(patterns) {\n    if (patterns.length === 0) {\n        return null;\n    }\n    return patterns.map(pattern => {\n        if (/^\\.[/\\\\]/u.test(pattern)) {\n            return new Minimatch(\n                pattern.slice(2),\n\n                // `./*.js` should not match with `subdir/foo.js`\n                { ...minimatchOpts, matchBase: false }\n            );\n        }\n        return new Minimatch(pattern, minimatchOpts);\n    });\n}\n\n/**\n * Convert a given matcher to string.\n * @param {Pattern} matchers The matchers.\n * @returns {string} The string expression of the matcher.\n */\nfunction patternToJson({ includes, excludes }) {\n    return {\n        includes: includes && includes.map(m => m.pattern),\n        excludes: excludes && excludes.map(m => m.pattern)\n    };\n}\n\n/**\n * The class to test given paths are matched by the patterns.\n */\nclass OverrideTester {\n\n    /**\n     * Create a tester with given criteria.\n     * If there are no criteria, returns `null`.\n     * @param {string|string[]} files The glob patterns for included files.\n     * @param {string|string[]} excludedFiles The glob patterns for excluded files.\n     * @param {string} basePath The path to the base directory to test paths.\n     * @returns {OverrideTester|null} The created instance or `null`.\n     * @throws {Error} When invalid patterns are given.\n     */\n    static create(files, excludedFiles, basePath) {\n        const includePatterns = normalizePatterns(files);\n        const excludePatterns = normalizePatterns(excludedFiles);\n        let endsWithWildcard = false;\n\n        if (includePatterns.length === 0) {\n            return null;\n        }\n\n        // Rejects absolute paths or relative paths to parents.\n        for (const pattern of includePatterns) {\n            if (path.isAbsolute(pattern) || pattern.includes(\"..\")) {\n                throw new Error(`Invalid override pattern (expected relative path not containing '..'): ${pattern}`);\n            }\n            if (pattern.endsWith(\"*\")) {\n                endsWithWildcard = true;\n            }\n        }\n        for (const pattern of excludePatterns) {\n            if (path.isAbsolute(pattern) || pattern.includes(\"..\")) {\n                throw new Error(`Invalid override pattern (expected relative path not containing '..'): ${pattern}`);\n            }\n        }\n\n        const includes = toMatcher(includePatterns);\n        const excludes = toMatcher(excludePatterns);\n\n        return new OverrideTester(\n            [{ includes, excludes }],\n            basePath,\n            endsWithWildcard\n        );\n    }\n\n    /**\n     * Combine two testers by logical and.\n     * If either of the testers was `null`, returns the other tester.\n     * The `basePath` property of the two must be the same value.\n     * @param {OverrideTester|null} a A tester.\n     * @param {OverrideTester|null} b Another tester.\n     * @returns {OverrideTester|null} Combined tester.\n     */\n    static and(a, b) {\n        if (!b) {\n            return a && new OverrideTester(\n                a.patterns,\n                a.basePath,\n                a.endsWithWildcard\n            );\n        }\n        if (!a) {\n            return new OverrideTester(\n                b.patterns,\n                b.basePath,\n                b.endsWithWildcard\n            );\n        }\n\n        assert.strictEqual(a.basePath, b.basePath);\n        return new OverrideTester(\n            a.patterns.concat(b.patterns),\n            a.basePath,\n            a.endsWithWildcard || b.endsWithWildcard\n        );\n    }\n\n    /**\n     * Initialize this instance.\n     * @param {Pattern[]} patterns The matchers.\n     * @param {string} basePath The base path.\n     * @param {boolean} endsWithWildcard If `true` then a pattern ends with `*`.\n     */\n    constructor(patterns, basePath, endsWithWildcard = false) {\n\n        /** @type {Pattern[]} */\n        this.patterns = patterns;\n\n        /** @type {string} */\n        this.basePath = basePath;\n\n        /** @type {boolean} */\n        this.endsWithWildcard = endsWithWildcard;\n    }\n\n    /**\n     * Test if a given path is matched or not.\n     * @param {string} filePath The absolute path to the target file.\n     * @returns {boolean} `true` if the path was matched.\n     * @throws {Error} When invalid `filePath` is given.\n     */\n    test(filePath) {\n        if (typeof filePath !== \"string\" || !path.isAbsolute(filePath)) {\n            throw new Error(`'filePath' should be an absolute path, but got ${filePath}.`);\n        }\n        const relativePath = path.relative(this.basePath, filePath);\n\n        return this.patterns.every(({ includes, excludes }) => (\n            (!includes || includes.some(m => m.match(relativePath))) &&\n            (!excludes || !excludes.some(m => m.match(relativePath)))\n        ));\n    }\n\n    /**\n     * Converts this instance to a JSON compatible object.\n     * @returns {Object} a JSON compatible object.\n     */\n    toJSON() {\n        if (this.patterns.length === 1) {\n            return {\n                ...patternToJson(this.patterns[0]),\n                basePath: this.basePath\n            };\n        }\n        return {\n            AND: this.patterns.map(patternToJson),\n            basePath: this.basePath\n        };\n    }\n\n    /**\n     * Custom inspect method for Node.js `console.log()`.\n     * @returns {Object} an object to display by `console.log()`.\n     */\n    [util.inspect.custom]() {\n        return this.toJSON();\n    }\n}\n\nexport { OverrideTester };\n", "/**\n * @fileoverview `ConfigArray` class.\n * <AUTHOR> <https://github.com/mysticatea>\n */\n\nimport { ConfigArray, getUsedExtractedConfigs } from \"./config-array.js\";\nimport { ConfigDependency } from \"./config-dependency.js\";\nimport { ExtractedConfig } from \"./extracted-config.js\";\nimport { IgnorePattern } from \"./ignore-pattern.js\";\nimport { OverrideTester } from \"./override-tester.js\";\n\nexport {\n    ConfigArray,\n    ConfigDependency,\n    ExtractedConfig,\n    IgnorePattern,\n    OverrideTester,\n    getUsedExtractedConfigs\n};\n", "/**\n * @fileoverview Config file operations. This file must be usable in the browser,\n * so no Node-specific code can be here.\n * <AUTHOR>\n */\n\n//------------------------------------------------------------------------------\n// Private\n//------------------------------------------------------------------------------\n\nconst RULE_SEVERITY_STRINGS = [\"off\", \"warn\", \"error\"],\n    RULE_SEVERITY = RULE_SEVERITY_STRINGS.reduce((map, value, index) => {\n        map[value] = index;\n        return map;\n    }, {}),\n    VALID_SEVERITIES = new Set([0, 1, 2, \"off\", \"warn\", \"error\"]);\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\n/**\n * Normalizes the severity value of a rule's configuration to a number\n * @param {(number|string|[number, ...*]|[string, ...*])} ruleConfig A rule's configuration value, generally\n * received from the user. A valid config value is either 0, 1, 2, the string \"off\" (treated the same as 0),\n * the string \"warn\" (treated the same as 1), the string \"error\" (treated the same as 2), or an array\n * whose first element is one of the above values. Strings are matched case-insensitively.\n * @returns {(0|1|2)} The numeric severity value if the config value was valid, otherwise 0.\n */\nfunction getRuleSeverity(ruleConfig) {\n    const severityValue = Array.isArray(ruleConfig) ? ruleConfig[0] : ruleConfig;\n\n    if (severityValue === 0 || severityValue === 1 || severityValue === 2) {\n        return severityValue;\n    }\n\n    if (typeof severityValue === \"string\") {\n        return RULE_SEVERITY[severityValue.toLowerCase()] || 0;\n    }\n\n    return 0;\n}\n\n/**\n * Converts old-style severity settings (0, 1, 2) into new-style\n * severity settings (off, warn, error) for all rules. Assumption is that severity\n * values have already been validated as correct.\n * @param {Object} config The config object to normalize.\n * @returns {void}\n */\nfunction normalizeToStrings(config) {\n\n    if (config.rules) {\n        Object.keys(config.rules).forEach(ruleId => {\n            const ruleConfig = config.rules[ruleId];\n\n            if (typeof ruleConfig === \"number\") {\n                config.rules[ruleId] = RULE_SEVERITY_STRINGS[ruleConfig] || RULE_SEVERITY_STRINGS[0];\n            } else if (Array.isArray(ruleConfig) && typeof ruleConfig[0] === \"number\") {\n                ruleConfig[0] = RULE_SEVERITY_STRINGS[ruleConfig[0]] || RULE_SEVERITY_STRINGS[0];\n            }\n        });\n    }\n}\n\n/**\n * Determines if the severity for the given rule configuration represents an error.\n * @param {int|string|Array} ruleConfig The configuration for an individual rule.\n * @returns {boolean} True if the rule represents an error, false if not.\n */\nfunction isErrorSeverity(ruleConfig) {\n    return getRuleSeverity(ruleConfig) === 2;\n}\n\n/**\n * Checks whether a given config has valid severity or not.\n * @param {number|string|Array} ruleConfig The configuration for an individual rule.\n * @returns {boolean} `true` if the configuration has valid severity.\n */\nfunction isValidSeverity(ruleConfig) {\n    let severity = Array.isArray(ruleConfig) ? ruleConfig[0] : ruleConfig;\n\n    if (typeof severity === \"string\") {\n        severity = severity.toLowerCase();\n    }\n    return VALID_SEVERITIES.has(severity);\n}\n\n/**\n * Checks whether every rule of a given config has valid severity or not.\n * @param {Object} config The configuration for rules.\n * @returns {boolean} `true` if the configuration has valid severity.\n */\nfunction isEverySeverityValid(config) {\n    return Object.keys(config).every(ruleId => isValidSeverity(config[ruleId]));\n}\n\n/**\n * Normalizes a value for a global in a config\n * @param {(boolean|string|null)} configuredValue The value given for a global in configuration or in\n * a global directive comment\n * @returns {(\"readable\"|\"writeable\"|\"off\")} The value normalized as a string\n * @throws Error if global value is invalid\n */\nfunction normalizeConfigGlobal(configuredValue) {\n    switch (configuredValue) {\n        case \"off\":\n            return \"off\";\n\n        case true:\n        case \"true\":\n        case \"writeable\":\n        case \"writable\":\n            return \"writable\";\n\n        case null:\n        case false:\n        case \"false\":\n        case \"readable\":\n        case \"readonly\":\n            return \"readonly\";\n\n        default:\n            throw new Error(`'${configuredValue}' is not a valid configuration for a global (use 'readonly', 'writable', or 'off')`);\n    }\n}\n\nexport {\n    getRuleSeverity,\n    normalizeToStrings,\n    isErrorSeverity,\n    isValidSeverity,\n    isEverySeverityValid,\n    normalizeConfigGlobal\n};\n", "/**\n * @fileoverview Provide the function that emits deprecation warnings.\n * <AUTHOR> <http://github.com/mysticatea>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport path from \"node:path\";\n\n//------------------------------------------------------------------------------\n// Private\n//------------------------------------------------------------------------------\n\n// Defitions for deprecation warnings.\nconst deprecationWarningMessages = {\n    ESLINT_LEGACY_ECMAFEATURES:\n        \"The 'ecmaFeatures' config file property is deprecated and has no effect.\",\n    ESLINT_PERSONAL_CONFIG_LOAD:\n        \"'~/.eslintrc.*' config files have been deprecated. \" +\n        \"Please use a config file per project or the '--config' option.\",\n    ESLINT_PERSONAL_CONFIG_SUPPRESS:\n        \"'~/.eslintrc.*' config files have been deprecated. \" +\n        \"Please remove it or add 'root:true' to the config files in your \" +\n        \"projects in order to avoid loading '~/.eslintrc.*' accidentally.\"\n};\n\nconst sourceFileErrorCache = new Set();\n\n/**\n * Emits a deprecation warning containing a given filepath. A new deprecation warning is emitted\n * for each unique file path, but repeated invocations with the same file path have no effect.\n * No warnings are emitted if the `--no-deprecation` or `--no-warnings` Node runtime flags are active.\n * @param {string} source The name of the configuration source to report the warning for.\n * @param {string} errorCode The warning message to show.\n * @returns {void}\n */\nfunction emitDeprecationWarning(source, errorCode) {\n    const cacheKey = JSON.stringify({ source, errorCode });\n\n    if (sourceFileErrorCache.has(cacheKey)) {\n        return;\n    }\n    sourceFileErrorCache.add(cacheKey);\n\n    const rel = path.relative(process.cwd(), source);\n    const message = deprecationWarningMessages[errorCode];\n\n    process.emitWarning(\n        `${message} (found in \"${rel}\")`,\n        \"DeprecationWarning\",\n        errorCode\n    );\n}\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\nexport {\n    emitDeprecationWarning\n};\n", "/**\n * @fileoverview The instance of Ajv validator.\n * <AUTHOR> <PERSON>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport Ajv from \"ajv\";\n\n//-----------------------------------------------------------------------------\n// Helpers\n//-----------------------------------------------------------------------------\n\n/*\n * Copied from ajv/lib/refs/json-schema-draft-04.json\n * The MIT License (MIT)\n * Copyright (c) 2015-2017 Evgeny Poberezkin\n */\nconst metaSchema = {\n    id: \"http://json-schema.org/draft-04/schema#\",\n    $schema: \"http://json-schema.org/draft-04/schema#\",\n    description: \"Core schema meta-schema\",\n    definitions: {\n        schemaArray: {\n            type: \"array\",\n            minItems: 1,\n            items: { $ref: \"#\" }\n        },\n        positiveInteger: {\n            type: \"integer\",\n            minimum: 0\n        },\n        positiveIntegerDefault0: {\n            allOf: [{ $ref: \"#/definitions/positiveInteger\" }, { default: 0 }]\n        },\n        simpleTypes: {\n            enum: [\"array\", \"boolean\", \"integer\", \"null\", \"number\", \"object\", \"string\"]\n        },\n        stringArray: {\n            type: \"array\",\n            items: { type: \"string\" },\n            minItems: 1,\n            uniqueItems: true\n        }\n    },\n    type: \"object\",\n    properties: {\n        id: {\n            type: \"string\"\n        },\n        $schema: {\n            type: \"string\"\n        },\n        title: {\n            type: \"string\"\n        },\n        description: {\n            type: \"string\"\n        },\n        default: { },\n        multipleOf: {\n            type: \"number\",\n            minimum: 0,\n            exclusiveMinimum: true\n        },\n        maximum: {\n            type: \"number\"\n        },\n        exclusiveMaximum: {\n            type: \"boolean\",\n            default: false\n        },\n        minimum: {\n            type: \"number\"\n        },\n        exclusiveMinimum: {\n            type: \"boolean\",\n            default: false\n        },\n        maxLength: { $ref: \"#/definitions/positiveInteger\" },\n        minLength: { $ref: \"#/definitions/positiveIntegerDefault0\" },\n        pattern: {\n            type: \"string\",\n            format: \"regex\"\n        },\n        additionalItems: {\n            anyOf: [\n                { type: \"boolean\" },\n                { $ref: \"#\" }\n            ],\n            default: { }\n        },\n        items: {\n            anyOf: [\n                { $ref: \"#\" },\n                { $ref: \"#/definitions/schemaArray\" }\n            ],\n            default: { }\n        },\n        maxItems: { $ref: \"#/definitions/positiveInteger\" },\n        minItems: { $ref: \"#/definitions/positiveIntegerDefault0\" },\n        uniqueItems: {\n            type: \"boolean\",\n            default: false\n        },\n        maxProperties: { $ref: \"#/definitions/positiveInteger\" },\n        minProperties: { $ref: \"#/definitions/positiveIntegerDefault0\" },\n        required: { $ref: \"#/definitions/stringArray\" },\n        additionalProperties: {\n            anyOf: [\n                { type: \"boolean\" },\n                { $ref: \"#\" }\n            ],\n            default: { }\n        },\n        definitions: {\n            type: \"object\",\n            additionalProperties: { $ref: \"#\" },\n            default: { }\n        },\n        properties: {\n            type: \"object\",\n            additionalProperties: { $ref: \"#\" },\n            default: { }\n        },\n        patternProperties: {\n            type: \"object\",\n            additionalProperties: { $ref: \"#\" },\n            default: { }\n        },\n        dependencies: {\n            type: \"object\",\n            additionalProperties: {\n                anyOf: [\n                    { $ref: \"#\" },\n                    { $ref: \"#/definitions/stringArray\" }\n                ]\n            }\n        },\n        enum: {\n            type: \"array\",\n            minItems: 1,\n            uniqueItems: true\n        },\n        type: {\n            anyOf: [\n                { $ref: \"#/definitions/simpleTypes\" },\n                {\n                    type: \"array\",\n                    items: { $ref: \"#/definitions/simpleTypes\" },\n                    minItems: 1,\n                    uniqueItems: true\n                }\n            ]\n        },\n        format: { type: \"string\" },\n        allOf: { $ref: \"#/definitions/schemaArray\" },\n        anyOf: { $ref: \"#/definitions/schemaArray\" },\n        oneOf: { $ref: \"#/definitions/schemaArray\" },\n        not: { $ref: \"#\" }\n    },\n    dependencies: {\n        exclusiveMaximum: [\"maximum\"],\n        exclusiveMinimum: [\"minimum\"]\n    },\n    default: { }\n};\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\nexport default (additionalOptions = {}) => {\n    const ajv = new Ajv({\n        meta: false,\n        useDefaults: true,\n        validateSchema: false,\n        missingRefs: \"ignore\",\n        verbose: true,\n        schemaId: \"auto\",\n        ...additionalOptions\n    });\n\n    ajv.addMetaSchema(metaSchema);\n    // eslint-disable-next-line no-underscore-dangle -- part of the API\n    ajv._opts.defaultMeta = metaSchema.id;\n\n    return ajv;\n};\n", "/**\n * @fileoverview Applies default rule options\n * <AUTHOR>\n */\n\n/**\n * Check if the variable contains an object strictly rejecting arrays\n * @param {unknown} value an object\n * @returns {boolean} Whether value is an object\n */\nfunction isObjectNotArray(value) {\n    return typeof value === \"object\" && value !== null && !Array.isArray(value);\n}\n\n/**\n * Deeply merges second on top of first, creating a new {} object if needed.\n * @param {T} first Base, default value.\n * @param {U} second User-specified value.\n * @returns {T | U | (T & U)} Merged equivalent of second on top of first.\n */\nfunction deepMergeObjects(first, second) {\n    if (second === void 0) {\n        return first;\n    }\n\n    if (!isObjectNotArray(first) || !isObjectNotArray(second)) {\n        return second;\n    }\n\n    const result = { ...first, ...second };\n\n    for (const key of Object.keys(second)) {\n        if (Object.prototype.propertyIsEnumerable.call(first, key)) {\n            result[key] = deepMergeObjects(first[key], second[key]);\n        }\n    }\n\n    return result;\n}\n\n/**\n * Deeply merges second on top of first, creating a new [] array if needed.\n * @param {T[] | undefined} first Base, default values.\n * @param {U[] | undefined} second User-specified values.\n * @returns {(T | U | (T & U))[]} Merged equivalent of second on top of first.\n */\nfunction deepMergeArrays(first, second) {\n    if (!first || !second) {\n        return second || first || [];\n    }\n\n    return [\n        ...first.map((value, i) => deepMergeObjects(value, second[i])),\n        ...second.slice(first.length)\n    ];\n}\n\nexport { deepMergeArrays };\n", "/**\n * @fileoverview Defines a schema for configs.\n * <AUTHOR>\n */\n\nconst baseConfigProperties = {\n    $schema: { type: \"string\" },\n    env: { type: \"object\" },\n    extends: { $ref: \"#/definitions/stringOrStrings\" },\n    globals: { type: \"object\" },\n    overrides: {\n        type: \"array\",\n        items: { $ref: \"#/definitions/overrideConfig\" },\n        additionalItems: false\n    },\n    parser: { type: [\"string\", \"null\"] },\n    parserOptions: { type: \"object\" },\n    plugins: { type: \"array\" },\n    processor: { type: \"string\" },\n    rules: { type: \"object\" },\n    settings: { type: \"object\" },\n    noInlineConfig: { type: \"boolean\" },\n    reportUnusedDisableDirectives: { type: \"boolean\" },\n\n    ecmaFeatures: { type: \"object\" } // deprecated; logs a warning when used\n};\n\nconst configSchema = {\n    definitions: {\n        stringOrStrings: {\n            oneOf: [\n                { type: \"string\" },\n                {\n                    type: \"array\",\n                    items: { type: \"string\" },\n                    additionalItems: false\n                }\n            ]\n        },\n        stringOrStringsRequired: {\n            oneOf: [\n                { type: \"string\" },\n                {\n                    type: \"array\",\n                    items: { type: \"string\" },\n                    additionalItems: false,\n                    minItems: 1\n                }\n            ]\n        },\n\n        // Config at top-level.\n        objectConfig: {\n            type: \"object\",\n            properties: {\n                root: { type: \"boolean\" },\n                ignorePatterns: { $ref: \"#/definitions/stringOrStrings\" },\n                ...baseConfigProperties\n            },\n            additionalProperties: false\n        },\n\n        // Config in `overrides`.\n        overrideConfig: {\n            type: \"object\",\n            properties: {\n                excludedFiles: { $ref: \"#/definitions/stringOrStrings\" },\n                files: { $ref: \"#/definitions/stringOrStringsRequired\" },\n                ...baseConfigProperties\n            },\n            required: [\"files\"],\n            additionalProperties: false\n        }\n    },\n\n    $ref: \"#/definitions/objectConfig\"\n};\n\nexport default configSchema;\n", "/**\n * @fileoverview Defines environment settings and globals.\n * <AUTHOR>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport globals from \"globals\";\n\n//------------------------------------------------------------------------------\n// Helpers\n//------------------------------------------------------------------------------\n\n/**\n * Get the object that has difference.\n * @param {Record<string,boolean>} current The newer object.\n * @param {Record<string,boolean>} prev The older object.\n * @returns {Record<string,boolean>} The difference object.\n */\nfunction getDiff(current, prev) {\n    const retv = {};\n\n    for (const [key, value] of Object.entries(current)) {\n        if (!Object.hasOwn(prev, key)) {\n            retv[key] = value;\n        }\n    }\n\n    return retv;\n}\n\nconst newGlobals2015 = getDiff(globals.es2015, globals.es5); // 19 variables such as Promise, Map, ...\nconst newGlobals2017 = {\n    Atomics: false,\n    SharedArrayBuffer: false\n};\nconst newGlobals2020 = {\n    BigInt: false,\n    BigInt64Array: false,\n    BigUint64Array: false,\n    globalThis: false\n};\n\nconst newGlobals2021 = {\n    AggregateError: false,\n    FinalizationRegistry: false,\n    WeakRef: false\n};\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\n/** @type {Map<string, import(\"../lib/shared/types\").Environment>} */\nexport default new Map(Object.entries({\n\n    // Language\n    builtin: {\n        globals: globals.es5\n    },\n    es6: {\n        globals: newGlobals2015,\n        parserOptions: {\n            ecmaVersion: 6\n        }\n    },\n    es2015: {\n        globals: newGlobals2015,\n        parserOptions: {\n            ecmaVersion: 6\n        }\n    },\n    es2016: {\n        globals: newGlobals2015,\n        parserOptions: {\n            ecmaVersion: 7\n        }\n    },\n    es2017: {\n        globals: { ...newGlobals2015, ...newGlobals2017 },\n        parserOptions: {\n            ecmaVersion: 8\n        }\n    },\n    es2018: {\n        globals: { ...newGlobals2015, ...newGlobals2017 },\n        parserOptions: {\n            ecmaVersion: 9\n        }\n    },\n    es2019: {\n        globals: { ...newGlobals2015, ...newGlobals2017 },\n        parserOptions: {\n            ecmaVersion: 10\n        }\n    },\n    es2020: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020 },\n        parserOptions: {\n            ecmaVersion: 11\n        }\n    },\n    es2021: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020, ...newGlobals2021 },\n        parserOptions: {\n            ecmaVersion: 12\n        }\n    },\n    es2022: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020, ...newGlobals2021 },\n        parserOptions: {\n            ecmaVersion: 13\n        }\n    },\n    es2023: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020, ...newGlobals2021 },\n        parserOptions: {\n            ecmaVersion: 14\n        }\n    },\n    es2024: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020, ...newGlobals2021 },\n        parserOptions: {\n            ecmaVersion: 15\n        }\n    },\n\n    // Platforms\n    browser: {\n        globals: globals.browser\n    },\n    node: {\n        globals: globals.node,\n        parserOptions: {\n            ecmaFeatures: {\n                globalReturn: true\n            }\n        }\n    },\n    \"shared-node-browser\": {\n        globals: globals[\"shared-node-browser\"]\n    },\n    worker: {\n        globals: globals.worker\n    },\n    serviceworker: {\n        globals: globals.serviceworker\n    },\n\n    // Frameworks\n    commonjs: {\n        globals: globals.commonjs,\n        parserOptions: {\n            ecmaFeatures: {\n                globalReturn: true\n            }\n        }\n    },\n    amd: {\n        globals: globals.amd\n    },\n    mocha: {\n        globals: globals.mocha\n    },\n    jasmine: {\n        globals: globals.jasmine\n    },\n    jest: {\n        globals: globals.jest\n    },\n    phantomjs: {\n        globals: globals.phantomjs\n    },\n    jquery: {\n        globals: globals.jquery\n    },\n    qunit: {\n        globals: globals.qunit\n    },\n    prototypejs: {\n        globals: globals.prototypejs\n    },\n    shelljs: {\n        globals: globals.shelljs\n    },\n    meteor: {\n        globals: globals.meteor\n    },\n    mongo: {\n        globals: globals.mongo\n    },\n    protractor: {\n        globals: globals.protractor\n    },\n    applescript: {\n        globals: globals.applescript\n    },\n    nashorn: {\n        globals: globals.nashorn\n    },\n    atomtest: {\n        globals: globals.atomtest\n    },\n    embertest: {\n        globals: globals.embertest\n    },\n    webextensions: {\n        globals: globals.webextensions\n    },\n    greasemonkey: {\n        globals: globals.greasemonkey\n    }\n}));\n", "/**\n * @fileoverview Validates configs.\n * <AUTHOR>\n */\n\n/* eslint class-methods-use-this: \"off\" -- not needed in this file */\n\n//------------------------------------------------------------------------------\n// Typedefs\n//------------------------------------------------------------------------------\n\n/** @typedef {import(\"../shared/types\").Rule} Rule */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport util from \"node:util\";\nimport * as ConfigOps from \"./config-ops.js\";\nimport { emitDeprecationWarning } from \"./deprecation-warnings.js\";\nimport ajvOrig from \"./ajv.js\";\nimport { deepMergeArrays } from \"./deep-merge-arrays.js\";\nimport configSchema from \"../../conf/config-schema.js\";\nimport BuiltInEnvironments from \"../../conf/environments.js\";\n\nconst ajv = ajvOrig();\n\nconst ruleValidators = new WeakMap();\nconst noop = Function.prototype;\n\n//------------------------------------------------------------------------------\n// Private\n//------------------------------------------------------------------------------\nlet validateSchema;\nconst severityMap = {\n    error: 2,\n    warn: 1,\n    off: 0\n};\n\nconst validated = new WeakSet();\n\n// JSON schema that disallows passing any options\nconst noOptionsSchema = Object.freeze({\n    type: \"array\",\n    minItems: 0,\n    maxItems: 0\n});\n\n//-----------------------------------------------------------------------------\n// Exports\n//-----------------------------------------------------------------------------\n\n/**\n * Validator for configuration objects.\n */\nexport default class ConfigValidator {\n    constructor({ builtInRules = new Map() } = {}) {\n        this.builtInRules = builtInRules;\n    }\n\n    /**\n     * Gets a complete options schema for a rule.\n     * @param {Rule} rule A rule object\n     * @throws {TypeError} If `meta.schema` is specified but is not an array, object or `false`.\n     * @returns {Object|null} JSON Schema for the rule's options.\n     *      `null` if rule wasn't passed or its `meta.schema` is `false`.\n     */\n    getRuleOptionsSchema(rule) {\n        if (!rule) {\n            return null;\n        }\n\n        if (!rule.meta) {\n            return { ...noOptionsSchema }; // default if `meta.schema` is not specified\n        }\n\n        const schema = rule.meta.schema;\n\n        if (typeof schema === \"undefined\") {\n            return { ...noOptionsSchema }; // default if `meta.schema` is not specified\n        }\n\n        // `schema:false` is an allowed explicit opt-out of options validation for the rule\n        if (schema === false) {\n            return null;\n        }\n\n        if (typeof schema !== \"object\" || schema === null) {\n            throw new TypeError(\"Rule's `meta.schema` must be an array or object\");\n        }\n\n        // ESLint-specific array form needs to be converted into a valid JSON Schema definition\n        if (Array.isArray(schema)) {\n            if (schema.length) {\n                return {\n                    type: \"array\",\n                    items: schema,\n                    minItems: 0,\n                    maxItems: schema.length\n                };\n            }\n\n            // `schema:[]` is an explicit way to specify that the rule does not accept any options\n            return { ...noOptionsSchema };\n        }\n\n        // `schema:<object>` is assumed to be a valid JSON Schema definition\n        return schema;\n    }\n\n    /**\n     * Validates a rule's severity and returns the severity value. Throws an error if the severity is invalid.\n     * @param {options} options The given options for the rule.\n     * @returns {number|string} The rule's severity value\n     * @throws {Error} If the severity is invalid.\n     */\n    validateRuleSeverity(options) {\n        const severity = Array.isArray(options) ? options[0] : options;\n        const normSeverity = typeof severity === \"string\" ? severityMap[severity.toLowerCase()] : severity;\n\n        if (normSeverity === 0 || normSeverity === 1 || normSeverity === 2) {\n            return normSeverity;\n        }\n\n        throw new Error(`\\tSeverity should be one of the following: 0 = off, 1 = warn, 2 = error (you passed '${util.inspect(severity).replace(/'/gu, \"\\\"\").replace(/\\n/gu, \"\")}').\\n`);\n\n    }\n\n    /**\n     * Validates the non-severity options passed to a rule, based on its schema.\n     * @param {{create: Function}} rule The rule to validate\n     * @param {Array} localOptions The options for the rule, excluding severity\n     * @returns {void}\n     * @throws {Error} If the options are invalid.\n     */\n    validateRuleSchema(rule, localOptions) {\n        if (!ruleValidators.has(rule)) {\n            try {\n                const schema = this.getRuleOptionsSchema(rule);\n\n                if (schema) {\n                    ruleValidators.set(rule, ajv.compile(schema));\n                }\n            } catch (err) {\n                const errorWithCode = new Error(err.message, { cause: err });\n\n                errorWithCode.code = \"ESLINT_INVALID_RULE_OPTIONS_SCHEMA\";\n\n                throw errorWithCode;\n            }\n        }\n\n        const validateRule = ruleValidators.get(rule);\n\n        if (validateRule) {\n            const mergedOptions = deepMergeArrays(rule.meta?.defaultOptions, localOptions);\n\n            validateRule(mergedOptions);\n\n            if (validateRule.errors) {\n                throw new Error(validateRule.errors.map(\n                    error => `\\tValue ${JSON.stringify(error.data)} ${error.message}.\\n`\n                ).join(\"\"));\n            }\n        }\n    }\n\n    /**\n     * Validates a rule's options against its schema.\n     * @param {{create: Function}|null} rule The rule that the config is being validated for\n     * @param {string} ruleId The rule's unique name.\n     * @param {Array|number} options The given options for the rule.\n     * @param {string|null} source The name of the configuration source to report in any errors. If null or undefined,\n     * no source is prepended to the message.\n     * @returns {void}\n     * @throws {Error} If the options are invalid.\n     */\n    validateRuleOptions(rule, ruleId, options, source = null) {\n        try {\n            const severity = this.validateRuleSeverity(options);\n\n            if (severity !== 0) {\n                this.validateRuleSchema(rule, Array.isArray(options) ? options.slice(1) : []);\n            }\n        } catch (err) {\n            let enhancedMessage = err.code === \"ESLINT_INVALID_RULE_OPTIONS_SCHEMA\"\n                ? `Error while processing options validation schema of rule '${ruleId}': ${err.message}`\n                : `Configuration for rule \"${ruleId}\" is invalid:\\n${err.message}`;\n\n            if (typeof source === \"string\") {\n                enhancedMessage = `${source}:\\n\\t${enhancedMessage}`;\n            }\n\n            const enhancedError = new Error(enhancedMessage, { cause: err });\n\n            if (err.code) {\n                enhancedError.code = err.code;\n            }\n\n            throw enhancedError;\n        }\n    }\n\n    /**\n     * Validates an environment object\n     * @param {Object} environment The environment config object to validate.\n     * @param {string} source The name of the configuration source to report in any errors.\n     * @param {(envId:string) => Object} [getAdditionalEnv] A map from strings to loaded environments.\n     * @returns {void}\n     * @throws {Error} If the environment is invalid.\n     */\n    validateEnvironment(\n        environment,\n        source,\n        getAdditionalEnv = noop\n    ) {\n\n        // not having an environment is ok\n        if (!environment) {\n            return;\n        }\n\n        Object.keys(environment).forEach(id => {\n            const env = getAdditionalEnv(id) || BuiltInEnvironments.get(id) || null;\n\n            if (!env) {\n                const message = `${source}:\\n\\tEnvironment key \"${id}\" is unknown\\n`;\n\n                throw new Error(message);\n            }\n        });\n    }\n\n    /**\n     * Validates a rules config object\n     * @param {Object} rulesConfig The rules config object to validate.\n     * @param {string} source The name of the configuration source to report in any errors.\n     * @param {(ruleId:string) => Object} getAdditionalRule A map from strings to loaded rules\n     * @returns {void}\n     */\n    validateRules(\n        rulesConfig,\n        source,\n        getAdditionalRule = noop\n    ) {\n        if (!rulesConfig) {\n            return;\n        }\n\n        Object.keys(rulesConfig).forEach(id => {\n            const rule = getAdditionalRule(id) || this.builtInRules.get(id) || null;\n\n            this.validateRuleOptions(rule, id, rulesConfig[id], source);\n        });\n    }\n\n    /**\n     * Validates a `globals` section of a config file\n     * @param {Object} globalsConfig The `globals` section\n     * @param {string|null} source The name of the configuration source to report in the event of an error.\n     * @returns {void}\n     */\n    validateGlobals(globalsConfig, source = null) {\n        if (!globalsConfig) {\n            return;\n        }\n\n        Object.entries(globalsConfig)\n            .forEach(([configuredGlobal, configuredValue]) => {\n                try {\n                    ConfigOps.normalizeConfigGlobal(configuredValue);\n                } catch (err) {\n                    throw new Error(`ESLint configuration of global '${configuredGlobal}' in ${source} is invalid:\\n${err.message}`);\n                }\n            });\n    }\n\n    /**\n     * Validate `processor` configuration.\n     * @param {string|undefined} processorName The processor name.\n     * @param {string} source The name of config file.\n     * @param {(id:string) => Processor} getProcessor The getter of defined processors.\n     * @returns {void}\n     * @throws {Error} If the processor is invalid.\n     */\n    validateProcessor(processorName, source, getProcessor) {\n        if (processorName && !getProcessor(processorName)) {\n            throw new Error(`ESLint configuration of processor in '${source}' is invalid: '${processorName}' was not found.`);\n        }\n    }\n\n    /**\n     * Formats an array of schema validation errors.\n     * @param {Array} errors An array of error messages to format.\n     * @returns {string} Formatted error message\n     */\n    formatErrors(errors) {\n        return errors.map(error => {\n            if (error.keyword === \"additionalProperties\") {\n                const formattedPropertyPath = error.dataPath.length ? `${error.dataPath.slice(1)}.${error.params.additionalProperty}` : error.params.additionalProperty;\n\n                return `Unexpected top-level property \"${formattedPropertyPath}\"`;\n            }\n            if (error.keyword === \"type\") {\n                const formattedField = error.dataPath.slice(1);\n                const formattedExpectedType = Array.isArray(error.schema) ? error.schema.join(\"/\") : error.schema;\n                const formattedValue = JSON.stringify(error.data);\n\n                return `Property \"${formattedField}\" is the wrong type (expected ${formattedExpectedType} but got \\`${formattedValue}\\`)`;\n            }\n\n            const field = error.dataPath[0] === \".\" ? error.dataPath.slice(1) : error.dataPath;\n\n            return `\"${field}\" ${error.message}. Value: ${JSON.stringify(error.data)}`;\n        }).map(message => `\\t- ${message}.\\n`).join(\"\");\n    }\n\n    /**\n     * Validates the top level properties of the config object.\n     * @param {Object} config The config object to validate.\n     * @param {string} source The name of the configuration source to report in any errors.\n     * @returns {void}\n     * @throws {Error} If the config is invalid.\n     */\n    validateConfigSchema(config, source = null) {\n        validateSchema = validateSchema || ajv.compile(configSchema);\n\n        if (!validateSchema(config)) {\n            throw new Error(`ESLint configuration in ${source} is invalid:\\n${this.formatErrors(validateSchema.errors)}`);\n        }\n\n        if (Object.hasOwn(config, \"ecmaFeatures\")) {\n            emitDeprecationWarning(source, \"ESLINT_LEGACY_ECMAFEATURES\");\n        }\n    }\n\n    /**\n     * Validates an entire config object.\n     * @param {Object} config The config object to validate.\n     * @param {string} source The name of the configuration source to report in any errors.\n     * @param {(ruleId:string) => Object} [getAdditionalRule] A map from strings to loaded rules.\n     * @param {(envId:string) => Object} [getAdditionalEnv] A map from strings to loaded envs.\n     * @returns {void}\n     */\n    validate(config, source, getAdditionalRule, getAdditionalEnv) {\n        this.validateConfigSchema(config, source);\n        this.validateRules(config.rules, source, getAdditionalRule);\n        this.validateEnvironment(config.env, source, getAdditionalEnv);\n        this.validateGlobals(config.globals, source);\n\n        for (const override of config.overrides || []) {\n            this.validateRules(override.rules, source, getAdditionalRule);\n            this.validateEnvironment(override.env, source, getAdditionalEnv);\n            this.validateGlobals(config.globals, source);\n        }\n    }\n\n    /**\n     * Validate config array object.\n     * @param {ConfigArray} configArray The config array to validate.\n     * @returns {void}\n     */\n    validateConfigArray(configArray) {\n        const getPluginEnv = Map.prototype.get.bind(configArray.pluginEnvironments);\n        const getPluginProcessor = Map.prototype.get.bind(configArray.pluginProcessors);\n        const getPluginRule = Map.prototype.get.bind(configArray.pluginRules);\n\n        // Validate.\n        for (const element of configArray) {\n            if (validated.has(element)) {\n                continue;\n            }\n            validated.add(element);\n\n            this.validateEnvironment(element.env, element.name, getPluginEnv);\n            this.validateGlobals(element.globals, element.name);\n            this.validateProcessor(element.processor, element.name, getPluginProcessor);\n            this.validateRules(element.rules, element.name, getPluginRule);\n        }\n    }\n\n}\n", "/**\n * @fileoverview Common helpers for naming of plugins, formatters and configs\n */\n\nconst NAMESPACE_REGEX = /^@.*\\//iu;\n\n/**\n * Brings package name to correct format based on prefix\n * @param {string} name The name of the package.\n * @param {string} prefix Can be either \"eslint-plugin\", \"eslint-config\" or \"eslint-formatter\"\n * @returns {string} Normalized name of the package\n * @private\n */\nfunction normalizePackageName(name, prefix) {\n    let normalizedName = name;\n\n    /**\n     * On Windows, name can come in with Windows slashes instead of Unix slashes.\n     * Normalize to Unix first to avoid errors later on.\n     * https://github.com/eslint/eslint/issues/5644\n     */\n    if (normalizedName.includes(\"\\\\\")) {\n        normalizedName = normalizedName.replace(/\\\\/gu, \"/\");\n    }\n\n    if (normalizedName.charAt(0) === \"@\") {\n\n        /**\n         * it's a scoped package\n         * package name is the prefix, or just a username\n         */\n        const scopedPackageShortcutRegex = new RegExp(`^(@[^/]+)(?:/(?:${prefix})?)?$`, \"u\"),\n            scopedPackageNameRegex = new RegExp(`^${prefix}(-|$)`, \"u\");\n\n        if (scopedPackageShortcutRegex.test(normalizedName)) {\n            normalizedName = normalizedName.replace(scopedPackageShortcutRegex, `$1/${prefix}`);\n        } else if (!scopedPackageNameRegex.test(normalizedName.split(\"/\")[1])) {\n\n            /**\n             * for scoped packages, insert the prefix after the first / unless\n             * the path is already @scope/eslint or @scope/eslint-xxx-yyy\n             */\n            normalizedName = normalizedName.replace(/^@([^/]+)\\/(.*)$/u, `@$1/${prefix}-$2`);\n        }\n    } else if (!normalizedName.startsWith(`${prefix}-`)) {\n        normalizedName = `${prefix}-${normalizedName}`;\n    }\n\n    return normalizedName;\n}\n\n/**\n * Removes the prefix from a fullname.\n * @param {string} fullname The term which may have the prefix.\n * @param {string} prefix The prefix to remove.\n * @returns {string} The term without prefix.\n */\nfunction getShorthandName(fullname, prefix) {\n    if (fullname[0] === \"@\") {\n        let matchResult = new RegExp(`^(@[^/]+)/${prefix}$`, \"u\").exec(fullname);\n\n        if (matchResult) {\n            return matchResult[1];\n        }\n\n        matchResult = new RegExp(`^(@[^/]+)/${prefix}-(.+)$`, \"u\").exec(fullname);\n        if (matchResult) {\n            return `${matchResult[1]}/${matchResult[2]}`;\n        }\n    } else if (fullname.startsWith(`${prefix}-`)) {\n        return fullname.slice(prefix.length + 1);\n    }\n\n    return fullname;\n}\n\n/**\n * Gets the scope (namespace) of a term.\n * @param {string} term The term which may have the namespace.\n * @returns {string} The namespace of the term if it has one.\n */\nfunction getNamespaceFromTerm(term) {\n    const match = term.match(NAMESPACE_REGEX);\n\n    return match ? match[0] : \"\";\n}\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\nexport {\n    normalizePackageName,\n    getShorthandName,\n    getNamespaceFromTerm\n};\n", "/**\n * Utility for resolving a module relative to another module\n * <AUTHOR>\n */\n\nimport Module from \"node:module\";\n\n/*\n * `Module.createRequire` is added in v12.2.0. It supports URL as well.\n * We only support the case where the argument is a filepath, not a URL.\n */\nconst createRequire = Module.createRequire;\n\n/**\n * Resolves a Node module relative to another module\n * @param {string} moduleName The name of a Node module, or a path to a Node module.\n * @param {string} relativeToPath An absolute path indicating the module that `moduleName` should be resolved relative to. This must be\n * a file rather than a directory, but the file need not actually exist.\n * @returns {string} The absolute path that would result from calling `require.resolve(moduleName)` in a file located at `relativeToPath`\n * @throws {Error} When the module cannot be resolved.\n */\nfunction resolve(moduleName, relativeToPath) {\n    try {\n        return createRequire(relativeToPath).resolve(moduleName);\n    } catch (error) {\n\n        // This `if` block is for older Node.js than 12.0.0. We can remove this block in the future.\n        if (\n            typeof error === \"object\" &&\n            error !== null &&\n            error.code === \"MODULE_NOT_FOUND\" &&\n            !error.requireStack &&\n            error.message.includes(moduleName)\n        ) {\n            error.message += `\\nRequire stack:\\n- ${relativeToPath}`;\n        }\n        throw error;\n    }\n}\n\nexport {\n    resolve\n};\n", "/**\n * @fileoverview The factory of `ConfigArray` objects.\n *\n * This class provides methods to create `ConfigArray` instance.\n *\n * - `create(configData, options)`\n *     Create a `ConfigArray` instance from a config data. This is to handle CLI\n *     options except `--config`.\n * - `loadFile(filePath, options)`\n *     Create a `ConfigArray` instance from a config file. This is to handle\n *     `--config` option. If the file was not found, throws the following error:\n *      - If the filename was `*.js`, a `MODULE_NOT_FOUND` error.\n *      - If the filename was `package.json`, an IO error or an\n *        `ESLINT_CONFIG_FIELD_NOT_FOUND` error.\n *      - Otherwise, an IO error such as `ENOENT`.\n * - `loadInDirectory(directoryPath, options)`\n *     Create a `ConfigArray` instance from a config file which is on a given\n *     directory. This tries to load `.eslintrc.*` or `package.json`. If not\n *     found, returns an empty `ConfigArray`.\n * - `loadESLintIgnore(filePath)`\n *     Create a `ConfigArray` instance from a config file that is `.eslintignore`\n *     format. This is to handle `--ignore-path` option.\n * - `loadDefaultESLintIgnore()`\n *     Create a `ConfigArray` instance from `.eslintignore` or `package.json` in\n *     the current working directory.\n *\n * `ConfigArrayFactory` class has the responsibility that loads configuration\n * files, including loading `extends`, `parser`, and `plugins`. The created\n * `ConfigArray` instance has the loaded `extends`, `parser`, and `plugins`.\n *\n * But this class doesn't handle cascading. `CascadingConfigArrayFactory` class\n * handles cascading and hierarchy.\n *\n * <AUTHOR> Nagashima <https://github.com/mysticatea>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport debugOrig from \"debug\";\nimport fs from \"node:fs\";\nimport importFresh from \"import-fresh\";\nimport { createRequire } from \"node:module\";\nimport path from \"node:path\";\nimport stripComments from \"strip-json-comments\";\n\nimport {\n    ConfigArray,\n    ConfigDependency,\n    IgnorePattern,\n    OverrideTester\n} from \"./config-array/index.js\";\nimport ConfigValidator from \"./shared/config-validator.js\";\nimport * as naming from \"./shared/naming.js\";\nimport * as ModuleResolver from \"./shared/relative-module-resolver.js\";\n\nconst require = createRequire(import.meta.url);\n\nconst debug = debugOrig(\"eslintrc:config-array-factory\");\n\n//------------------------------------------------------------------------------\n// Helpers\n//------------------------------------------------------------------------------\n\nconst configFilenames = [\n    \".eslintrc.js\",\n    \".eslintrc.cjs\",\n    \".eslintrc.yaml\",\n    \".eslintrc.yml\",\n    \".eslintrc.json\",\n    \".eslintrc\",\n    \"package.json\"\n];\n\n// Define types for VSCode IntelliSense.\n/** @typedef {import(\"./shared/types\").ConfigData} ConfigData */\n/** @typedef {import(\"./shared/types\").OverrideConfigData} OverrideConfigData */\n/** @typedef {import(\"./shared/types\").Parser} Parser */\n/** @typedef {import(\"./shared/types\").Plugin} Plugin */\n/** @typedef {import(\"./shared/types\").Rule} Rule */\n/** @typedef {import(\"./config-array/config-dependency\").DependentParser} DependentParser */\n/** @typedef {import(\"./config-array/config-dependency\").DependentPlugin} DependentPlugin */\n/** @typedef {ConfigArray[0]} ConfigArrayElement */\n\n/**\n * @typedef {Object} ConfigArrayFactoryOptions\n * @property {Map<string,Plugin>} [additionalPluginPool] The map for additional plugins.\n * @property {string} [cwd] The path to the current working directory.\n * @property {string} [resolvePluginsRelativeTo] A path to the directory that plugins should be resolved from. Defaults to `cwd`.\n * @property {Map<string,Rule>} builtInRules The rules that are built in to ESLint.\n * @property {Object} [resolver=ModuleResolver] The module resolver object.\n * @property {string} eslintAllPath The path to the definitions for eslint:all.\n * @property {Function} getEslintAllConfig Returns the config data for eslint:all.\n * @property {string} eslintRecommendedPath The path to the definitions for eslint:recommended.\n * @property {Function} getEslintRecommendedConfig Returns the config data for eslint:recommended.\n */\n\n/**\n * @typedef {Object} ConfigArrayFactoryInternalSlots\n * @property {Map<string,Plugin>} additionalPluginPool The map for additional plugins.\n * @property {string} cwd The path to the current working directory.\n * @property {string | undefined} resolvePluginsRelativeTo An absolute path the the directory that plugins should be resolved from.\n * @property {Map<string,Rule>} builtInRules The rules that are built in to ESLint.\n * @property {Object} [resolver=ModuleResolver] The module resolver object.\n * @property {string} eslintAllPath The path to the definitions for eslint:all.\n * @property {Function} getEslintAllConfig Returns the config data for eslint:all.\n * @property {string} eslintRecommendedPath The path to the definitions for eslint:recommended.\n * @property {Function} getEslintRecommendedConfig Returns the config data for eslint:recommended.\n */\n\n/**\n * @typedef {Object} ConfigArrayFactoryLoadingContext\n * @property {string} filePath The path to the current configuration.\n * @property {string} matchBasePath The base path to resolve relative paths in `overrides[].files`, `overrides[].excludedFiles`, and `ignorePatterns`.\n * @property {string} name The name of the current configuration.\n * @property {string} pluginBasePath The base path to resolve plugins.\n * @property {\"config\" | \"ignore\" | \"implicit-processor\"} type The type of the current configuration. This is `\"config\"` in normal. This is `\"ignore\"` if it came from `.eslintignore`. This is `\"implicit-processor\"` if it came from legacy file-extension processors.\n */\n\n/**\n * @typedef {Object} ConfigArrayFactoryLoadingContext\n * @property {string} filePath The path to the current configuration.\n * @property {string} matchBasePath The base path to resolve relative paths in `overrides[].files`, `overrides[].excludedFiles`, and `ignorePatterns`.\n * @property {string} name The name of the current configuration.\n * @property {\"config\" | \"ignore\" | \"implicit-processor\"} type The type of the current configuration. This is `\"config\"` in normal. This is `\"ignore\"` if it came from `.eslintignore`. This is `\"implicit-processor\"` if it came from legacy file-extension processors.\n */\n\n/** @type {WeakMap<ConfigArrayFactory, ConfigArrayFactoryInternalSlots>} */\nconst internalSlotsMap = new WeakMap();\n\n/** @type {WeakMap<object, Plugin>} */\nconst normalizedPlugins = new WeakMap();\n\n/**\n * Check if a given string is a file path.\n * @param {string} nameOrPath A module name or file path.\n * @returns {boolean} `true` if the `nameOrPath` is a file path.\n */\nfunction isFilePath(nameOrPath) {\n    return (\n        /^\\.{1,2}[/\\\\]/u.test(nameOrPath) ||\n        path.isAbsolute(nameOrPath)\n    );\n}\n\n/**\n * Convenience wrapper for synchronously reading file contents.\n * @param {string} filePath The filename to read.\n * @returns {string} The file contents, with the BOM removed.\n * @private\n */\nfunction readFile(filePath) {\n    return fs.readFileSync(filePath, \"utf8\").replace(/^\\ufeff/u, \"\");\n}\n\n/**\n * Loads a YAML configuration from a file.\n * @param {string} filePath The filename to load.\n * @returns {ConfigData} The configuration object from the file.\n * @throws {Error} If the file cannot be read.\n * @private\n */\nfunction loadYAMLConfigFile(filePath) {\n    debug(`Loading YAML config file: ${filePath}`);\n\n    // lazy load YAML to improve performance when not used\n    const yaml = require(\"js-yaml\");\n\n    try {\n\n        // empty YAML file can be null, so always use\n        return yaml.load(readFile(filePath)) || {};\n    } catch (e) {\n        debug(`Error reading YAML file: ${filePath}`);\n        e.message = `Cannot read config file: ${filePath}\\nError: ${e.message}`;\n        throw e;\n    }\n}\n\n/**\n * Loads a JSON configuration from a file.\n * @param {string} filePath The filename to load.\n * @returns {ConfigData} The configuration object from the file.\n * @throws {Error} If the file cannot be read.\n * @private\n */\nfunction loadJSONConfigFile(filePath) {\n    debug(`Loading JSON config file: ${filePath}`);\n\n    try {\n        return JSON.parse(stripComments(readFile(filePath)));\n    } catch (e) {\n        debug(`Error reading JSON file: ${filePath}`);\n        e.message = `Cannot read config file: ${filePath}\\nError: ${e.message}`;\n        e.messageTemplate = \"failed-to-read-json\";\n        e.messageData = {\n            path: filePath,\n            message: e.message\n        };\n        throw e;\n    }\n}\n\n/**\n * Loads a legacy (.eslintrc) configuration from a file.\n * @param {string} filePath The filename to load.\n * @returns {ConfigData} The configuration object from the file.\n * @throws {Error} If the file cannot be read.\n * @private\n */\nfunction loadLegacyConfigFile(filePath) {\n    debug(`Loading legacy config file: ${filePath}`);\n\n    // lazy load YAML to improve performance when not used\n    const yaml = require(\"js-yaml\");\n\n    try {\n        return yaml.load(stripComments(readFile(filePath))) || /* istanbul ignore next */ {};\n    } catch (e) {\n        debug(\"Error reading YAML file: %s\\n%o\", filePath, e);\n        e.message = `Cannot read config file: ${filePath}\\nError: ${e.message}`;\n        throw e;\n    }\n}\n\n/**\n * Loads a JavaScript configuration from a file.\n * @param {string} filePath The filename to load.\n * @returns {ConfigData} The configuration object from the file.\n * @throws {Error} If the file cannot be read.\n * @private\n */\nfunction loadJSConfigFile(filePath) {\n    debug(`Loading JS config file: ${filePath}`);\n    try {\n        return importFresh(filePath);\n    } catch (e) {\n        debug(`Error reading JavaScript file: ${filePath}`);\n        e.message = `Cannot read config file: ${filePath}\\nError: ${e.message}`;\n        throw e;\n    }\n}\n\n/**\n * Loads a configuration from a package.json file.\n * @param {string} filePath The filename to load.\n * @returns {ConfigData} The configuration object from the file.\n * @throws {Error} If the file cannot be read.\n * @private\n */\nfunction loadPackageJSONConfigFile(filePath) {\n    debug(`Loading package.json config file: ${filePath}`);\n    try {\n        const packageData = loadJSONConfigFile(filePath);\n\n        if (!Object.hasOwn(packageData, \"eslintConfig\")) {\n            throw Object.assign(\n                new Error(\"package.json file doesn't have 'eslintConfig' field.\"),\n                { code: \"ESLINT_CONFIG_FIELD_NOT_FOUND\" }\n            );\n        }\n\n        return packageData.eslintConfig;\n    } catch (e) {\n        debug(`Error reading package.json file: ${filePath}`);\n        e.message = `Cannot read config file: ${filePath}\\nError: ${e.message}`;\n        throw e;\n    }\n}\n\n/**\n * Loads a `.eslintignore` from a file.\n * @param {string} filePath The filename to load.\n * @returns {string[]} The ignore patterns from the file.\n * @throws {Error} If the file cannot be read.\n * @private\n */\nfunction loadESLintIgnoreFile(filePath) {\n    debug(`Loading .eslintignore file: ${filePath}`);\n\n    try {\n        return readFile(filePath)\n            .split(/\\r?\\n/gu)\n            .filter(line => line.trim() !== \"\" && !line.startsWith(\"#\"));\n    } catch (e) {\n        debug(`Error reading .eslintignore file: ${filePath}`);\n        e.message = `Cannot read .eslintignore file: ${filePath}\\nError: ${e.message}`;\n        throw e;\n    }\n}\n\n/**\n * Creates an error to notify about a missing config to extend from.\n * @param {string} configName The name of the missing config.\n * @param {string} importerName The name of the config that imported the missing config\n * @param {string} messageTemplate The text template to source error strings from.\n * @returns {Error} The error object to throw\n * @private\n */\nfunction configInvalidError(configName, importerName, messageTemplate) {\n    return Object.assign(\n        new Error(`Failed to load config \"${configName}\" to extend from.`),\n        {\n            messageTemplate,\n            messageData: { configName, importerName }\n        }\n    );\n}\n\n/**\n * Loads a configuration file regardless of the source. Inspects the file path\n * to determine the correctly way to load the config file.\n * @param {string} filePath The path to the configuration.\n * @returns {ConfigData|null} The configuration information.\n * @private\n */\nfunction loadConfigFile(filePath) {\n    switch (path.extname(filePath)) {\n        case \".js\":\n        case \".cjs\":\n            return loadJSConfigFile(filePath);\n\n        case \".json\":\n            if (path.basename(filePath) === \"package.json\") {\n                return loadPackageJSONConfigFile(filePath);\n            }\n            return loadJSONConfigFile(filePath);\n\n        case \".yaml\":\n        case \".yml\":\n            return loadYAMLConfigFile(filePath);\n\n        default:\n            return loadLegacyConfigFile(filePath);\n    }\n}\n\n/**\n * Write debug log.\n * @param {string} request The requested module name.\n * @param {string} relativeTo The file path to resolve the request relative to.\n * @param {string} filePath The resolved file path.\n * @returns {void}\n */\nfunction writeDebugLogForLoading(request, relativeTo, filePath) {\n    /* istanbul ignore next */\n    if (debug.enabled) {\n        let nameAndVersion = null; // eslint-disable-line no-useless-assignment -- known bug in the rule\n\n        try {\n            const packageJsonPath = ModuleResolver.resolve(\n                `${request}/package.json`,\n                relativeTo\n            );\n            const { version = \"unknown\" } = require(packageJsonPath);\n\n            nameAndVersion = `${request}@${version}`;\n        } catch (error) {\n            debug(\"package.json was not found:\", error.message);\n            nameAndVersion = request;\n        }\n\n        debug(\"Loaded: %s (%s)\", nameAndVersion, filePath);\n    }\n}\n\n/**\n * Create a new context with default values.\n * @param {ConfigArrayFactoryInternalSlots} slots The internal slots.\n * @param {\"config\" | \"ignore\" | \"implicit-processor\" | undefined} providedType The type of the current configuration. Default is `\"config\"`.\n * @param {string | undefined} providedName The name of the current configuration. Default is the relative path from `cwd` to `filePath`.\n * @param {string | undefined} providedFilePath The path to the current configuration. Default is empty string.\n * @param {string | undefined} providedMatchBasePath The type of the current configuration. Default is the directory of `filePath` or `cwd`.\n * @returns {ConfigArrayFactoryLoadingContext} The created context.\n */\nfunction createContext(\n    { cwd, resolvePluginsRelativeTo },\n    providedType,\n    providedName,\n    providedFilePath,\n    providedMatchBasePath\n) {\n    const filePath = providedFilePath\n        ? path.resolve(cwd, providedFilePath)\n        : \"\";\n    const matchBasePath =\n        (providedMatchBasePath && path.resolve(cwd, providedMatchBasePath)) ||\n        (filePath && path.dirname(filePath)) ||\n        cwd;\n    const name =\n        providedName ||\n        (filePath && path.relative(cwd, filePath)) ||\n        \"\";\n    const pluginBasePath =\n        resolvePluginsRelativeTo ||\n        (filePath && path.dirname(filePath)) ||\n        cwd;\n    const type = providedType || \"config\";\n\n    return { filePath, matchBasePath, name, pluginBasePath, type };\n}\n\n/**\n * Normalize a given plugin.\n * - Ensure the object to have four properties: configs, environments, processors, and rules.\n * - Ensure the object to not have other properties.\n * @param {Plugin} plugin The plugin to normalize.\n * @returns {Plugin} The normalized plugin.\n */\nfunction normalizePlugin(plugin) {\n\n    // first check the cache\n    let normalizedPlugin = normalizedPlugins.get(plugin);\n\n    if (normalizedPlugin) {\n        return normalizedPlugin;\n    }\n\n    normalizedPlugin = {\n        configs: plugin.configs || {},\n        environments: plugin.environments || {},\n        processors: plugin.processors || {},\n        rules: plugin.rules || {}\n    };\n\n    // save the reference for later\n    normalizedPlugins.set(plugin, normalizedPlugin);\n\n    return normalizedPlugin;\n}\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\n/**\n * The factory of `ConfigArray` objects.\n */\nclass ConfigArrayFactory {\n\n    /**\n     * Initialize this instance.\n     * @param {ConfigArrayFactoryOptions} [options] The map for additional plugins.\n     */\n    constructor({\n        additionalPluginPool = new Map(),\n        cwd = process.cwd(),\n        resolvePluginsRelativeTo,\n        builtInRules,\n        resolver = ModuleResolver,\n        eslintAllPath,\n        getEslintAllConfig,\n        eslintRecommendedPath,\n        getEslintRecommendedConfig\n    } = {}) {\n        internalSlotsMap.set(this, {\n            additionalPluginPool,\n            cwd,\n            resolvePluginsRelativeTo:\n                resolvePluginsRelativeTo &&\n                path.resolve(cwd, resolvePluginsRelativeTo),\n            builtInRules,\n            resolver,\n            eslintAllPath,\n            getEslintAllConfig,\n            eslintRecommendedPath,\n            getEslintRecommendedConfig\n        });\n    }\n\n    /**\n     * Create `ConfigArray` instance from a config data.\n     * @param {ConfigData|null} configData The config data to create.\n     * @param {Object} [options] The options.\n     * @param {string} [options.basePath] The base path to resolve relative paths in `overrides[].files`, `overrides[].excludedFiles`, and `ignorePatterns`.\n     * @param {string} [options.filePath] The path to this config data.\n     * @param {string} [options.name] The config name.\n     * @returns {ConfigArray} Loaded config.\n     */\n    create(configData, { basePath, filePath, name } = {}) {\n        if (!configData) {\n            return new ConfigArray();\n        }\n\n        const slots = internalSlotsMap.get(this);\n        const ctx = createContext(slots, \"config\", name, filePath, basePath);\n        const elements = this._normalizeConfigData(configData, ctx);\n\n        return new ConfigArray(...elements);\n    }\n\n    /**\n     * Load a config file.\n     * @param {string} filePath The path to a config file.\n     * @param {Object} [options] The options.\n     * @param {string} [options.basePath] The base path to resolve relative paths in `overrides[].files`, `overrides[].excludedFiles`, and `ignorePatterns`.\n     * @param {string} [options.name] The config name.\n     * @returns {ConfigArray} Loaded config.\n     */\n    loadFile(filePath, { basePath, name } = {}) {\n        const slots = internalSlotsMap.get(this);\n        const ctx = createContext(slots, \"config\", name, filePath, basePath);\n\n        return new ConfigArray(...this._loadConfigData(ctx));\n    }\n\n    /**\n     * Load the config file on a given directory if exists.\n     * @param {string} directoryPath The path to a directory.\n     * @param {Object} [options] The options.\n     * @param {string} [options.basePath] The base path to resolve relative paths in `overrides[].files`, `overrides[].excludedFiles`, and `ignorePatterns`.\n     * @param {string} [options.name] The config name.\n     * @throws {Error} If the config file is invalid.\n     * @returns {ConfigArray} Loaded config. An empty `ConfigArray` if any config doesn't exist.\n     */\n    loadInDirectory(directoryPath, { basePath, name } = {}) {\n        const slots = internalSlotsMap.get(this);\n\n        for (const filename of configFilenames) {\n            const ctx = createContext(\n                slots,\n                \"config\",\n                name,\n                path.join(directoryPath, filename),\n                basePath\n            );\n\n            if (fs.existsSync(ctx.filePath) && fs.statSync(ctx.filePath).isFile()) {\n                let configData;\n\n                try {\n                    configData = loadConfigFile(ctx.filePath);\n                } catch (error) {\n                    if (!error || error.code !== \"ESLINT_CONFIG_FIELD_NOT_FOUND\") {\n                        throw error;\n                    }\n                }\n\n                if (configData) {\n                    debug(`Config file found: ${ctx.filePath}`);\n                    return new ConfigArray(\n                        ...this._normalizeConfigData(configData, ctx)\n                    );\n                }\n            }\n        }\n\n        debug(`Config file not found on ${directoryPath}`);\n        return new ConfigArray();\n    }\n\n    /**\n     * Check if a config file on a given directory exists or not.\n     * @param {string} directoryPath The path to a directory.\n     * @returns {string | null} The path to the found config file. If not found then null.\n     */\n    static getPathToConfigFileInDirectory(directoryPath) {\n        for (const filename of configFilenames) {\n            const filePath = path.join(directoryPath, filename);\n\n            if (fs.existsSync(filePath)) {\n                if (filename === \"package.json\") {\n                    try {\n                        loadPackageJSONConfigFile(filePath);\n                        return filePath;\n                    } catch { /* ignore */ }\n                } else {\n                    return filePath;\n                }\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Load `.eslintignore` file.\n     * @param {string} filePath The path to a `.eslintignore` file to load.\n     * @returns {ConfigArray} Loaded config. An empty `ConfigArray` if any config doesn't exist.\n     */\n    loadESLintIgnore(filePath) {\n        const slots = internalSlotsMap.get(this);\n        const ctx = createContext(\n            slots,\n            \"ignore\",\n            void 0,\n            filePath,\n            slots.cwd\n        );\n        const ignorePatterns = loadESLintIgnoreFile(ctx.filePath);\n\n        return new ConfigArray(\n            ...this._normalizeESLintIgnoreData(ignorePatterns, ctx)\n        );\n    }\n\n    /**\n     * Load `.eslintignore` file in the current working directory.\n     * @returns {ConfigArray} Loaded config. An empty `ConfigArray` if any config doesn't exist.\n     * @throws {Error} If the ignore file is invalid.\n     */\n    loadDefaultESLintIgnore() {\n        const slots = internalSlotsMap.get(this);\n        const eslintIgnorePath = path.resolve(slots.cwd, \".eslintignore\");\n        const packageJsonPath = path.resolve(slots.cwd, \"package.json\");\n\n        if (fs.existsSync(eslintIgnorePath)) {\n            return this.loadESLintIgnore(eslintIgnorePath);\n        }\n        if (fs.existsSync(packageJsonPath)) {\n            const data = loadJSONConfigFile(packageJsonPath);\n\n            if (Object.hasOwn(data, \"eslintIgnore\")) {\n                if (!Array.isArray(data.eslintIgnore)) {\n                    throw new Error(\"Package.json eslintIgnore property requires an array of paths\");\n                }\n                const ctx = createContext(\n                    slots,\n                    \"ignore\",\n                    \"eslintIgnore in package.json\",\n                    packageJsonPath,\n                    slots.cwd\n                );\n\n                return new ConfigArray(\n                    ...this._normalizeESLintIgnoreData(data.eslintIgnore, ctx)\n                );\n            }\n        }\n\n        return new ConfigArray();\n    }\n\n    /**\n     * Load a given config file.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} Loaded config.\n     * @private\n     */\n    _loadConfigData(ctx) {\n        return this._normalizeConfigData(loadConfigFile(ctx.filePath), ctx);\n    }\n\n    /**\n     * Normalize a given `.eslintignore` data to config array elements.\n     * @param {string[]} ignorePatterns The patterns to ignore files.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} The normalized config.\n     * @private\n     */\n    *_normalizeESLintIgnoreData(ignorePatterns, ctx) {\n        const elements = this._normalizeObjectConfigData(\n            { ignorePatterns },\n            ctx\n        );\n\n        // Set `ignorePattern.loose` flag for backward compatibility.\n        for (const element of elements) {\n            if (element.ignorePattern) {\n                element.ignorePattern.loose = true;\n            }\n            yield element;\n        }\n    }\n\n    /**\n     * Normalize a given config to an array.\n     * @param {ConfigData} configData The config data to normalize.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} The normalized config.\n     * @private\n     */\n    _normalizeConfigData(configData, ctx) {\n        const validator = new ConfigValidator();\n\n        validator.validateConfigSchema(configData, ctx.name || ctx.filePath);\n        return this._normalizeObjectConfigData(configData, ctx);\n    }\n\n    /**\n     * Normalize a given config to an array.\n     * @param {ConfigData|OverrideConfigData} configData The config data to normalize.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} The normalized config.\n     * @private\n     */\n    *_normalizeObjectConfigData(configData, ctx) {\n        const { files, excludedFiles, ...configBody } = configData;\n        const criteria = OverrideTester.create(\n            files,\n            excludedFiles,\n            ctx.matchBasePath\n        );\n        const elements = this._normalizeObjectConfigDataBody(configBody, ctx);\n\n        // Apply the criteria to every element.\n        for (const element of elements) {\n\n            /*\n             * Merge the criteria.\n             * This is for the `overrides` entries that came from the\n             * configurations of `overrides[].extends`.\n             */\n            element.criteria = OverrideTester.and(criteria, element.criteria);\n\n            /*\n             * Remove `root` property to ignore `root` settings which came from\n             * `extends` in `overrides`.\n             */\n            if (element.criteria) {\n                element.root = void 0;\n            }\n\n            yield element;\n        }\n    }\n\n    /**\n     * Normalize a given config to an array.\n     * @param {ConfigData} configData The config data to normalize.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} The normalized config.\n     * @private\n     */\n    *_normalizeObjectConfigDataBody(\n        {\n            env,\n            extends: extend,\n            globals,\n            ignorePatterns,\n            noInlineConfig,\n            parser: parserName,\n            parserOptions,\n            plugins: pluginList,\n            processor,\n            reportUnusedDisableDirectives,\n            root,\n            rules,\n            settings,\n            overrides: overrideList = []\n        },\n        ctx\n    ) {\n        const extendList = Array.isArray(extend) ? extend : [extend];\n        const ignorePattern = ignorePatterns && new IgnorePattern(\n            Array.isArray(ignorePatterns) ? ignorePatterns : [ignorePatterns],\n            ctx.matchBasePath\n        );\n\n        // Flatten `extends`.\n        for (const extendName of extendList.filter(Boolean)) {\n            yield* this._loadExtends(extendName, ctx);\n        }\n\n        // Load parser & plugins.\n        const parser = parserName && this._loadParser(parserName, ctx);\n        const plugins = pluginList && this._loadPlugins(pluginList, ctx);\n\n        // Yield pseudo config data for file extension processors.\n        if (plugins) {\n            yield* this._takeFileExtensionProcessors(plugins, ctx);\n        }\n\n        // Yield the config data except `extends` and `overrides`.\n        yield {\n\n            // Debug information.\n            type: ctx.type,\n            name: ctx.name,\n            filePath: ctx.filePath,\n\n            // Config data.\n            criteria: null,\n            env,\n            globals,\n            ignorePattern,\n            noInlineConfig,\n            parser,\n            parserOptions,\n            plugins,\n            processor,\n            reportUnusedDisableDirectives,\n            root,\n            rules,\n            settings\n        };\n\n        // Flatten `overries`.\n        for (let i = 0; i < overrideList.length; ++i) {\n            yield* this._normalizeObjectConfigData(\n                overrideList[i],\n                { ...ctx, name: `${ctx.name}#overrides[${i}]` }\n            );\n        }\n    }\n\n    /**\n     * Load configs of an element in `extends`.\n     * @param {string} extendName The name of a base config.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} The normalized config.\n     * @throws {Error} If the extended config file can't be loaded.\n     * @private\n     */\n    _loadExtends(extendName, ctx) {\n        debug(\"Loading {extends:%j} relative to %s\", extendName, ctx.filePath);\n        try {\n            if (extendName.startsWith(\"eslint:\")) {\n                return this._loadExtendedBuiltInConfig(extendName, ctx);\n            }\n            if (extendName.startsWith(\"plugin:\")) {\n                return this._loadExtendedPluginConfig(extendName, ctx);\n            }\n            return this._loadExtendedShareableConfig(extendName, ctx);\n        } catch (error) {\n            error.message += `\\nReferenced from: ${ctx.filePath || ctx.name}`;\n            throw error;\n        }\n    }\n\n    /**\n     * Load configs of an element in `extends`.\n     * @param {string} extendName The name of a base config.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} The normalized config.\n     * @throws {Error} If the extended config file can't be loaded.\n     * @private\n     */\n    _loadExtendedBuiltInConfig(extendName, ctx) {\n        const {\n            eslintAllPath,\n            getEslintAllConfig,\n            eslintRecommendedPath,\n            getEslintRecommendedConfig\n        } = internalSlotsMap.get(this);\n\n        if (extendName === \"eslint:recommended\") {\n            const name = `${ctx.name} » ${extendName}`;\n\n            if (getEslintRecommendedConfig) {\n                if (typeof getEslintRecommendedConfig !== \"function\") {\n                    throw new Error(`getEslintRecommendedConfig must be a function instead of '${getEslintRecommendedConfig}'`);\n                }\n                return this._normalizeConfigData(getEslintRecommendedConfig(), { ...ctx, name, filePath: \"\" });\n            }\n            return this._loadConfigData({\n                ...ctx,\n                name,\n                filePath: eslintRecommendedPath\n            });\n        }\n        if (extendName === \"eslint:all\") {\n            const name = `${ctx.name} » ${extendName}`;\n\n            if (getEslintAllConfig) {\n                if (typeof getEslintAllConfig !== \"function\") {\n                    throw new Error(`getEslintAllConfig must be a function instead of '${getEslintAllConfig}'`);\n                }\n                return this._normalizeConfigData(getEslintAllConfig(), { ...ctx, name, filePath: \"\" });\n            }\n            return this._loadConfigData({\n                ...ctx,\n                name,\n                filePath: eslintAllPath\n            });\n        }\n\n        throw configInvalidError(extendName, ctx.name, \"extend-config-missing\");\n    }\n\n    /**\n     * Load configs of an element in `extends`.\n     * @param {string} extendName The name of a base config.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} The normalized config.\n     * @throws {Error} If the extended config file can't be loaded.\n     * @private\n     */\n    _loadExtendedPluginConfig(extendName, ctx) {\n        const slashIndex = extendName.lastIndexOf(\"/\");\n\n        if (slashIndex === -1) {\n            throw configInvalidError(extendName, ctx.filePath, \"plugin-invalid\");\n        }\n\n        const pluginName = extendName.slice(\"plugin:\".length, slashIndex);\n        const configName = extendName.slice(slashIndex + 1);\n\n        if (isFilePath(pluginName)) {\n            throw new Error(\"'extends' cannot use a file path for plugins.\");\n        }\n\n        const plugin = this._loadPlugin(pluginName, ctx);\n        const configData =\n            plugin.definition &&\n            plugin.definition.configs[configName];\n\n        if (configData) {\n            return this._normalizeConfigData(configData, {\n                ...ctx,\n                filePath: plugin.filePath || ctx.filePath,\n                name: `${ctx.name} » plugin:${plugin.id}/${configName}`\n            });\n        }\n\n        throw plugin.error || configInvalidError(extendName, ctx.filePath, \"extend-config-missing\");\n    }\n\n    /**\n     * Load configs of an element in `extends`.\n     * @param {string} extendName The name of a base config.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} The normalized config.\n     * @throws {Error} If the extended config file can't be loaded.\n     * @private\n     */\n    _loadExtendedShareableConfig(extendName, ctx) {\n        const { cwd, resolver } = internalSlotsMap.get(this);\n        const relativeTo = ctx.filePath || path.join(cwd, \"__placeholder__.js\");\n        let request;\n\n        if (isFilePath(extendName)) {\n            request = extendName;\n        } else if (extendName.startsWith(\".\")) {\n            request = `./${extendName}`; // For backward compatibility. A ton of tests depended on this behavior.\n        } else {\n            request = naming.normalizePackageName(\n                extendName,\n                \"eslint-config\"\n            );\n        }\n\n        let filePath;\n\n        try {\n            filePath = resolver.resolve(request, relativeTo);\n        } catch (error) {\n            /* istanbul ignore else */\n            if (error && error.code === \"MODULE_NOT_FOUND\") {\n                throw configInvalidError(extendName, ctx.filePath, \"extend-config-missing\");\n            }\n            throw error;\n        }\n\n        writeDebugLogForLoading(request, relativeTo, filePath);\n        return this._loadConfigData({\n            ...ctx,\n            filePath,\n            name: `${ctx.name} » ${request}`\n        });\n    }\n\n    /**\n     * Load given plugins.\n     * @param {string[]} names The plugin names to load.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {Record<string,DependentPlugin>} The loaded parser.\n     * @private\n     */\n    _loadPlugins(names, ctx) {\n        return names.reduce((map, name) => {\n            if (isFilePath(name)) {\n                throw new Error(\"Plugins array cannot includes file paths.\");\n            }\n            const plugin = this._loadPlugin(name, ctx);\n\n            map[plugin.id] = plugin;\n\n            return map;\n        }, {});\n    }\n\n    /**\n     * Load a given parser.\n     * @param {string} nameOrPath The package name or the path to a parser file.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {DependentParser} The loaded parser.\n     */\n    _loadParser(nameOrPath, ctx) {\n        debug(\"Loading parser %j from %s\", nameOrPath, ctx.filePath);\n\n        const { cwd, resolver } = internalSlotsMap.get(this);\n        const relativeTo = ctx.filePath || path.join(cwd, \"__placeholder__.js\");\n\n        try {\n            const filePath = resolver.resolve(nameOrPath, relativeTo);\n\n            writeDebugLogForLoading(nameOrPath, relativeTo, filePath);\n\n            return new ConfigDependency({\n                definition: require(filePath),\n                filePath,\n                id: nameOrPath,\n                importerName: ctx.name,\n                importerPath: ctx.filePath\n            });\n        } catch (error) {\n\n            // If the parser name is \"espree\", load the espree of ESLint.\n            if (nameOrPath === \"espree\") {\n                debug(\"Fallback espree.\");\n                return new ConfigDependency({\n                    definition: require(\"espree\"),\n                    filePath: require.resolve(\"espree\"),\n                    id: nameOrPath,\n                    importerName: ctx.name,\n                    importerPath: ctx.filePath\n                });\n            }\n\n            debug(\"Failed to load parser '%s' declared in '%s'.\", nameOrPath, ctx.name);\n            error.message = `Failed to load parser '${nameOrPath}' declared in '${ctx.name}': ${error.message}`;\n\n            return new ConfigDependency({\n                error,\n                id: nameOrPath,\n                importerName: ctx.name,\n                importerPath: ctx.filePath\n            });\n        }\n    }\n\n    /**\n     * Load a given plugin.\n     * @param {string} name The plugin name to load.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {DependentPlugin} The loaded plugin.\n     * @private\n     */\n    _loadPlugin(name, ctx) {\n        debug(\"Loading plugin %j from %s\", name, ctx.filePath);\n\n        const { additionalPluginPool, resolver } = internalSlotsMap.get(this);\n        const request = naming.normalizePackageName(name, \"eslint-plugin\");\n        const id = naming.getShorthandName(request, \"eslint-plugin\");\n        const relativeTo = path.join(ctx.pluginBasePath, \"__placeholder__.js\");\n\n        if (name.match(/\\s+/u)) {\n            const error = Object.assign(\n                new Error(`Whitespace found in plugin name '${name}'`),\n                {\n                    messageTemplate: \"whitespace-found\",\n                    messageData: { pluginName: request }\n                }\n            );\n\n            return new ConfigDependency({\n                error,\n                id,\n                importerName: ctx.name,\n                importerPath: ctx.filePath\n            });\n        }\n\n        // Check for additional pool.\n        const plugin =\n            additionalPluginPool.get(request) ||\n            additionalPluginPool.get(id);\n\n        if (plugin) {\n            return new ConfigDependency({\n                definition: normalizePlugin(plugin),\n                original: plugin,\n                filePath: \"\", // It's unknown where the plugin came from.\n                id,\n                importerName: ctx.name,\n                importerPath: ctx.filePath\n            });\n        }\n\n        let filePath;\n        let error;\n\n        try {\n            filePath = resolver.resolve(request, relativeTo);\n        } catch (resolveError) {\n            error = resolveError;\n            /* istanbul ignore else */\n            if (error && error.code === \"MODULE_NOT_FOUND\") {\n                error.messageTemplate = \"plugin-missing\";\n                error.messageData = {\n                    pluginName: request,\n                    resolvePluginsRelativeTo: ctx.pluginBasePath,\n                    importerName: ctx.name\n                };\n            }\n        }\n\n        if (filePath) {\n            try {\n                writeDebugLogForLoading(request, relativeTo, filePath);\n\n                const startTime = Date.now();\n                const pluginDefinition = require(filePath);\n\n                debug(`Plugin ${filePath} loaded in: ${Date.now() - startTime}ms`);\n\n                return new ConfigDependency({\n                    definition: normalizePlugin(pluginDefinition),\n                    original: pluginDefinition,\n                    filePath,\n                    id,\n                    importerName: ctx.name,\n                    importerPath: ctx.filePath\n                });\n            } catch (loadError) {\n                error = loadError;\n            }\n        }\n\n        debug(\"Failed to load plugin '%s' declared in '%s'.\", name, ctx.name);\n        error.message = `Failed to load plugin '${name}' declared in '${ctx.name}': ${error.message}`;\n        return new ConfigDependency({\n            error,\n            id,\n            importerName: ctx.name,\n            importerPath: ctx.filePath\n        });\n    }\n\n    /**\n     * Take file expression processors as config array elements.\n     * @param {Record<string,DependentPlugin>} plugins The plugin definitions.\n     * @param {ConfigArrayFactoryLoadingContext} ctx The loading context.\n     * @returns {IterableIterator<ConfigArrayElement>} The config array elements of file expression processors.\n     * @private\n     */\n    *_takeFileExtensionProcessors(plugins, ctx) {\n        for (const pluginId of Object.keys(plugins)) {\n            const processors =\n                plugins[pluginId] &&\n                plugins[pluginId].definition &&\n                plugins[pluginId].definition.processors;\n\n            if (!processors) {\n                continue;\n            }\n\n            for (const processorId of Object.keys(processors)) {\n                if (processorId.startsWith(\".\")) {\n                    yield* this._normalizeObjectConfigData(\n                        {\n                            files: [`*${processorId}`],\n                            processor: `${pluginId}/${processorId}`\n                        },\n                        {\n                            ...ctx,\n                            type: \"implicit-processor\",\n                            name: `${ctx.name}#processors[\"${pluginId}/${processorId}\"]`\n                        }\n                    );\n                }\n            }\n        }\n    }\n}\n\nexport {\n    ConfigArrayFactory,\n    createContext,\n    loadConfigFile\n};\n", "/**\n * @fileoverview `CascadingConfigArrayFactory` class.\n *\n * `CascadingConfigArrayFactory` class has a responsibility:\n *\n * 1. Handles cascading of config files.\n *\n * It provides two methods:\n *\n * - `getConfigArrayForFile(filePath)`\n *     Get the corresponded configuration of a given file. This method doesn't\n *     throw even if the given file didn't exist.\n * - `clearCache()`\n *     Clear the internal cache. You have to call this method when\n *     `additionalPluginPool` was updated if `baseConfig` or `cliConfig` depends\n *     on the additional plugins. (`CLIEngine#addPlugin()` method calls this.)\n *\n * <AUTHOR> <https://github.com/mysticatea>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport debugOrig from \"debug\";\nimport os from \"node:os\";\nimport path from \"node:path\";\n\nimport { ConfigArrayFactory } from \"./config-array-factory.js\";\nimport {\n    ConfigArray,\n    ConfigDependency,\n    IgnorePattern\n} from \"./config-array/index.js\";\nimport ConfigValidator from \"./shared/config-validator.js\";\nimport { emitDeprecationWarning } from \"./shared/deprecation-warnings.js\";\n\nconst debug = debugOrig(\"eslintrc:cascading-config-array-factory\");\n\n//------------------------------------------------------------------------------\n// Helpers\n//------------------------------------------------------------------------------\n\n// Define types for VSCode IntelliSense.\n/** @typedef {import(\"./shared/types\").ConfigData} ConfigData */\n/** @typedef {import(\"./shared/types\").Parser} Parser */\n/** @typedef {import(\"./shared/types\").Plugin} Plugin */\n/** @typedef {import(\"./shared/types\").Rule} Rule */\n/** @typedef {ReturnType<ConfigArrayFactory[\"create\"]>} ConfigArray */\n\n/**\n * @typedef {Object} CascadingConfigArrayFactoryOptions\n * @property {Map<string,Plugin>} [additionalPluginPool] The map for additional plugins.\n * @property {ConfigData} [baseConfig] The config by `baseConfig` option.\n * @property {ConfigData} [cliConfig] The config by CLI options (`--env`, `--global`, `--ignore-pattern`, `--parser`, `--parser-options`, `--plugin`, and `--rule`). CLI options overwrite the setting in config files.\n * @property {string} [cwd] The base directory to start lookup.\n * @property {string} [ignorePath] The path to the alternative file of `.eslintignore`.\n * @property {string[]} [rulePaths] The value of `--rulesdir` option.\n * @property {string} [specificConfigPath] The value of `--config` option.\n * @property {boolean} [useEslintrc] if `false` then it doesn't load config files.\n * @property {Function} loadRules The function to use to load rules.\n * @property {Map<string,Rule>} builtInRules The rules that are built in to ESLint.\n * @property {Object} [resolver=ModuleResolver] The module resolver object.\n * @property {string} eslintAllPath The path to the definitions for eslint:all.\n * @property {Function} getEslintAllConfig Returns the config data for eslint:all.\n * @property {string} eslintRecommendedPath The path to the definitions for eslint:recommended.\n * @property {Function} getEslintRecommendedConfig Returns the config data for eslint:recommended.\n */\n\n/**\n * @typedef {Object} CascadingConfigArrayFactoryInternalSlots\n * @property {ConfigArray} baseConfigArray The config array of `baseConfig` option.\n * @property {ConfigData} baseConfigData The config data of `baseConfig` option. This is used to reset `baseConfigArray`.\n * @property {ConfigArray} cliConfigArray The config array of CLI options.\n * @property {ConfigData} cliConfigData The config data of CLI options. This is used to reset `cliConfigArray`.\n * @property {ConfigArrayFactory} configArrayFactory The factory for config arrays.\n * @property {Map<string, ConfigArray>} configCache The cache from directory paths to config arrays.\n * @property {string} cwd The base directory to start lookup.\n * @property {WeakMap<ConfigArray, ConfigArray>} finalizeCache The cache from config arrays to finalized config arrays.\n * @property {string} [ignorePath] The path to the alternative file of `.eslintignore`.\n * @property {string[]|null} rulePaths The value of `--rulesdir` option. This is used to reset `baseConfigArray`.\n * @property {string|null} specificConfigPath The value of `--config` option. This is used to reset `cliConfigArray`.\n * @property {boolean} useEslintrc if `false` then it doesn't load config files.\n * @property {Function} loadRules The function to use to load rules.\n * @property {Map<string,Rule>} builtInRules The rules that are built in to ESLint.\n * @property {Object} [resolver=ModuleResolver] The module resolver object.\n * @property {string} eslintAllPath The path to the definitions for eslint:all.\n * @property {Function} getEslintAllConfig Returns the config data for eslint:all.\n * @property {string} eslintRecommendedPath The path to the definitions for eslint:recommended.\n * @property {Function} getEslintRecommendedConfig Returns the config data for eslint:recommended.\n */\n\n/** @type {WeakMap<CascadingConfigArrayFactory, CascadingConfigArrayFactoryInternalSlots>} */\nconst internalSlotsMap = new WeakMap();\n\n/**\n * Create the config array from `baseConfig` and `rulePaths`.\n * @param {CascadingConfigArrayFactoryInternalSlots} slots The slots.\n * @returns {ConfigArray} The config array of the base configs.\n */\nfunction createBaseConfigArray({\n    configArrayFactory,\n    baseConfigData,\n    rulePaths,\n    cwd,\n    loadRules\n}) {\n    const baseConfigArray = configArrayFactory.create(\n        baseConfigData,\n        { name: \"BaseConfig\" }\n    );\n\n    /*\n     * Create the config array element for the default ignore patterns.\n     * This element has `ignorePattern` property that ignores the default\n     * patterns in the current working directory.\n     */\n    baseConfigArray.unshift(configArrayFactory.create(\n        { ignorePatterns: IgnorePattern.DefaultPatterns },\n        { name: \"DefaultIgnorePattern\" }\n    )[0]);\n\n    /*\n     * Load rules `--rulesdir` option as a pseudo plugin.\n     * Use a pseudo plugin to define rules of `--rulesdir`, so we can validate\n     * the rule's options with only information in the config array.\n     */\n    if (rulePaths && rulePaths.length > 0) {\n        baseConfigArray.push({\n            type: \"config\",\n            name: \"--rulesdir\",\n            filePath: \"\",\n            plugins: {\n                \"\": new ConfigDependency({\n                    definition: {\n                        rules: rulePaths.reduce(\n                            (map, rulesPath) => Object.assign(\n                                map,\n                                loadRules(rulesPath, cwd)\n                            ),\n                            {}\n                        )\n                    },\n                    filePath: \"\",\n                    id: \"\",\n                    importerName: \"--rulesdir\",\n                    importerPath: \"\"\n                })\n            }\n        });\n    }\n\n    return baseConfigArray;\n}\n\n/**\n * Create the config array from CLI options.\n * @param {CascadingConfigArrayFactoryInternalSlots} slots The slots.\n * @returns {ConfigArray} The config array of the base configs.\n */\nfunction createCLIConfigArray({\n    cliConfigData,\n    configArrayFactory,\n    cwd,\n    ignorePath,\n    specificConfigPath\n}) {\n    const cliConfigArray = configArrayFactory.create(\n        cliConfigData,\n        { name: \"CLIOptions\" }\n    );\n\n    cliConfigArray.unshift(\n        ...(ignorePath\n            ? configArrayFactory.loadESLintIgnore(ignorePath)\n            : configArrayFactory.loadDefaultESLintIgnore())\n    );\n\n    if (specificConfigPath) {\n        cliConfigArray.unshift(\n            ...configArrayFactory.loadFile(\n                specificConfigPath,\n                { name: \"--config\", basePath: cwd }\n            )\n        );\n    }\n\n    return cliConfigArray;\n}\n\n/**\n * The error type when there are files matched by a glob, but all of them have been ignored.\n */\nclass ConfigurationNotFoundError extends Error {\n\n\n    /**\n     * @param {string} directoryPath The directory path.\n     */\n    constructor(directoryPath) {\n        super(`No ESLint configuration found in ${directoryPath}.`);\n        this.messageTemplate = \"no-config-found\";\n        this.messageData = { directoryPath };\n    }\n}\n\n/**\n * This class provides the functionality that enumerates every file which is\n * matched by given glob patterns and that configuration.\n */\nclass CascadingConfigArrayFactory {\n\n    /**\n     * Initialize this enumerator.\n     * @param {CascadingConfigArrayFactoryOptions} options The options.\n     */\n    constructor({\n        additionalPluginPool = new Map(),\n        baseConfig: baseConfigData = null,\n        cliConfig: cliConfigData = null,\n        cwd = process.cwd(),\n        ignorePath,\n        resolvePluginsRelativeTo,\n        rulePaths = [],\n        specificConfigPath = null,\n        useEslintrc = true,\n        builtInRules = new Map(),\n        loadRules,\n        resolver,\n        eslintRecommendedPath,\n        getEslintRecommendedConfig,\n        eslintAllPath,\n        getEslintAllConfig\n    } = {}) {\n        const configArrayFactory = new ConfigArrayFactory({\n            additionalPluginPool,\n            cwd,\n            resolvePluginsRelativeTo,\n            builtInRules,\n            resolver,\n            eslintRecommendedPath,\n            getEslintRecommendedConfig,\n            eslintAllPath,\n            getEslintAllConfig\n        });\n\n        internalSlotsMap.set(this, {\n            baseConfigArray: createBaseConfigArray({\n                baseConfigData,\n                configArrayFactory,\n                cwd,\n                rulePaths,\n                loadRules\n            }),\n            baseConfigData,\n            cliConfigArray: createCLIConfigArray({\n                cliConfigData,\n                configArrayFactory,\n                cwd,\n                ignorePath,\n                specificConfigPath\n            }),\n            cliConfigData,\n            configArrayFactory,\n            configCache: new Map(),\n            cwd,\n            finalizeCache: new WeakMap(),\n            ignorePath,\n            rulePaths,\n            specificConfigPath,\n            useEslintrc,\n            builtInRules,\n            loadRules\n        });\n    }\n\n    /**\n     * The path to the current working directory.\n     * This is used by tests.\n     * @type {string}\n     */\n    get cwd() {\n        const { cwd } = internalSlotsMap.get(this);\n\n        return cwd;\n    }\n\n    /**\n     * Get the config array of a given file.\n     * If `filePath` was not given, it returns the config which contains only\n     * `baseConfigData` and `cliConfigData`.\n     * @param {string} [filePath] The file path to a file.\n     * @param {Object} [options] The options.\n     * @param {boolean} [options.ignoreNotFoundError] If `true` then it doesn't throw `ConfigurationNotFoundError`.\n     * @returns {ConfigArray} The config array of the file.\n     */\n    getConfigArrayForFile(filePath, { ignoreNotFoundError = false } = {}) {\n        const {\n            baseConfigArray,\n            cliConfigArray,\n            cwd\n        } = internalSlotsMap.get(this);\n\n        if (!filePath) {\n            return new ConfigArray(...baseConfigArray, ...cliConfigArray);\n        }\n\n        const directoryPath = path.dirname(path.resolve(cwd, filePath));\n\n        debug(`Load config files for ${directoryPath}.`);\n\n        return this._finalizeConfigArray(\n            this._loadConfigInAncestors(directoryPath),\n            directoryPath,\n            ignoreNotFoundError\n        );\n    }\n\n    /**\n     * Set the config data to override all configs.\n     * Require to call `clearCache()` method after this method is called.\n     * @param {ConfigData} configData The config data to override all configs.\n     * @returns {void}\n     */\n    setOverrideConfig(configData) {\n        const slots = internalSlotsMap.get(this);\n\n        slots.cliConfigData = configData;\n    }\n\n    /**\n     * Clear config cache.\n     * @returns {void}\n     */\n    clearCache() {\n        const slots = internalSlotsMap.get(this);\n\n        slots.baseConfigArray = createBaseConfigArray(slots);\n        slots.cliConfigArray = createCLIConfigArray(slots);\n        slots.configCache.clear();\n    }\n\n    /**\n     * Load and normalize config files from the ancestor directories.\n     * @param {string} directoryPath The path to a leaf directory.\n     * @param {boolean} configsExistInSubdirs `true` if configurations exist in subdirectories.\n     * @returns {ConfigArray} The loaded config.\n     * @throws {Error} If a config file is invalid.\n     * @private\n     */\n    _loadConfigInAncestors(directoryPath, configsExistInSubdirs = false) {\n        const {\n            baseConfigArray,\n            configArrayFactory,\n            configCache,\n            cwd,\n            useEslintrc\n        } = internalSlotsMap.get(this);\n\n        if (!useEslintrc) {\n            return baseConfigArray;\n        }\n\n        let configArray = configCache.get(directoryPath);\n\n        // Hit cache.\n        if (configArray) {\n            debug(`Cache hit: ${directoryPath}.`);\n            return configArray;\n        }\n        debug(`No cache found: ${directoryPath}.`);\n\n        const homePath = os.homedir();\n\n        // Consider this is root.\n        if (directoryPath === homePath && cwd !== homePath) {\n            debug(\"Stop traversing because of considered root.\");\n            if (configsExistInSubdirs) {\n                const filePath = ConfigArrayFactory.getPathToConfigFileInDirectory(directoryPath);\n\n                if (filePath) {\n                    emitDeprecationWarning(\n                        filePath,\n                        \"ESLINT_PERSONAL_CONFIG_SUPPRESS\"\n                    );\n                }\n            }\n            return this._cacheConfig(directoryPath, baseConfigArray);\n        }\n\n        // Load the config on this directory.\n        try {\n            configArray = configArrayFactory.loadInDirectory(directoryPath);\n        } catch (error) {\n            /* istanbul ignore next */\n            if (error.code === \"EACCES\") {\n                debug(\"Stop traversing because of 'EACCES' error.\");\n                return this._cacheConfig(directoryPath, baseConfigArray);\n            }\n            throw error;\n        }\n\n        if (configArray.length > 0 && configArray.isRoot()) {\n            debug(\"Stop traversing because of 'root:true'.\");\n            configArray.unshift(...baseConfigArray);\n            return this._cacheConfig(directoryPath, configArray);\n        }\n\n        // Load from the ancestors and merge it.\n        const parentPath = path.dirname(directoryPath);\n        const parentConfigArray = parentPath && parentPath !== directoryPath\n            ? this._loadConfigInAncestors(\n                parentPath,\n                configsExistInSubdirs || configArray.length > 0\n            )\n            : baseConfigArray;\n\n        if (configArray.length > 0) {\n            configArray.unshift(...parentConfigArray);\n        } else {\n            configArray = parentConfigArray;\n        }\n\n        // Cache and return.\n        return this._cacheConfig(directoryPath, configArray);\n    }\n\n    /**\n     * Freeze and cache a given config.\n     * @param {string} directoryPath The path to a directory as a cache key.\n     * @param {ConfigArray} configArray The config array as a cache value.\n     * @returns {ConfigArray} The `configArray` (frozen).\n     */\n    _cacheConfig(directoryPath, configArray) {\n        const { configCache } = internalSlotsMap.get(this);\n\n        Object.freeze(configArray);\n        configCache.set(directoryPath, configArray);\n\n        return configArray;\n    }\n\n    /**\n     * Finalize a given config array.\n     * Concatenate `--config` and other CLI options.\n     * @param {ConfigArray} configArray The parent config array.\n     * @param {string} directoryPath The path to the leaf directory to find config files.\n     * @param {boolean} ignoreNotFoundError If `true` then it doesn't throw `ConfigurationNotFoundError`.\n     * @returns {ConfigArray} The loaded config.\n     * @throws {Error} If a config file is invalid.\n     * @private\n     */\n    _finalizeConfigArray(configArray, directoryPath, ignoreNotFoundError) {\n        const {\n            cliConfigArray,\n            configArrayFactory,\n            finalizeCache,\n            useEslintrc,\n            builtInRules\n        } = internalSlotsMap.get(this);\n\n        let finalConfigArray = finalizeCache.get(configArray);\n\n        if (!finalConfigArray) {\n            finalConfigArray = configArray;\n\n            // Load the personal config if there are no regular config files.\n            if (\n                useEslintrc &&\n                configArray.every(c => !c.filePath) &&\n                cliConfigArray.every(c => !c.filePath) // `--config` option can be a file.\n            ) {\n                const homePath = os.homedir();\n\n                debug(\"Loading the config file of the home directory:\", homePath);\n\n                const personalConfigArray = configArrayFactory.loadInDirectory(\n                    homePath,\n                    { name: \"PersonalConfig\" }\n                );\n\n                if (\n                    personalConfigArray.length > 0 &&\n                    !directoryPath.startsWith(homePath)\n                ) {\n                    const lastElement =\n                        personalConfigArray.at(-1);\n\n                    emitDeprecationWarning(\n                        lastElement.filePath,\n                        \"ESLINT_PERSONAL_CONFIG_LOAD\"\n                    );\n                }\n\n                finalConfigArray = finalConfigArray.concat(personalConfigArray);\n            }\n\n            // Apply CLI options.\n            if (cliConfigArray.length > 0) {\n                finalConfigArray = finalConfigArray.concat(cliConfigArray);\n            }\n\n            // Validate rule settings and environments.\n            const validator = new ConfigValidator({\n                builtInRules\n            });\n\n            validator.validateConfigArray(finalConfigArray);\n\n            // Cache it.\n            Object.freeze(finalConfigArray);\n            finalizeCache.set(configArray, finalConfigArray);\n\n            debug(\n                \"Configuration was determined: %o on %s\",\n                finalConfigArray,\n                directoryPath\n            );\n        }\n\n        // At least one element (the default ignore patterns) exists.\n        if (!ignoreNotFoundError && useEslintrc && finalConfigArray.length <= 1) {\n            throw new ConfigurationNotFoundError(directoryPath);\n        }\n\n        return finalConfigArray;\n    }\n}\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\nexport { CascadingConfigArrayFactory };\n", "/**\n * @fileoverview Compatibility class for flat config.\n * <AUTHOR>\n */\n\n//-----------------------------------------------------------------------------\n// Requirements\n//-----------------------------------------------------------------------------\n\nimport createDebug from \"debug\";\nimport path from \"node:path\";\n\nimport environments from \"../conf/environments.js\";\nimport { ConfigArrayFactory } from \"./config-array-factory.js\";\n\n//-----------------------------------------------------------------------------\n// Helpers\n//-----------------------------------------------------------------------------\n\n/** @typedef {import(\"../../shared/types\").Environment} Environment */\n/** @typedef {import(\"../../shared/types\").Processor} Processor */\n\nconst debug = createDebug(\"eslintrc:flat-compat\");\nconst cafactory = Symbol(\"cafactory\");\n\n/**\n * Translates an ESLintRC-style config object into a flag-config-style config\n * object.\n * @param {Object} eslintrcConfig An ESLintRC-style config object.\n * @param {Object} options Options to help translate the config.\n * @param {string} options.resolveConfigRelativeTo To the directory to resolve\n *      configs from.\n * @param {string} options.resolvePluginsRelativeTo The directory to resolve\n *      plugins from.\n * @param {ReadOnlyMap<string,Environment>} options.pluginEnvironments A map of plugin environment\n *      names to objects.\n * @param {ReadOnlyMap<string,Processor>} options.pluginProcessors A map of plugin processor\n *      names to objects.\n * @returns {Object} A flag-config-style config object.\n * @throws {Error} If a plugin or environment cannot be resolved.\n */\nfunction translateESLintRC(eslintrcConfig, {\n    resolveConfigRelativeTo,\n    resolvePluginsRelativeTo,\n    pluginEnvironments,\n    pluginProcessors\n}) {\n\n    const flatConfig = {};\n    const configs = [];\n    const languageOptions = {};\n    const linterOptions = {};\n    const keysToCopy = [\"settings\", \"rules\", \"processor\"];\n    const languageOptionsKeysToCopy = [\"globals\", \"parser\", \"parserOptions\"];\n    const linterOptionsKeysToCopy = [\"noInlineConfig\", \"reportUnusedDisableDirectives\"];\n\n    // copy over simple translations\n    for (const key of keysToCopy) {\n        if (key in eslintrcConfig && typeof eslintrcConfig[key] !== \"undefined\") {\n            flatConfig[key] = eslintrcConfig[key];\n        }\n    }\n\n    // copy over languageOptions\n    for (const key of languageOptionsKeysToCopy) {\n        if (key in eslintrcConfig && typeof eslintrcConfig[key] !== \"undefined\") {\n\n            // create the languageOptions key in the flat config\n            flatConfig.languageOptions = languageOptions;\n\n            if (key === \"parser\") {\n                debug(`Resolving parser '${languageOptions[key]}' relative to ${resolveConfigRelativeTo}`);\n\n                if (eslintrcConfig[key].error) {\n                    throw eslintrcConfig[key].error;\n                }\n\n                languageOptions[key] = eslintrcConfig[key].definition;\n                continue;\n            }\n\n            // clone any object values that are in the eslintrc config\n            if (eslintrcConfig[key] && typeof eslintrcConfig[key] === \"object\") {\n                languageOptions[key] = {\n                    ...eslintrcConfig[key]\n                };\n            } else {\n                languageOptions[key] = eslintrcConfig[key];\n            }\n        }\n    }\n\n    // copy over linterOptions\n    for (const key of linterOptionsKeysToCopy) {\n        if (key in eslintrcConfig && typeof eslintrcConfig[key] !== \"undefined\") {\n            flatConfig.linterOptions = linterOptions;\n            linterOptions[key] = eslintrcConfig[key];\n        }\n    }\n\n    // move ecmaVersion a level up\n    if (languageOptions.parserOptions) {\n\n        if (\"ecmaVersion\" in languageOptions.parserOptions) {\n            languageOptions.ecmaVersion = languageOptions.parserOptions.ecmaVersion;\n            delete languageOptions.parserOptions.ecmaVersion;\n        }\n\n        if (\"sourceType\" in languageOptions.parserOptions) {\n            languageOptions.sourceType = languageOptions.parserOptions.sourceType;\n            delete languageOptions.parserOptions.sourceType;\n        }\n\n        // check to see if we even need parserOptions anymore and remove it if not\n        if (Object.keys(languageOptions.parserOptions).length === 0) {\n            delete languageOptions.parserOptions;\n        }\n    }\n\n    // overrides\n    if (eslintrcConfig.criteria) {\n        flatConfig.files = [absoluteFilePath => eslintrcConfig.criteria.test(absoluteFilePath)];\n    }\n\n    // translate plugins\n    if (eslintrcConfig.plugins && typeof eslintrcConfig.plugins === \"object\") {\n        debug(`Translating plugins: ${eslintrcConfig.plugins}`);\n\n        flatConfig.plugins = {};\n\n        for (const pluginName of Object.keys(eslintrcConfig.plugins)) {\n\n            debug(`Translating plugin: ${pluginName}`);\n            debug(`Resolving plugin '${pluginName} relative to ${resolvePluginsRelativeTo}`);\n\n            const { original: plugin, error } = eslintrcConfig.plugins[pluginName];\n\n            if (error) {\n                throw error;\n            }\n\n            flatConfig.plugins[pluginName] = plugin;\n\n            // create a config for any processors\n            if (plugin.processors) {\n                for (const processorName of Object.keys(plugin.processors)) {\n                    if (processorName.startsWith(\".\")) {\n                        debug(`Assigning processor: ${pluginName}/${processorName}`);\n\n                        configs.unshift({\n                            files: [`**/*${processorName}`],\n                            processor: pluginProcessors.get(`${pluginName}/${processorName}`)\n                        });\n                    }\n\n                }\n            }\n        }\n    }\n\n    // translate env - must come after plugins\n    if (eslintrcConfig.env && typeof eslintrcConfig.env === \"object\") {\n        for (const envName of Object.keys(eslintrcConfig.env)) {\n\n            // only add environments that are true\n            if (eslintrcConfig.env[envName]) {\n                debug(`Translating environment: ${envName}`);\n\n                if (environments.has(envName)) {\n\n                    // built-in environments should be defined first\n                    configs.unshift(...translateESLintRC({\n                        criteria: eslintrcConfig.criteria,\n                        ...environments.get(envName)\n                    }, {\n                        resolveConfigRelativeTo,\n                        resolvePluginsRelativeTo\n                    }));\n                } else if (pluginEnvironments.has(envName)) {\n\n                    // if the environment comes from a plugin, it should come after the plugin config\n                    configs.push(...translateESLintRC({\n                        criteria: eslintrcConfig.criteria,\n                        ...pluginEnvironments.get(envName)\n                    }, {\n                        resolveConfigRelativeTo,\n                        resolvePluginsRelativeTo\n                    }));\n                }\n            }\n        }\n    }\n\n    // only add if there are actually keys in the config\n    if (Object.keys(flatConfig).length > 0) {\n        configs.push(flatConfig);\n    }\n\n    return configs;\n}\n\n\n//-----------------------------------------------------------------------------\n// Exports\n//-----------------------------------------------------------------------------\n\n/**\n * A compatibility class for working with configs.\n */\nclass FlatCompat {\n\n    constructor({\n        baseDirectory = process.cwd(),\n        resolvePluginsRelativeTo = baseDirectory,\n        recommendedConfig,\n        allConfig\n    } = {}) {\n        this.baseDirectory = baseDirectory;\n        this.resolvePluginsRelativeTo = resolvePluginsRelativeTo;\n        this[cafactory] = new ConfigArrayFactory({\n            cwd: baseDirectory,\n            resolvePluginsRelativeTo,\n            getEslintAllConfig() {\n\n                if (!allConfig) {\n                    throw new TypeError(\"Missing parameter 'allConfig' in FlatCompat constructor.\");\n                }\n\n                return allConfig;\n            },\n            getEslintRecommendedConfig() {\n\n                if (!recommendedConfig) {\n                    throw new TypeError(\"Missing parameter 'recommendedConfig' in FlatCompat constructor.\");\n                }\n\n                return recommendedConfig;\n            }\n        });\n    }\n\n    /**\n     * Translates an ESLintRC-style config into a flag-config-style config.\n     * @param {Object} eslintrcConfig The ESLintRC-style config object.\n     * @returns {Object} A flag-config-style config object.\n     */\n    config(eslintrcConfig) {\n        const eslintrcArray = this[cafactory].create(eslintrcConfig, {\n            basePath: this.baseDirectory\n        });\n\n        const flatArray = [];\n        let hasIgnorePatterns = false;\n\n        eslintrcArray.forEach(configData => {\n            if (configData.type === \"config\") {\n                hasIgnorePatterns = hasIgnorePatterns || configData.ignorePattern;\n                flatArray.push(...translateESLintRC(configData, {\n                    resolveConfigRelativeTo: path.join(this.baseDirectory, \"__placeholder.js\"),\n                    resolvePluginsRelativeTo: path.join(this.resolvePluginsRelativeTo, \"__placeholder.js\"),\n                    pluginEnvironments: eslintrcArray.pluginEnvironments,\n                    pluginProcessors: eslintrcArray.pluginProcessors\n                }));\n            }\n        });\n\n        // combine ignorePatterns to emulate ESLintRC behavior better\n        if (hasIgnorePatterns) {\n            flatArray.unshift({\n                ignores: [filePath => {\n\n                    // Compute the final config for this file.\n                    // This filters config array elements by `files`/`excludedFiles` then merges the elements.\n                    const finalConfig = eslintrcArray.extractConfig(filePath);\n\n                    // Test the `ignorePattern` properties of the final config.\n                    return Boolean(finalConfig.ignores) && finalConfig.ignores(filePath);\n                }]\n            });\n        }\n\n        return flatArray;\n    }\n\n    /**\n     * Translates the `env` section of an ESLintRC-style config.\n     * @param {Object} envConfig The `env` section of an ESLintRC config.\n     * @returns {Object[]} An array of flag-config objects representing the environments.\n     */\n    env(envConfig) {\n        return this.config({\n            env: envConfig\n        });\n    }\n\n    /**\n     * Translates the `extends` section of an ESLintRC-style config.\n     * @param {...string} configsToExtend The names of the configs to load.\n     * @returns {Object[]} An array of flag-config objects representing the config.\n     */\n    extends(...configsToExtend) {\n        return this.config({\n            extends: configsToExtend\n        });\n    }\n\n    /**\n     * Translates the `plugins` section of an ESLintRC-style config.\n     * @param {...string} plugins The names of the plugins to load.\n     * @returns {Object[]} An array of flag-config objects representing the plugins.\n     */\n    plugins(...plugins) {\n        return this.config({\n            plugins\n        });\n    }\n}\n\nexport { FlatCompat };\n", "/**\n * @fileoverview Package exports for @eslint/eslintrc\n * <AUTHOR>\n */\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport {\n    ConfigArrayFactory,\n    createContext as createConfigArrayFactoryContext,\n    loadConfigFile\n} from \"./config-array-factory.js\";\n\nimport { CascadingConfigArrayFactory } from \"./cascading-config-array-factory.js\";\nimport * as ModuleResolver from \"./shared/relative-module-resolver.js\";\nimport { ConfigArra<PERSON>, getUsedExtractedConfigs } from \"./config-array/index.js\";\nimport { ConfigDependency } from \"./config-array/config-dependency.js\";\nimport { ExtractedConfig } from \"./config-array/extracted-config.js\";\nimport { IgnorePattern } from \"./config-array/ignore-pattern.js\";\nimport { OverrideTester } from \"./config-array/override-tester.js\";\nimport * as ConfigOps from \"./shared/config-ops.js\";\nimport ConfigValidator from \"./shared/config-validator.js\";\nimport * as naming from \"./shared/naming.js\";\nimport { FlatCompat } from \"./flat-compat.js\";\nimport environments from \"../conf/environments.js\";\n\n//-----------------------------------------------------------------------------\n// Exports\n//-----------------------------------------------------------------------------\n\nconst Legacy = {\n    ConfigArray,\n    createConfigArrayFactoryContext,\n    CascadingConfigArrayFactory,\n    ConfigArrayFactory,\n    ConfigDependency,\n    ExtractedConfig,\n    IgnorePattern,\n    OverrideTester,\n    getUsedExtractedConfigs,\n    environments,\n    loadConfigFile,\n\n    // shared\n    ConfigOps,\n    ConfigValidator,\n    ModuleResolver,\n    naming\n};\n\nexport {\n\n    Legacy,\n\n    FlatCompat\n\n};\n"], "names": ["debug", "debugOrig", "path", "ignore", "assert", "internalSlotsMap", "util", "minimatch", "Ajv", "globals", "BuiltInEnvironments", "ConfigOps.normalizeConfigGlobal", "<PERSON><PERSON><PERSON>", "require", "createRequire", "fs", "stripComments", "importFresh", "ModuleResolver.resolve", "naming.normalizePackageName", "naming.getShorthandName", "os", "createDebug", "createConfigArrayFactoryContext"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAUA;AACA,MAAMA,OAAK,GAAGC,6BAAS,CAAC,yBAAyB,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC,WAAW,EAAE;AAC5C,IAAI,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACjD,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC;AACzB,QAAQ,MAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACjC;AACA;AACA,QAAQ,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7C;AACA;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AAC3E,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AAC/B,gBAAgB,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAChD,gBAAgB,MAAM;AACtB,aAAa;AACb,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,KAAKC,wBAAI,CAAC,GAAG,EAAE;AACnC,gBAAgB,UAAU,GAAG,CAAC,CAAC;AAC/B,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA,IAAI,IAAI,cAAc,GAAG,MAAM,IAAIA,wBAAI,CAAC,GAAG,CAAC;AAC5C;AACA;AACA,IAAI,IAAI,cAAc,IAAI,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;AACxF,QAAQ,cAAc,IAAIA,wBAAI,CAAC,GAAG,CAAC;AACnC,KAAK;AACL,IAAI,OAAO,cAAc,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE;AAC5B,IAAI,MAAM,OAAO,GAAGA,wBAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAC5C;AACA,IAAI,IAAIA,wBAAI,CAAC,GAAG,KAAK,GAAG,EAAE;AAC1B,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,OAAO,CAAC,KAAK,CAACA,wBAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,QAAQ,EAAE;AAC7B,IAAI,MAAM,KAAK;AACf,QAAQ,QAAQ,CAAC,QAAQ,CAACA,wBAAI,CAAC,GAAG,CAAC;AACnC,SAAS,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAChE,KAAK,CAAC;AACN;AACA,IAAI,OAAO,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC;AAC5B,CAAC;AACD;AACA,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC;AAC9D,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,aAAa,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,eAAe,GAAG;AACjC,QAAQ,OAAO,eAAe,CAAC;AAC/B,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,mBAAmB,CAAC,GAAG,EAAE;AACpC,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,aAAa,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5E,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,YAAY,CAAC,cAAc,EAAE;AACxC,QAAQF,OAAK,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;AACjD;AACA,QAAQ,MAAM,QAAQ,GAAG,qBAAqB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACpF,QAAQ,MAAM,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACxF,QAAQ,MAAM,EAAE,GAAGG,0BAAM,CAAC,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC3F,QAAQ,MAAM,KAAK,GAAGA,0BAAM,CAAC,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACzE;AACA,QAAQH,OAAK,CAAC,iBAAiB,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;AACzD;AACA,QAAQ,OAAO,MAAM,CAAC,MAAM;AAC5B,YAAY,CAAC,QAAQ,EAAE,GAAG,GAAG,KAAK,KAAK;AACvC,gBAAgBI,0BAAM,CAACF,wBAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,wCAAwC,CAAC,CAAC;AAC5F,gBAAgB,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAChE,gBAAgB,MAAM,OAAO,GAAG,UAAU,KAAK,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjF,gBAAgB,MAAM,SAAS,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;AACnD,gBAAgB,MAAM,MAAM,GAAG,OAAO,KAAK,EAAE,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC5E;AACA,gBAAgBF,OAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;AACjF,gBAAgB,OAAO,MAAM,CAAC;AAC9B,aAAa;AACb,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAClC,SAAS,CAAC;AACV,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE;AACpC,QAAQI,0BAAM,CAACF,wBAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,wCAAwC,CAAC,CAAC;AACpF;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qBAAqB,CAAC,WAAW,EAAE;AACvC,QAAQE,0BAAM,CAACF,wBAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,2CAA2C,CAAC,CAAC;AAC1F,QAAQ,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AACnD;AACA,QAAQ,IAAI,WAAW,KAAK,QAAQ,EAAE;AACtC,YAAY,OAAO,QAAQ,CAAC;AAC5B,SAAS;AACT,QAAQ,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7D;AACA,QAAQ,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,IAAI;AACvC,YAAY,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACrD,YAAY,MAAM,IAAI,GAAG,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC;AAC7C,YAAY,MAAM,IAAI,GAAG,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AAC/D;AACA,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAChE,gBAAgB,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACjD,aAAa;AACb,YAAY,OAAO,KAAK,GAAG,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACnE,SAAS,CAAC,CAAC;AACX,KAAK;AACL;;AC5OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE;AAC5B,IAAI,OAAO,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,eAAe,CAAC;AACtB,IAAI,WAAW,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,0BAA0B,GAAG,EAAE,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AAC3B,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,qCAAqC,GAAG;AAC5C,QAAQ,MAAM;AACd;AACA,YAAY,0BAA0B,EAAE,QAAQ;AAChD,YAAY,SAAS,EAAE,QAAQ;AAC/B;AACA,YAAY,OAAO;AACnB,YAAY,GAAG,MAAM;AACrB,SAAS,GAAG,IAAI,CAAC;AACjB;AACA,QAAQ,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;AAChE,QAAQ,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;AAC/E,QAAQ,MAAM,CAAC,cAAc,GAAG,OAAO,GAAG,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC;AAChE;AACA;AACA,QAAQ,IAAI,UAAU,CAAC,MAAM,CAAC,cAAc,EAAE,aAAa,CAAC,eAAe,CAAC,EAAE;AAC9E,YAAY,MAAM,CAAC,cAAc;AACjC,gBAAgB,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAClF,SAAS;AACT;AACA,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK;AACL;;AC9IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,kBAAgB,GAAG,IAAI,cAAc,OAAO,CAAC;AACnD,IAAI,GAAG,CAAC,GAAG,EAAE;AACb,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACnC;AACA,QAAQ,IAAI,CAAC,KAAK,EAAE;AACpB,YAAY,KAAK,GAAG;AACpB,gBAAgB,KAAK,EAAE,IAAI,GAAG,EAAE;AAChC,gBAAgB,MAAM,EAAE,IAAI;AAC5B,gBAAgB,YAAY,EAAE,IAAI;AAClC,gBAAgB,OAAO,EAAE,IAAI;AAC7B,aAAa,CAAC;AACd,YAAY,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAClC,SAAS;AACT;AACA,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,CAAC,EAAE,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE;AAC/C,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AACnD,QAAQ,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACpC;AACA,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;AAChF,YAAY,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAS;AACT,KAAK;AACL;AACA,IAAI,OAAO,OAAO,CAAC;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,CAAC,EAAE;AAC5B,IAAI,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE;AAC/C,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;AAClC,QAAQ,OAAO;AACf,KAAK;AACL;AACA,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AAC3C,QAAQ,IAAI,GAAG,KAAK,WAAW,EAAE;AACjC,YAAY,SAAS;AACrB,SAAS;AACT;AACA,QAAQ,IAAI,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AAC1C,YAAY,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5D,SAAS,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,EAAE;AAC3C,YAAY,IAAI,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AAC9C,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACnE,gBAAgB,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAChE,aAAa,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,EAAE;AAC/C,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1C,aAAa;AACb,SAAS;AACT,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,mBAAmB,SAAS,KAAK,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE;AACnC,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,yBAAyB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvH,QAAQ,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;AACjD,QAAQ,IAAI,CAAC,WAAW,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;AACjD,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE;AACtC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;AAClC,QAAQ,OAAO;AACf,KAAK;AACL;AACA,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AAC3C,QAAQ,IAAI,GAAG,KAAK,WAAW,EAAE;AACjC,YAAY,SAAS;AACrB,SAAS;AACT,QAAQ,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACxC,QAAQ,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACxC;AACA;AACA,QAAQ,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE;AACpC,YAAY,IAAI,WAAW,CAAC,KAAK,EAAE;AACnC,gBAAgB,MAAM,WAAW,CAAC,KAAK,CAAC;AACxC,aAAa;AACb,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;AACtC,SAAS,MAAM,IAAI,WAAW,CAAC,QAAQ,KAAK,WAAW,CAAC,QAAQ,EAAE;AAClE,YAAY,MAAM,IAAI,mBAAmB,CAAC,GAAG,EAAE;AAC/C,gBAAgB;AAChB,oBAAoB,QAAQ,EAAE,WAAW,CAAC,QAAQ;AAClD,oBAAoB,YAAY,EAAE,WAAW,CAAC,YAAY;AAC1D,iBAAiB;AACjB,gBAAgB;AAChB,oBAAoB,QAAQ,EAAE,WAAW,CAAC,QAAQ;AAClD,oBAAoB,YAAY,EAAE,WAAW,CAAC,YAAY;AAC1D,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE;AAC1C,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;AAClC,QAAQ,OAAO;AACf,KAAK;AACL;AACA,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AAC3C,QAAQ,IAAI,GAAG,KAAK,WAAW,EAAE;AACjC,YAAY,SAAS;AACrB,SAAS;AACT,QAAQ,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACtC,QAAQ,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACtC;AACA;AACA,QAAQ,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE;AAClC,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAC1C,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;AAC7C,aAAa,MAAM;AACnB,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC1C,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM;AACf,YAAY,SAAS,CAAC,MAAM,KAAK,CAAC;AAClC,YAAY,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;AACpC,YAAY,SAAS,CAAC,MAAM,IAAI,CAAC;AACjC,UAAU;AACV,YAAY,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,SAAS;AACT,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE;AACzC,IAAI,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;AACzC,IAAI,MAAM,cAAc,GAAG,EAAE,CAAC;AAC9B;AACA;AACA,IAAI,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;AACjC,QAAQ,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxC;AACA;AACA,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE;AAC9C,YAAY,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;AACtC,gBAAgB,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AAC3C,aAAa;AACb,YAAY,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAC3C,SAAS;AACT;AACA;AACA,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,EAAE;AACpD,YAAY,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AACjD,SAAS;AACT;AACA;AACA,QAAQ,IAAI,MAAM,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,EAAE;AACnF,YAAY,MAAM,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AAC3D,YAAY,MAAM,CAAC,0BAA0B,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7D,SAAS;AACT;AACA;AACA,QAAQ,IAAI,MAAM,CAAC,6BAA6B,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,6BAA6B,KAAK,KAAK,CAAC,EAAE;AACjH,YAAY,MAAM,CAAC,6BAA6B,GAAG,OAAO,CAAC,6BAA6B,CAAC;AACzF,SAAS;AACT;AACA;AACA,QAAQ,IAAI,OAAO,CAAC,aAAa,EAAE;AACnC,YAAY,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACvD,SAAS;AACT;AACA;AACA,QAAQ,qBAAqB,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACvD,QAAQ,qBAAqB,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AAC/D,QAAQ,qBAAqB,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC3E,QAAQ,qBAAqB,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjE,QAAQ,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AACtD,QAAQ,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AACtD,KAAK;AACL;AACA;AACA,IAAI,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,QAAQ,MAAM,CAAC,OAAO,GAAG,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC;AAC9E,KAAK;AACL;AACA,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE;AACtC,IAAI,IAAI,IAAI,EAAE;AACd,QAAQ,MAAM,MAAM,GAAG,QAAQ,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClD;AACA,QAAQ,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACzD,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC9C,SAAS;AACT,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC,GAAG,EAAE;AACpC,IAAI,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE;AACjC,QAAQ,KAAK,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE;AACpD,QAAQ,MAAM,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE;AACrD,QAAQ,GAAG,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE;AAClD,KAAK,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,QAAQ,EAAE,KAAK,EAAE;AAC/C,IAAI,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAChC;AACA,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;AAC7B,IAAI,KAAK,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AACnC,IAAI,KAAK,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAC9B;AACA,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC9B,YAAY,SAAS;AACrB,SAAS;AACT;AACA,QAAQ,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACzE,YAAY,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;AAC5C;AACA,YAAY,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AACpD,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACpC;AACA,YAAY,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AACjE,YAAY,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;AACrE,YAAY,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AAC3D,SAAS;AACT,KAAK;AACL;AACA,IAAI,qBAAqB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,qBAAqB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC9C,IAAI,qBAAqB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACzC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,sBAAsB,CAAC,QAAQ,EAAE;AAC1C,IAAI,MAAM,KAAK,GAAGA,kBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACjD;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACxB,QAAQ,oBAAoB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC9C,KAAK;AACL;AACA,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,SAAS,KAAK,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,kBAAkB,GAAG;AAC7B,QAAQ,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AACnD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,gBAAgB,GAAG;AAC3B,QAAQ,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC;AACzD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,WAAW,GAAG;AACtB,QAAQ,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;AACpD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,GAAG;AACb,QAAQ,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AACnD,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACtC;AACA,YAAY,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE;AAC3C,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,CAAC,QAAQ,EAAE;AAC5B,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAGA,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACrD,QAAQ,MAAM,OAAO,GAAG,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1D,QAAQ,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C;AACA,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAClC,YAAY,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AAC7D,SAAS;AACT;AACA,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACnC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,sBAAsB,CAAC,QAAQ,EAAE;AACrC,QAAQ,KAAK,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE;AAC/C,YAAY;AACZ,gBAAgB,IAAI,KAAK,QAAQ;AACjC,gBAAgB,QAAQ;AACxB,gBAAgB,CAAC,QAAQ,CAAC,gBAAgB;AAC1C,gBAAgB,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;AACvC,cAAc;AACd,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,uBAAuB,CAAC,QAAQ,EAAE;AAC3C,IAAI,MAAM,EAAE,KAAK,EAAE,GAAGA,kBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACrD;AACA,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;AACtC;;ACzfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC;AAChB,QAAQ,UAAU,GAAG,IAAI;AACzB,QAAQ,QAAQ,GAAG,IAAI;AACvB,QAAQ,KAAK,GAAG,IAAI;AACpB,QAAQ,QAAQ,GAAG,IAAI;AACvB,QAAQ,EAAE;AACV,QAAQ,YAAY;AACpB,QAAQ,YAAY;AACpB,KAAK,EAAE;AACP;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACzC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,GAAG;AACb,QAAQ,MAAM,GAAG,GAAG,IAAI,CAACC,wBAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;AAChD;AACA;AACA,QAAQ,IAAI,GAAG,CAAC,KAAK,YAAY,KAAK,EAAE;AACxC,YAAY,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AACrE,SAAS;AACT;AACA,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,CAACA,wBAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG;AAC5B,QAAQ,MAAM;AACd,YAAY,UAAU,EAAE,QAAQ;AAChC,YAAY,QAAQ,EAAE,QAAQ;AAC9B,YAAY,GAAG,GAAG;AAClB,SAAS,GAAG,IAAI,CAAC;AACjB;AACA,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL;;ACtHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA,MAAM,EAAE,SAAS,EAAE,GAAGC,6BAAS,CAAC;AAChC;AACA,MAAM,aAAa,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,iBAAiB,CAAC,QAAQ,EAAE;AACrC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACjC,QAAQ,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,EAAE;AAClD,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC1B,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,QAAQ,EAAE;AAC7B,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,IAAI;AACnC,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AACvC,YAAY,OAAO,IAAI,SAAS;AAChC,gBAAgB,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAChC;AACA;AACA,gBAAgB,EAAE,GAAG,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE;AACtD,aAAa,CAAC;AACd,SAAS;AACT,QAAQ,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AACrD,KAAK,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE;AAC/C,IAAI,OAAO;AACX,QAAQ,QAAQ,EAAE,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;AAC1D,QAAQ,QAAQ,EAAE,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;AAC1D,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,cAAc,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,MAAM,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE;AAClD,QAAQ,MAAM,eAAe,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACzD,QAAQ,MAAM,eAAe,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC;AACjE,QAAQ,IAAI,gBAAgB,GAAG,KAAK,CAAC;AACrC;AACA,QAAQ,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1C,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT;AACA;AACA,QAAQ,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE;AAC/C,YAAY,IAAIL,wBAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACpE,gBAAgB,MAAM,IAAI,KAAK,CAAC,CAAC,uEAAuE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AACrH,aAAa;AACb,YAAY,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACvC,gBAAgB,gBAAgB,GAAG,IAAI,CAAC;AACxC,aAAa;AACb,SAAS;AACT,QAAQ,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE;AAC/C,YAAY,IAAIA,wBAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACpE,gBAAgB,MAAM,IAAI,KAAK,CAAC,CAAC,uEAAuE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AACrH,aAAa;AACb,SAAS;AACT;AACA,QAAQ,MAAM,QAAQ,GAAG,SAAS,CAAC,eAAe,CAAC,CAAC;AACpD,QAAQ,MAAM,QAAQ,GAAG,SAAS,CAAC,eAAe,CAAC,CAAC;AACpD;AACA,QAAQ,OAAO,IAAI,cAAc;AACjC,YAAY,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;AACpC,YAAY,QAAQ;AACpB,YAAY,gBAAgB;AAC5B,SAAS,CAAC;AACV,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE;AACrB,QAAQ,IAAI,CAAC,CAAC,EAAE;AAChB,YAAY,OAAO,CAAC,IAAI,IAAI,cAAc;AAC1C,gBAAgB,CAAC,CAAC,QAAQ;AAC1B,gBAAgB,CAAC,CAAC,QAAQ;AAC1B,gBAAgB,CAAC,CAAC,gBAAgB;AAClC,aAAa,CAAC;AACd,SAAS;AACT,QAAQ,IAAI,CAAC,CAAC,EAAE;AAChB,YAAY,OAAO,IAAI,cAAc;AACrC,gBAAgB,CAAC,CAAC,QAAQ;AAC1B,gBAAgB,CAAC,CAAC,QAAQ;AAC1B,gBAAgB,CAAC,CAAC,gBAAgB;AAClC,aAAa,CAAC;AACd,SAAS;AACT;AACA,QAAQE,0BAAM,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;AACnD,QAAQ,OAAO,IAAI,cAAc;AACjC,YAAY,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;AACzC,YAAY,CAAC,CAAC,QAAQ;AACtB,YAAY,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC,gBAAgB;AACpD,SAAS,CAAC;AACV,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,GAAG,KAAK,EAAE;AAC9D;AACA;AACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC;AACA;AACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC;AACA;AACA,QAAQ,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACjD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAACF,wBAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACxE,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,+CAA+C,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3F,SAAS;AACT,QAAQ,MAAM,YAAY,GAAGA,wBAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACpE;AACA,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC1D,YAAY,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACnE,aAAa,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;AACrE,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,GAAG;AACb,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AACxC,YAAY,OAAO;AACnB,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClD,gBAAgB,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvC,aAAa,CAAC;AACd,SAAS;AACT,QAAQ,OAAO;AACf,YAAY,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC;AACjD,YAAY,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACnC,SAAS,CAAC;AACV,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,CAACI,wBAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG;AAC5B,QAAQ,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,KAAK;AACL;;AChOA;AACA;AACA;AACA;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qBAAqB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;AACtD,IAAI,aAAa,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,KAAK;AACxE,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC3B,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,EAAE,EAAE,CAAC;AACV,IAAI,gBAAgB,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,UAAU,EAAE;AACrC,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AACjF;AACA,IAAI,IAAI,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,EAAE;AAC3E,QAAQ,OAAO,aAAa,CAAC;AAC7B,KAAK;AACL;AACA,IAAI,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;AAC3C,QAAQ,OAAO,aAAa,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC;AAC/D,KAAK;AACL;AACA,IAAI,OAAO,CAAC,CAAC;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,MAAM,EAAE;AACpC;AACA,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;AACtB,QAAQ,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI;AACpD,YAAY,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACpD;AACA,YAAY,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AAChD,gBAAgB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,qBAAqB,CAAC,UAAU,CAAC,IAAI,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACrG,aAAa,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,UAAU,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AACvF,gBAAgB,UAAU,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACjG,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,UAAU,EAAE;AACrC,IAAI,OAAO,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC7C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,UAAU,EAAE;AACrC,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AAC1E;AACA,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AACtC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;AAC1C,KAAK;AACL,IAAI,OAAO,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC1C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,MAAM,EAAE;AACtC,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC,eAAe,EAAE;AAChD,IAAI,QAAQ,eAAe;AAC3B,QAAQ,KAAK,KAAK;AAClB,YAAY,OAAO,KAAK,CAAC;AACzB;AACA,QAAQ,KAAK,IAAI,CAAC;AAClB,QAAQ,KAAK,MAAM,CAAC;AACpB,QAAQ,KAAK,WAAW,CAAC;AACzB,QAAQ,KAAK,UAAU;AACvB,YAAY,OAAO,UAAU,CAAC;AAC9B;AACA,QAAQ,KAAK,IAAI,CAAC;AAClB,QAAQ,KAAK,KAAK,CAAC;AACnB,QAAQ,KAAK,OAAO,CAAC;AACrB,QAAQ,KAAK,UAAU,CAAC;AACxB,QAAQ,KAAK,UAAU;AACvB,YAAY,OAAO,UAAU,CAAC;AAC9B;AACA,QAAQ;AACR,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,kFAAkF,CAAC,CAAC,CAAC;AACrI,KAAK;AACL;;;;;;;;;;;;AC7HA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,0BAA0B,GAAG;AACnC,IAAI,0BAA0B;AAC9B,QAAQ,0EAA0E;AAClF,IAAI,2BAA2B;AAC/B,QAAQ,qDAAqD;AAC7D,QAAQ,gEAAgE;AACxE,IAAI,+BAA+B;AACnC,QAAQ,qDAAqD;AAC7D,QAAQ,kEAAkE;AAC1E,QAAQ,kEAAkE;AAC1E,CAAC,CAAC;AACF;AACA,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,sBAAsB,CAAC,MAAM,EAAE,SAAS,EAAE;AACnD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC3D;AACA,IAAI,IAAI,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC5C,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACvC;AACA,IAAI,MAAM,GAAG,GAAGJ,wBAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;AACrD,IAAI,MAAM,OAAO,GAAG,0BAA0B,CAAC,SAAS,CAAC,CAAC;AAC1D;AACA,IAAI,OAAO,CAAC,WAAW;AACvB,QAAQ,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,CAAC;AACxC,QAAQ,oBAAoB;AAC5B,QAAQ,SAAS;AACjB,KAAK,CAAC;AACN;;ACtDA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,GAAG;AACnB,IAAI,EAAE,EAAE,yCAAyC;AACjD,IAAI,OAAO,EAAE,yCAAyC;AACtD,IAAI,WAAW,EAAE,yBAAyB;AAC1C,IAAI,WAAW,EAAE;AACjB,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,QAAQ,EAAE,CAAC;AACvB,YAAY,KAAK,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAChC,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,IAAI,EAAE,SAAS;AAC3B,YAAY,OAAO,EAAE,CAAC;AACtB,SAAS;AACT,QAAQ,uBAAuB,EAAE;AACjC,YAAY,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,+BAA+B,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9E,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;AACvF,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AACrC,YAAY,QAAQ,EAAE,CAAC;AACvB,YAAY,WAAW,EAAE,IAAI;AAC7B,SAAS;AACT,KAAK;AACL,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,UAAU,EAAE;AAChB,QAAQ,EAAE,EAAE;AACZ,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,KAAK,EAAE;AACf,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,OAAO,EAAE,GAAG;AACpB,QAAQ,UAAU,EAAE;AACpB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,gBAAgB,EAAE,IAAI;AAClC,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,IAAI,EAAE,SAAS;AAC3B,YAAY,OAAO,EAAE,KAAK;AAC1B,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,IAAI,EAAE,SAAS;AAC3B,YAAY,OAAO,EAAE,KAAK;AAC1B,SAAS;AACT,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AAC5D,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;AACpE,QAAQ,OAAO,EAAE;AACjB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,MAAM,EAAE,OAAO;AAC3B,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;AACnC,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAE;AAC7B,aAAa;AACb,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,KAAK,EAAE;AACf,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAE;AAC7B,gBAAgB,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACrD,aAAa;AACb,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AAC3D,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;AACnE,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,SAAS;AAC3B,YAAY,OAAO,EAAE,KAAK;AAC1B,SAAS;AACT,QAAQ,aAAa,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AAChE,QAAQ,aAAa,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;AACxE,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACvD,QAAQ,oBAAoB,EAAE;AAC9B,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;AACnC,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAE;AAC7B,aAAa;AACb,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,oBAAoB,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC/C,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,oBAAoB,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC/C,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,iBAAiB,EAAE;AAC3B,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,oBAAoB,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC/C,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,oBAAoB,EAAE;AAClC,gBAAgB,KAAK,EAAE;AACvB,oBAAoB,EAAE,IAAI,EAAE,GAAG,EAAE;AACjC,oBAAoB,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACzD,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,QAAQ,EAAE,CAAC;AACvB,YAAY,WAAW,EAAE,IAAI;AAC7B,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACrD,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,OAAO;AACjC,oBAAoB,KAAK,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AAChE,oBAAoB,QAAQ,EAAE,CAAC;AAC/B,oBAAoB,WAAW,EAAE,IAAI;AACrC,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAClC,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACpD,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACpD,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACpD,QAAQ,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC1B,KAAK;AACL,IAAI,YAAY,EAAE;AAClB,QAAQ,gBAAgB,EAAE,CAAC,SAAS,CAAC;AACrC,QAAQ,gBAAgB,EAAE,CAAC,SAAS,CAAC;AACrC,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,cAAe,CAAC,iBAAiB,GAAG,EAAE,KAAK;AAC3C,IAAI,MAAM,GAAG,GAAG,IAAIM,uBAAG,CAAC;AACxB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,WAAW,EAAE,QAAQ;AAC7B,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,QAAQ,EAAE,MAAM;AACxB,QAAQ,GAAG,iBAAiB;AAC5B,KAAK,CAAC,CAAC;AACP;AACA,IAAI,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AAClC;AACA,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC,EAAE,CAAC;AAC1C;AACA,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;;AC9LD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,IAAI,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAChF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE;AACzC,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE;AAC3B,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL;AACA,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;AAC/D,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK;AACL;AACA,IAAI,MAAM,MAAM,GAAG,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,EAAE,CAAC;AAC3C;AACA,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AAC3C,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;AACpE,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACpE,SAAS;AACT,KAAK;AACL;AACA,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;AAC3B,QAAQ,OAAO,MAAM,IAAI,KAAK,IAAI,EAAE,CAAC;AACrC,KAAK;AACL;AACA,IAAI,OAAO;AACX,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AACrC,KAAK,CAAC;AACN;;ACvDA;AACA;AACA;AACA;AACA;AACA,MAAM,oBAAoB,GAAG;AAC7B,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC/B,IAAI,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC3B,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AACtD,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC/B,IAAI,SAAS,EAAE;AACf,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,8BAA8B,EAAE;AACvD,QAAQ,eAAe,EAAE,KAAK;AAC9B,KAAK;AACL,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;AACxC,IAAI,aAAa,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AACrC,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;AAC9B,IAAI,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AACjC,IAAI,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC7B,IAAI,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAChC,IAAI,cAAc,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;AACvC,IAAI,6BAA6B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;AACtD;AACA,IAAI,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AACpC,CAAC,CAAC;AACF;AACA,MAAM,YAAY,GAAG;AACrB,IAAI,WAAW,EAAE;AACjB,QAAQ,eAAe,EAAE;AACzB,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAE;AAClC,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,OAAO;AACjC,oBAAoB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC7C,oBAAoB,eAAe,EAAE,KAAK;AAC1C,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,uBAAuB,EAAE;AACjC,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAE;AAClC,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,OAAO;AACjC,oBAAoB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC7C,oBAAoB,eAAe,EAAE,KAAK;AAC1C,oBAAoB,QAAQ,EAAE,CAAC;AAC/B,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA,QAAQ,YAAY,EAAE;AACtB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,UAAU,EAAE;AACxB,gBAAgB,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;AACzC,gBAAgB,cAAc,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AACzE,gBAAgB,GAAG,oBAAoB;AACvC,aAAa;AACb,YAAY,oBAAoB,EAAE,KAAK;AACvC,SAAS;AACT;AACA;AACA,QAAQ,cAAc,EAAE;AACxB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,UAAU,EAAE;AACxB,gBAAgB,aAAa,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AACxE,gBAAgB,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;AACxE,gBAAgB,GAAG,oBAAoB;AACvC,aAAa;AACb,YAAY,QAAQ,EAAE,CAAC,OAAO,CAAC;AAC/B,YAAY,oBAAoB,EAAE,KAAK;AACvC,SAAS;AACT,KAAK;AACL;AACA,IAAI,IAAI,EAAE,4BAA4B;AACtC,CAAC;;AC5ED;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE;AAChC,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;AACpB;AACA,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACxD,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;AACvC,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC9B,SAAS;AACT,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD;AACA,MAAM,cAAc,GAAG,OAAO,CAACC,2BAAO,CAAC,MAAM,EAAEA,2BAAO,CAAC,GAAG,CAAC,CAAC;AAC5D,MAAM,cAAc,GAAG;AACvB,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,iBAAiB,EAAE,KAAK;AAC5B,CAAC,CAAC;AACF,MAAM,cAAc,GAAG;AACvB,IAAI,MAAM,EAAE,KAAK;AACjB,IAAI,aAAa,EAAE,KAAK;AACxB,IAAI,cAAc,EAAE,KAAK;AACzB,IAAI,UAAU,EAAE,KAAK;AACrB,CAAC,CAAC;AACF;AACA,MAAM,cAAc,GAAG;AACvB,IAAI,cAAc,EAAE,KAAK;AACzB,IAAI,oBAAoB,EAAE,KAAK;AAC/B,IAAI,OAAO,EAAE,KAAK;AAClB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,mBAAe,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC;AACtC;AACA;AACA,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,GAAG;AAC5B,KAAK;AACL,IAAI,GAAG,EAAE;AACT,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AACzD,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AACzD,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AACzD,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC5E,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC/F,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC/F,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC/F,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC/F,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,OAAO;AAChC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,QAAQ,OAAO,EAAEA,2BAAO,CAAC,IAAI;AAC7B,QAAQ,aAAa,EAAE;AACvB,YAAY,YAAY,EAAE;AAC1B,gBAAgB,YAAY,EAAE,IAAI;AAClC,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAI,qBAAqB,EAAE;AAC3B,QAAQ,OAAO,EAAEA,2BAAO,CAAC,qBAAqB,CAAC;AAC/C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAEA,2BAAO,CAAC,MAAM;AAC/B,KAAK;AACL,IAAI,aAAa,EAAE;AACnB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,aAAa;AACtC,KAAK;AACL;AACA;AACA,IAAI,QAAQ,EAAE;AACd,QAAQ,OAAO,EAAEA,2BAAO,CAAC,QAAQ;AACjC,QAAQ,aAAa,EAAE;AACvB,YAAY,YAAY,EAAE;AAC1B,gBAAgB,YAAY,EAAE,IAAI;AAClC,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAI,GAAG,EAAE;AACT,QAAQ,OAAO,EAAEA,2BAAO,CAAC,GAAG;AAC5B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,OAAO,EAAEA,2BAAO,CAAC,KAAK;AAC9B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,OAAO;AAChC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,QAAQ,OAAO,EAAEA,2BAAO,CAAC,IAAI;AAC7B,KAAK;AACL,IAAI,SAAS,EAAE;AACf,QAAQ,OAAO,EAAEA,2BAAO,CAAC,SAAS;AAClC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAEA,2BAAO,CAAC,MAAM;AAC/B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,OAAO,EAAEA,2BAAO,CAAC,KAAK;AAC9B,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,WAAW;AACpC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,OAAO;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAEA,2BAAO,CAAC,MAAM;AAC/B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,OAAO,EAAEA,2BAAO,CAAC,KAAK;AAC9B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,UAAU;AACnC,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,WAAW;AACpC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,OAAO;AAChC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,QAAQ,OAAO,EAAEA,2BAAO,CAAC,QAAQ;AACjC,KAAK;AACL,IAAI,SAAS,EAAE;AACf,QAAQ,OAAO,EAAEA,2BAAO,CAAC,SAAS;AAClC,KAAK;AACL,IAAI,aAAa,EAAE;AACnB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,aAAa;AACtC,KAAK;AACL,IAAI,YAAY,EAAE;AAClB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,YAAY;AACrC,KAAK;AACL,CAAC,CAAC,CAAC;;ACtNH;AACA;AACA;AACA;AAqBA;AACA,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC;AACtB;AACA,MAAM,cAAc,GAAG,IAAI,OAAO,EAAE,CAAC;AACrC,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC;AAChC;AACA;AACA;AACA;AACA,IAAI,cAAc,CAAC;AACnB,MAAM,WAAW,GAAG;AACpB,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,GAAG,EAAE,CAAC;AACV,CAAC,CAAC;AACF;AACA,MAAM,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;AAChC;AACA;AACA,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC;AACtC,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI,QAAQ,EAAE,CAAC;AACf,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAM,eAAe,CAAC;AACrC,IAAI,WAAW,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AACnD,QAAQ,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACzC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,CAAC,IAAI,EAAE;AAC/B,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT;AACA,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACxB,YAAY,OAAO,EAAE,GAAG,eAAe,EAAE,CAAC;AAC1C,SAAS;AACT;AACA,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACxC;AACA,QAAQ,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC3C,YAAY,OAAO,EAAE,GAAG,eAAe,EAAE,CAAC;AAC1C,SAAS;AACT;AACA;AACA,QAAQ,IAAI,MAAM,KAAK,KAAK,EAAE;AAC9B,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT;AACA,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE;AAC3D,YAAY,MAAM,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC;AACnF,SAAS;AACT;AACA;AACA,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACnC,YAAY,IAAI,MAAM,CAAC,MAAM,EAAE;AAC/B,gBAAgB,OAAO;AACvB,oBAAoB,IAAI,EAAE,OAAO;AACjC,oBAAoB,KAAK,EAAE,MAAM;AACjC,oBAAoB,QAAQ,EAAE,CAAC;AAC/B,oBAAoB,QAAQ,EAAE,MAAM,CAAC,MAAM;AAC3C,iBAAiB,CAAC;AAClB,aAAa;AACb;AACA;AACA,YAAY,OAAO,EAAE,GAAG,eAAe,EAAE,CAAC;AAC1C,SAAS;AACT;AACA;AACA,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,CAAC,OAAO,EAAE;AAClC,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AACvE,QAAQ,MAAM,YAAY,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,GAAG,QAAQ,CAAC;AAC3G;AACA,QAAQ,IAAI,YAAY,KAAK,CAAC,IAAI,YAAY,KAAK,CAAC,IAAI,YAAY,KAAK,CAAC,EAAE;AAC5E,YAAY,OAAO,YAAY,CAAC;AAChC,SAAS;AACT;AACA,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,qFAAqF,EAAEH,wBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACxL;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC3C,QAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACvC,YAAY,IAAI;AAChB,gBAAgB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAC/D;AACA,gBAAgB,IAAI,MAAM,EAAE;AAC5B,oBAAoB,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAClE,iBAAiB;AACjB,aAAa,CAAC,OAAO,GAAG,EAAE;AAC1B,gBAAgB,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7E;AACA,gBAAgB,aAAa,CAAC,IAAI,GAAG,oCAAoC,CAAC;AAC1E;AACA,gBAAgB,MAAM,aAAa,CAAC;AACpC,aAAa;AACb,SAAS;AACT;AACA,QAAQ,MAAM,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACtD;AACA,QAAQ,IAAI,YAAY,EAAE;AAC1B,YAAY,MAAM,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;AAC3F;AACA,YAAY,YAAY,CAAC,aAAa,CAAC,CAAC;AACxC;AACA,YAAY,IAAI,YAAY,CAAC,MAAM,EAAE;AACrC,gBAAgB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG;AACvD,oBAAoB,KAAK,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;AACxF,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,EAAE;AAC9D,QAAQ,IAAI;AACZ,YAAY,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAChE;AACA,YAAY,IAAI,QAAQ,KAAK,CAAC,EAAE;AAChC,gBAAgB,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9F,aAAa;AACb,SAAS,CAAC,OAAO,GAAG,EAAE;AACtB,YAAY,IAAI,eAAe,GAAG,GAAG,CAAC,IAAI,KAAK,oCAAoC;AACnF,kBAAkB,CAAC,0DAA0D,EAAE,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AACxG,kBAAkB,CAAC,wBAAwB,EAAE,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AACnF;AACA,YAAY,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC5C,gBAAgB,eAAe,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC;AACrE,aAAa;AACb;AACA,YAAY,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7E;AACA,YAAY,IAAI,GAAG,CAAC,IAAI,EAAE;AAC1B,gBAAgB,aAAa,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AAC9C,aAAa;AACb;AACA,YAAY,MAAM,aAAa,CAAC;AAChC,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB;AACvB,QAAQ,WAAW;AACnB,QAAQ,MAAM;AACd,QAAQ,gBAAgB,GAAG,IAAI;AAC/B,MAAM;AACN;AACA;AACA,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,OAAO;AACnB,SAAS;AACT;AACA,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI;AAC/C,YAAY,MAAM,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAII,YAAmB,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;AACpF;AACA,YAAY,IAAI,CAAC,GAAG,EAAE;AACtB,gBAAgB,MAAM,OAAO,GAAG,CAAC,EAAE,MAAM,CAAC,sBAAsB,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC;AACrF;AACA,gBAAgB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACzC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa;AACjB,QAAQ,WAAW;AACnB,QAAQ,MAAM;AACd,QAAQ,iBAAiB,GAAG,IAAI;AAChC,MAAM;AACN,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,OAAO;AACnB,SAAS;AACT;AACA,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI;AAC/C,YAAY,MAAM,IAAI,GAAG,iBAAiB,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;AACpF;AACA,YAAY,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACxE,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,eAAe,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,EAAE;AAClD,QAAQ,IAAI,CAAC,aAAa,EAAE;AAC5B,YAAY,OAAO;AACnB,SAAS;AACT;AACA,QAAQ,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;AACrC,aAAa,OAAO,CAAC,CAAC,CAAC,gBAAgB,EAAE,eAAe,CAAC,KAAK;AAC9D,gBAAgB,IAAI;AACpB,oBAAoBC,qBAA+B,CAAC,eAAe,CAAC,CAAC;AACrE,iBAAiB,CAAC,OAAO,GAAG,EAAE;AAC9B,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,gCAAgC,EAAE,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,cAAc,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrI,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE;AAC3D,QAAQ,IAAI,aAAa,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;AAC3D,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,sCAAsC,EAAE,MAAM,CAAC,eAAe,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC9H,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,CAAC,MAAM,EAAE;AACzB,QAAQ,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI;AACnC,YAAY,IAAI,KAAK,CAAC,OAAO,KAAK,sBAAsB,EAAE;AAC1D,gBAAgB,MAAM,qBAAqB,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC;AACxK;AACA,gBAAgB,OAAO,CAAC,+BAA+B,EAAE,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAClF,aAAa;AACb,YAAY,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;AAC1C,gBAAgB,MAAM,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/D,gBAAgB,MAAM,qBAAqB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AAClH,gBAAgB,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAClE;AACA,gBAAgB,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,8BAA8B,EAAE,qBAAqB,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;AAC1I,aAAa;AACb;AACA,YAAY,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;AAC/F;AACA,YAAY,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvF,SAAS,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE;AAChD,QAAQ,cAAc,GAAG,cAAc,IAAI,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AACrE;AACA,QAAQ,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AACrC,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,wBAAwB,EAAE,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1H,SAAS;AACT;AACA,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE;AACnD,YAAY,sBAAsB,CAAC,MAAM,EAAE,4BAA4B,CAAC,CAAC;AACzE,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,gBAAgB,EAAE;AAClE,QAAQ,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAClD,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;AACpE,QAAQ,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;AACvE,QAAQ,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACrD;AACA,QAAQ,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,IAAI,EAAE,EAAE;AACvD,YAAY,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC1E,YAAY,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;AAC7E,YAAY,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACzD,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB,CAAC,WAAW,EAAE;AACrC,QAAQ,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AACpF,QAAQ,MAAM,kBAAkB,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;AACxF,QAAQ,MAAM,aAAa,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAC9E;AACA;AACA,QAAQ,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;AAC3C,YAAY,IAAI,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACxC,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACnC;AACA,YAAY,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AAC9E,YAAY,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;AAChE,YAAY,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;AACxF,YAAY,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC3E,SAAS;AACT,KAAK;AACL;AACA;;AC9XA;AACA;AACA;AACA;AACA,MAAM,eAAe,GAAG,UAAU,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE;AAC5C,IAAI,IAAI,cAAc,GAAG,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACvC,QAAQ,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC7D,KAAK;AACL;AACA,IAAI,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC1C;AACA;AACA;AACA;AACA;AACA,QAAQ,MAAM,0BAA0B,GAAG,IAAI,MAAM,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;AAC5F,YAAY,sBAAsB,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AACxE;AACA,QAAQ,IAAI,0BAA0B,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;AAC7D,YAAY,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,0BAA0B,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AAChG,SAAS,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC/E;AACA;AACA;AACA;AACA;AACA,YAAY,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7F,SAAS;AACT,KAAK,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AACzD,QAAQ,cAAc,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;AACvD,KAAK;AACL;AACA,IAAI,OAAO,cAAc,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE;AAC5C,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC7B,QAAQ,IAAI,WAAW,GAAG,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjF;AACA,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAY,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;AAClC,SAAS;AACT;AACA,QAAQ,WAAW,GAAG,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClF,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAY,OAAO,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,SAAS;AACT,KAAK,MAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAClD,QAAQ,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACjD,KAAK;AACL;AACA,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,IAAI,EAAE;AACpC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AAC9C;AACA,IAAI,OAAO,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACjC;;;;;;;;;ACrFA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA,MAAM,aAAa,GAAGC,0BAAM,CAAC,aAAa,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,UAAU,EAAE,cAAc,EAAE;AAC7C,IAAI,IAAI;AACR,QAAQ,OAAO,aAAa,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACjE,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB;AACA;AACA,QAAQ;AACR,YAAY,OAAO,KAAK,KAAK,QAAQ;AACrC,YAAY,KAAK,KAAK,IAAI;AAC1B,YAAY,KAAK,CAAC,IAAI,KAAK,kBAAkB;AAC7C,YAAY,CAAC,KAAK,CAAC,YAAY;AAC/B,YAAY,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC9C,UAAU;AACV,YAAY,KAAK,CAAC,OAAO,IAAI,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC,CAAC;AACrE,SAAS;AACT,QAAQ,MAAM,KAAK,CAAC;AACpB,KAAK;AACL;;;;;;;ACtCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAsBA;AACA,MAAMC,SAAO,GAAGC,oBAAa,CAAC,mDAAe,CAAC,CAAC;AAC/C;AACA,MAAMd,OAAK,GAAGC,6BAAS,CAAC,+BAA+B,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,MAAM,eAAe,GAAG;AACxB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,kBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;AACvC;AACA;AACA,MAAM,iBAAiB,GAAG,IAAI,OAAO,EAAE,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,UAAU,EAAE;AAChC,IAAI;AACJ,QAAQ,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;AACzC,QAAQH,wBAAI,CAAC,UAAU,CAAC,UAAU,CAAC;AACnC,MAAM;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,QAAQ,EAAE;AAC5B,IAAI,OAAOa,sBAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AACrE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,QAAQ,EAAE;AACtC,IAAIf,OAAK,CAAC,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACnD;AACA;AACA,IAAI,MAAM,IAAI,GAAGa,SAAO,CAAC,SAAS,CAAC,CAAC;AACpC;AACA,IAAI,IAAI;AACR;AACA;AACA,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;AACnD,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,QAAQb,OAAK,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACtD,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC,yBAAyB,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAChF,QAAQ,MAAM,CAAC,CAAC;AAChB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,QAAQ,EAAE;AACtC,IAAIA,OAAK,CAAC,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACnD;AACA,IAAI,IAAI;AACR,QAAQ,OAAO,IAAI,CAAC,KAAK,CAACgB,iCAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7D,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,QAAQhB,OAAK,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACtD,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC,yBAAyB,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAChF,QAAQ,CAAC,CAAC,eAAe,GAAG,qBAAqB,CAAC;AAClD,QAAQ,CAAC,CAAC,WAAW,GAAG;AACxB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,OAAO,EAAE,CAAC,CAAC,OAAO;AAC9B,SAAS,CAAC;AACV,QAAQ,MAAM,CAAC,CAAC;AAChB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,QAAQ,EAAE;AACxC,IAAIA,OAAK,CAAC,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACrD;AACA;AACA,IAAI,MAAM,IAAI,GAAGa,SAAO,CAAC,SAAS,CAAC,CAAC;AACpC;AACA,IAAI,IAAI;AACR,QAAQ,OAAO,IAAI,CAAC,IAAI,CAACG,iCAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,+BAA+B,EAAE,CAAC;AAC7F,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,QAAQhB,OAAK,CAAC,iCAAiC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC9D,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC,yBAAyB,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAChF,QAAQ,MAAM,CAAC,CAAC;AAChB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AACpC,IAAIA,OAAK,CAAC,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACjD,IAAI,IAAI;AACR,QAAQ,OAAOiB,+BAAW,CAAC,QAAQ,CAAC,CAAC;AACrC,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,QAAQjB,OAAK,CAAC,CAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC5D,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC,yBAAyB,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAChF,QAAQ,MAAM,CAAC,CAAC;AAChB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,yBAAyB,CAAC,QAAQ,EAAE;AAC7C,IAAIA,OAAK,CAAC,CAAC,kCAAkC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAI,IAAI;AACR,QAAQ,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACzD;AACA,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE;AACzD,YAAY,MAAM,MAAM,CAAC,MAAM;AAC/B,gBAAgB,IAAI,KAAK,CAAC,sDAAsD,CAAC;AACjF,gBAAgB,EAAE,IAAI,EAAE,+BAA+B,EAAE;AACzD,aAAa,CAAC;AACd,SAAS;AACT;AACA,QAAQ,OAAO,WAAW,CAAC,YAAY,CAAC;AACxC,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,QAAQA,OAAK,CAAC,CAAC,iCAAiC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9D,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC,yBAAyB,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAChF,QAAQ,MAAM,CAAC,CAAC;AAChB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,QAAQ,EAAE;AACxC,IAAIA,OAAK,CAAC,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACrD;AACA,IAAI,IAAI;AACR,QAAQ,OAAO,QAAQ,CAAC,QAAQ,CAAC;AACjC,aAAa,KAAK,CAAC,SAAS,CAAC;AAC7B,aAAa,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACzE,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,QAAQA,OAAK,CAAC,CAAC,kCAAkC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/D,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC,gCAAgC,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACvF,QAAQ,MAAM,CAAC,CAAC;AAChB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE;AACvE,IAAI,OAAO,MAAM,CAAC,MAAM;AACxB,QAAQ,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;AAC1E,QAAQ;AACR,YAAY,eAAe;AAC3B,YAAY,WAAW,EAAE,EAAE,UAAU,EAAE,YAAY,EAAE;AACrD,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,QAAQ,EAAE;AAClC,IAAI,QAAQE,wBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;AAClC,QAAQ,KAAK,KAAK,CAAC;AACnB,QAAQ,KAAK,MAAM;AACnB,YAAY,OAAO,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAC9C;AACA,QAAQ,KAAK,OAAO;AACpB,YAAY,IAAIA,wBAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;AAC5D,gBAAgB,OAAO,yBAAyB,CAAC,QAAQ,CAAC,CAAC;AAC3D,aAAa;AACb,YAAY,OAAO,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAChD;AACA,QAAQ,KAAK,OAAO,CAAC;AACrB,QAAQ,KAAK,MAAM;AACnB,YAAY,OAAO,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAChD;AACA,QAAQ;AACR,YAAY,OAAO,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAClD,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,uBAAuB,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE;AAChE;AACA,IAAI,IAAIF,OAAK,CAAC,OAAO,EAAE;AACvB,QAAQ,IAAI,cAAc,GAAG,IAAI,CAAC;AAClC;AACA,QAAQ,IAAI;AACZ,YAAY,MAAM,eAAe,GAAGkB,OAAsB;AAC1D,gBAAgB,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;AACzC,gBAAgB,UAAU;AAC1B,aAAa,CAAC;AACd,YAAY,MAAM,EAAE,OAAO,GAAG,SAAS,EAAE,GAAGL,SAAO,CAAC,eAAe,CAAC,CAAC;AACrE;AACA,YAAY,cAAc,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AACrD,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,YAAYb,OAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AAChE,YAAY,cAAc,GAAG,OAAO,CAAC;AACrC,SAAS;AACT;AACA,QAAQA,OAAK,CAAC,iBAAiB,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;AAC3D,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa;AACtB,IAAI,EAAE,GAAG,EAAE,wBAAwB,EAAE;AACrC,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,qBAAqB;AACzB,EAAE;AACF,IAAI,MAAM,QAAQ,GAAG,gBAAgB;AACrC,UAAUE,wBAAI,CAAC,OAAO,CAAC,GAAG,EAAE,gBAAgB,CAAC;AAC7C,UAAU,EAAE,CAAC;AACb,IAAI,MAAM,aAAa;AACvB,QAAQ,CAAC,qBAAqB,IAAIA,wBAAI,CAAC,OAAO,CAAC,GAAG,EAAE,qBAAqB,CAAC;AAC1E,SAAS,QAAQ,IAAIA,wBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC5C,QAAQ,GAAG,CAAC;AACZ,IAAI,MAAM,IAAI;AACd,QAAQ,YAAY;AACpB,SAAS,QAAQ,IAAIA,wBAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAClD,QAAQ,EAAE,CAAC;AACX,IAAI,MAAM,cAAc;AACxB,QAAQ,wBAAwB;AAChC,SAAS,QAAQ,IAAIA,wBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC5C,QAAQ,GAAG,CAAC;AACZ,IAAI,MAAM,IAAI,GAAG,YAAY,IAAI,QAAQ,CAAC;AAC1C;AACA,IAAI,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC;AACnE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,MAAM,EAAE;AACjC;AACA;AACA,IAAI,IAAI,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzD;AACA,IAAI,IAAI,gBAAgB,EAAE;AAC1B,QAAQ,OAAO,gBAAgB,CAAC;AAChC,KAAK;AACL;AACA,IAAI,gBAAgB,GAAG;AACvB,QAAQ,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE;AACrC,QAAQ,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;AAC/C,QAAQ,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE;AAC3C,QAAQ,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;AACjC,KAAK,CAAC;AACN;AACA;AACA,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;AACpD;AACA,IAAI,OAAO,gBAAgB,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC;AAChB,QAAQ,oBAAoB,GAAG,IAAI,GAAG,EAAE;AACxC,QAAQ,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE;AAC3B,QAAQ,wBAAwB;AAChC,QAAQ,YAAY;AACpB,QAAQ,QAAQ,GAAG,cAAc;AACjC,QAAQ,aAAa;AACrB,QAAQ,kBAAkB;AAC1B,QAAQ,qBAAqB;AAC7B,QAAQ,0BAA0B;AAClC,KAAK,GAAG,EAAE,EAAE;AACZ,QAAQG,kBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE;AACnC,YAAY,oBAAoB;AAChC,YAAY,GAAG;AACf,YAAY,wBAAwB;AACpC,gBAAgB,wBAAwB;AACxC,gBAAgBH,wBAAI,CAAC,OAAO,CAAC,GAAG,EAAE,wBAAwB,CAAC;AAC3D,YAAY,YAAY;AACxB,YAAY,QAAQ;AACpB,YAAY,aAAa;AACzB,YAAY,kBAAkB;AAC9B,YAAY,qBAAqB;AACjC,YAAY,0BAA0B;AACtC,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;AAC1D,QAAQ,IAAI,CAAC,UAAU,EAAE;AACzB,YAAY,OAAO,IAAI,WAAW,EAAE,CAAC;AACrC,SAAS;AACT;AACA,QAAQ,MAAM,KAAK,GAAGG,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjD,QAAQ,MAAM,GAAG,GAAG,aAAa,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC7E,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACpE;AACA,QAAQ,OAAO,IAAI,WAAW,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC5C,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;AAChD,QAAQ,MAAM,KAAK,GAAGA,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjD,QAAQ,MAAM,GAAG,GAAG,aAAa,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC7E;AACA,QAAQ,OAAO,IAAI,WAAW,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,eAAe,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;AAC5D,QAAQ,MAAM,KAAK,GAAGA,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjD;AACA,QAAQ,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE;AAChD,YAAY,MAAM,GAAG,GAAG,aAAa;AACrC,gBAAgB,KAAK;AACrB,gBAAgB,QAAQ;AACxB,gBAAgB,IAAI;AACpB,gBAAgBH,wBAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC;AAClD,gBAAgB,QAAQ;AACxB,aAAa,CAAC;AACd;AACA,YAAY,IAAIa,sBAAE,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAIA,sBAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;AACnF,gBAAgB,IAAI,UAAU,CAAC;AAC/B;AACA,gBAAgB,IAAI;AACpB,oBAAoB,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC9D,iBAAiB,CAAC,OAAO,KAAK,EAAE;AAChC,oBAAoB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,+BAA+B,EAAE;AAClF,wBAAwB,MAAM,KAAK,CAAC;AACpC,qBAAqB;AACrB,iBAAiB;AACjB;AACA,gBAAgB,IAAI,UAAU,EAAE;AAChC,oBAAoBf,OAAK,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChE,oBAAoB,OAAO,IAAI,WAAW;AAC1C,wBAAwB,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC;AACrE,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA,QAAQA,OAAK,CAAC,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC3D,QAAQ,OAAO,IAAI,WAAW,EAAE,CAAC;AACjC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,8BAA8B,CAAC,aAAa,EAAE;AACzD,QAAQ,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE;AAChD,YAAY,MAAM,QAAQ,GAAGE,wBAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;AAChE;AACA,YAAY,IAAIa,sBAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACzC,gBAAgB,IAAI,QAAQ,KAAK,cAAc,EAAE;AACjD,oBAAoB,IAAI;AACxB,wBAAwB,yBAAyB,CAAC,QAAQ,CAAC,CAAC;AAC5D,wBAAwB,OAAO,QAAQ,CAAC;AACxC,qBAAqB,CAAC,MAAM,gBAAgB;AAC5C,iBAAiB,MAAM;AACvB,oBAAoB,OAAO,QAAQ,CAAC;AACpC,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,gBAAgB,CAAC,QAAQ,EAAE;AAC/B,QAAQ,MAAM,KAAK,GAAGV,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjD,QAAQ,MAAM,GAAG,GAAG,aAAa;AACjC,YAAY,KAAK;AACjB,YAAY,QAAQ;AACpB,YAAY,KAAK,CAAC;AAClB,YAAY,QAAQ;AACpB,YAAY,KAAK,CAAC,GAAG;AACrB,SAAS,CAAC;AACV,QAAQ,MAAM,cAAc,GAAG,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAClE;AACA,QAAQ,OAAO,IAAI,WAAW;AAC9B,YAAY,GAAG,IAAI,CAAC,0BAA0B,CAAC,cAAc,EAAE,GAAG,CAAC;AACnE,SAAS,CAAC;AACV,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,uBAAuB,GAAG;AAC9B,QAAQ,MAAM,KAAK,GAAGA,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjD,QAAQ,MAAM,gBAAgB,GAAGH,wBAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;AAC1E,QAAQ,MAAM,eAAe,GAAGA,wBAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AACxE;AACA,QAAQ,IAAIa,sBAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;AAC7C,YAAY,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAC3D,SAAS;AACT,QAAQ,IAAIA,sBAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;AAC5C,YAAY,MAAM,IAAI,GAAG,kBAAkB,CAAC,eAAe,CAAC,CAAC;AAC7D;AACA,YAAY,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC,EAAE;AACrD,gBAAgB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AACvD,oBAAoB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;AACrG,iBAAiB;AACjB,gBAAgB,MAAM,GAAG,GAAG,aAAa;AACzC,oBAAoB,KAAK;AACzB,oBAAoB,QAAQ;AAC5B,oBAAoB,8BAA8B;AAClD,oBAAoB,eAAe;AACnC,oBAAoB,KAAK,CAAC,GAAG;AAC7B,iBAAiB,CAAC;AAClB;AACA,gBAAgB,OAAO,IAAI,WAAW;AACtC,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;AAC9E,iBAAiB,CAAC;AAClB,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI,WAAW,EAAE,CAAC;AACjC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,eAAe,CAAC,GAAG,EAAE;AACzB,QAAQ,OAAO,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5E,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,0BAA0B,CAAC,cAAc,EAAE,GAAG,EAAE;AACrD,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,0BAA0B;AACxD,YAAY,EAAE,cAAc,EAAE;AAC9B,YAAY,GAAG;AACf,SAAS,CAAC;AACV;AACA;AACA,QAAQ,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACxC,YAAY,IAAI,OAAO,CAAC,aAAa,EAAE;AACvC,gBAAgB,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC;AACnD,aAAa;AACb,YAAY,MAAM,OAAO,CAAC;AAC1B,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,CAAC,UAAU,EAAE,GAAG,EAAE;AAC1C,QAAQ,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;AAChD;AACA,QAAQ,SAAS,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC7E,QAAQ,OAAO,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAChE,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,GAAG,EAAE;AACjD,QAAQ,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,UAAU,EAAE,GAAG,UAAU,CAAC;AACnE,QAAQ,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM;AAC9C,YAAY,KAAK;AACjB,YAAY,aAAa;AACzB,YAAY,GAAG,CAAC,aAAa;AAC7B,SAAS,CAAC;AACV,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,8BAA8B,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC9E;AACA;AACA,QAAQ,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,OAAO,CAAC,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA,YAAY,IAAI,OAAO,CAAC,QAAQ,EAAE;AAClC,gBAAgB,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AACtC,aAAa;AACb;AACA,YAAY,MAAM,OAAO,CAAC;AAC1B,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,8BAA8B;AACnC,QAAQ;AACR,YAAY,GAAG;AACf,YAAY,OAAO,EAAE,MAAM;AAC3B,YAAY,OAAO;AACnB,YAAY,cAAc;AAC1B,YAAY,cAAc;AAC1B,YAAY,MAAM,EAAE,UAAU;AAC9B,YAAY,aAAa;AACzB,YAAY,OAAO,EAAE,UAAU;AAC/B,YAAY,SAAS;AACrB,YAAY,6BAA6B;AACzC,YAAY,IAAI;AAChB,YAAY,KAAK;AACjB,YAAY,QAAQ;AACpB,YAAY,SAAS,EAAE,YAAY,GAAG,EAAE;AACxC,SAAS;AACT,QAAQ,GAAG;AACX,MAAM;AACN,QAAQ,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;AACrE,QAAQ,MAAM,aAAa,GAAG,cAAc,IAAI,IAAI,aAAa;AACjE,YAAY,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,cAAc,GAAG,CAAC,cAAc,CAAC;AAC7E,YAAY,GAAG,CAAC,aAAa;AAC7B,SAAS,CAAC;AACV;AACA;AACA,QAAQ,KAAK,MAAM,UAAU,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;AAC7D,YAAY,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACtD,SAAS;AACT;AACA;AACA,QAAQ,MAAM,MAAM,GAAG,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACvE,QAAQ,MAAM,OAAO,GAAG,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACzE;AACA;AACA,QAAQ,IAAI,OAAO,EAAE;AACrB,YAAY,OAAO,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACnE,SAAS;AACT;AACA;AACA,QAAQ,MAAM;AACd;AACA;AACA,YAAY,IAAI,EAAE,GAAG,CAAC,IAAI;AAC1B,YAAY,IAAI,EAAE,GAAG,CAAC,IAAI;AAC1B,YAAY,QAAQ,EAAE,GAAG,CAAC,QAAQ;AAClC;AACA;AACA,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,GAAG;AACf,YAAY,OAAO;AACnB,YAAY,aAAa;AACzB,YAAY,cAAc;AAC1B,YAAY,MAAM;AAClB,YAAY,aAAa;AACzB,YAAY,OAAO;AACnB,YAAY,SAAS;AACrB,YAAY,6BAA6B;AACzC,YAAY,IAAI;AAChB,YAAY,KAAK;AACjB,YAAY,QAAQ;AACpB,SAAS,CAAC;AACV;AACA;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACtD,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD,gBAAgB,YAAY,CAAC,CAAC,CAAC;AAC/B,gBAAgB,EAAE,GAAG,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;AAC/D,aAAa,CAAC;AACd,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,CAAC,UAAU,EAAE,GAAG,EAAE;AAClC,QAAQf,OAAK,CAAC,qCAAqC,EAAE,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC/E,QAAQ,IAAI;AACZ,YAAY,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AAClD,gBAAgB,OAAO,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACxE,aAAa;AACb,YAAY,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AAClD,gBAAgB,OAAO,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACvE,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACtE,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,YAAY,KAAK,CAAC,OAAO,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9E,YAAY,MAAM,KAAK,CAAC;AACxB,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,0BAA0B,CAAC,UAAU,EAAE,GAAG,EAAE;AAChD,QAAQ,MAAM;AACd,YAAY,aAAa;AACzB,YAAY,kBAAkB;AAC9B,YAAY,qBAAqB;AACjC,YAAY,0BAA0B;AACtC,SAAS,GAAGK,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvC;AACA,QAAQ,IAAI,UAAU,KAAK,oBAAoB,EAAE;AACjD,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;AACvD;AACA,YAAY,IAAI,0BAA0B,EAAE;AAC5C,gBAAgB,IAAI,OAAO,0BAA0B,KAAK,UAAU,EAAE;AACtE,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,0DAA0D,EAAE,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC;AAChI,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC,oBAAoB,CAAC,0BAA0B,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/G,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,eAAe,CAAC;AACxC,gBAAgB,GAAG,GAAG;AACtB,gBAAgB,IAAI;AACpB,gBAAgB,QAAQ,EAAE,qBAAqB;AAC/C,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,IAAI,UAAU,KAAK,YAAY,EAAE;AACzC,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;AACvD;AACA,YAAY,IAAI,kBAAkB,EAAE;AACpC,gBAAgB,IAAI,OAAO,kBAAkB,KAAK,UAAU,EAAE;AAC9D,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,kDAAkD,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;AAChH,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;AACvG,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,eAAe,CAAC;AACxC,gBAAgB,GAAG,GAAG;AACtB,gBAAgB,IAAI;AACpB,gBAAgB,QAAQ,EAAE,aAAa;AACvC,aAAa,CAAC,CAAC;AACf,SAAS;AACT;AACA,QAAQ,MAAM,kBAAkB,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;AAChF,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,yBAAyB,CAAC,UAAU,EAAE,GAAG,EAAE;AAC/C,QAAQ,MAAM,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACvD;AACA,QAAQ,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;AAC/B,YAAY,MAAM,kBAAkB,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;AACjF,SAAS;AACT;AACA,QAAQ,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAC1E,QAAQ,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAC5D;AACA,QAAQ,IAAI,UAAU,CAAC,UAAU,CAAC,EAAE;AACpC,YAAY,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;AAC7E,SAAS;AACT;AACA,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACzD,QAAQ,MAAM,UAAU;AACxB,YAAY,MAAM,CAAC,UAAU;AAC7B,YAAY,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAClD;AACA,QAAQ,IAAI,UAAU,EAAE;AACxB,YAAY,OAAO,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE;AACzD,gBAAgB,GAAG,GAAG;AACtB,gBAAgB,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ;AACzD,gBAAgB,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AACvE,aAAa,CAAC,CAAC;AACf,SAAS;AACT;AACA,QAAQ,MAAM,MAAM,CAAC,KAAK,IAAI,kBAAkB,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;AACpG,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,4BAA4B,CAAC,UAAU,EAAE,GAAG,EAAE;AAClD,QAAQ,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAGA,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7D,QAAQ,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,IAAIH,wBAAI,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;AAChF,QAAQ,IAAI,OAAO,CAAC;AACpB;AACA,QAAQ,IAAI,UAAU,CAAC,UAAU,CAAC,EAAE;AACpC,YAAY,OAAO,GAAG,UAAU,CAAC;AACjC,SAAS,MAAM,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAC/C,YAAY,OAAO,GAAG,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC;AACxC,SAAS,MAAM;AACf,YAAY,OAAO,GAAGiB,oBAA2B;AACjD,gBAAgB,UAAU;AAC1B,gBAAgB,eAAe;AAC/B,aAAa,CAAC;AACd,SAAS;AACT;AACA,QAAQ,IAAI,QAAQ,CAAC;AACrB;AACA,QAAQ,IAAI;AACZ,YAAY,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAC7D,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB;AACA,YAAY,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE;AAC5D,gBAAgB,MAAM,kBAAkB,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;AAC5F,aAAa;AACb,YAAY,MAAM,KAAK,CAAC;AACxB,SAAS;AACT;AACA,QAAQ,uBAAuB,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC/D,QAAQ,OAAO,IAAI,CAAC,eAAe,CAAC;AACpC,YAAY,GAAG,GAAG;AAClB,YAAY,QAAQ;AACpB,YAAY,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC5C,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE;AAC7B,QAAQ,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK;AAC3C,YAAY,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;AAClC,gBAAgB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;AAC7E,aAAa;AACb,YAAY,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACvD;AACA,YAAY,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;AACpC;AACA,YAAY,OAAO,GAAG,CAAC;AACvB,SAAS,EAAE,EAAE,CAAC,CAAC;AACf,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,UAAU,EAAE,GAAG,EAAE;AACjC,QAAQnB,OAAK,CAAC,2BAA2B,EAAE,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;AACrE;AACA,QAAQ,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAGK,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7D,QAAQ,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,IAAIH,wBAAI,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;AAChF;AACA,QAAQ,IAAI;AACZ,YAAY,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AACtE;AACA,YAAY,uBAAuB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AACtE;AACA,YAAY,OAAO,IAAI,gBAAgB,CAAC;AACxC,gBAAgB,UAAU,EAAEW,SAAO,CAAC,QAAQ,CAAC;AAC7C,gBAAgB,QAAQ;AACxB,gBAAgB,EAAE,EAAE,UAAU;AAC9B,gBAAgB,YAAY,EAAE,GAAG,CAAC,IAAI;AACtC,gBAAgB,YAAY,EAAE,GAAG,CAAC,QAAQ;AAC1C,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB;AACA;AACA,YAAY,IAAI,UAAU,KAAK,QAAQ,EAAE;AACzC,gBAAgBb,OAAK,CAAC,kBAAkB,CAAC,CAAC;AAC1C,gBAAgB,OAAO,IAAI,gBAAgB,CAAC;AAC5C,oBAAoB,UAAU,EAAEa,SAAO,CAAC,QAAQ,CAAC;AACjD,oBAAoB,QAAQ,EAAEA,SAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;AACvD,oBAAoB,EAAE,EAAE,UAAU;AAClC,oBAAoB,YAAY,EAAE,GAAG,CAAC,IAAI;AAC1C,oBAAoB,YAAY,EAAE,GAAG,CAAC,QAAQ;AAC9C,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb;AACA,YAAYb,OAAK,CAAC,8CAA8C,EAAE,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACxF,YAAY,KAAK,CAAC,OAAO,GAAG,CAAC,uBAAuB,EAAE,UAAU,CAAC,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAChH;AACA,YAAY,OAAO,IAAI,gBAAgB,CAAC;AACxC,gBAAgB,KAAK;AACrB,gBAAgB,EAAE,EAAE,UAAU;AAC9B,gBAAgB,YAAY,EAAE,GAAG,CAAC,IAAI;AACtC,gBAAgB,YAAY,EAAE,GAAG,CAAC,QAAQ;AAC1C,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE;AAC3B,QAAQA,OAAK,CAAC,2BAA2B,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC/D;AACA,QAAQ,MAAM,EAAE,oBAAoB,EAAE,QAAQ,EAAE,GAAGK,kBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9E,QAAQ,MAAM,OAAO,GAAGc,oBAA2B,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;AAC3E,QAAQ,MAAM,EAAE,GAAGC,gBAAuB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AACrE,QAAQ,MAAM,UAAU,GAAGlB,wBAAI,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;AAC/E;AACA,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAChC,YAAY,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM;AACvC,gBAAgB,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACtE,gBAAgB;AAChB,oBAAoB,eAAe,EAAE,kBAAkB;AACvD,oBAAoB,WAAW,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE;AACxD,iBAAiB;AACjB,aAAa,CAAC;AACd;AACA,YAAY,OAAO,IAAI,gBAAgB,CAAC;AACxC,gBAAgB,KAAK;AACrB,gBAAgB,EAAE;AAClB,gBAAgB,YAAY,EAAE,GAAG,CAAC,IAAI;AACtC,gBAAgB,YAAY,EAAE,GAAG,CAAC,QAAQ;AAC1C,aAAa,CAAC,CAAC;AACf,SAAS;AACT;AACA;AACA,QAAQ,MAAM,MAAM;AACpB,YAAY,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC;AAC7C,YAAY,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACzC;AACA,QAAQ,IAAI,MAAM,EAAE;AACpB,YAAY,OAAO,IAAI,gBAAgB,CAAC;AACxC,gBAAgB,UAAU,EAAE,eAAe,CAAC,MAAM,CAAC;AACnD,gBAAgB,QAAQ,EAAE,MAAM;AAChC,gBAAgB,QAAQ,EAAE,EAAE;AAC5B,gBAAgB,EAAE;AAClB,gBAAgB,YAAY,EAAE,GAAG,CAAC,IAAI;AACtC,gBAAgB,YAAY,EAAE,GAAG,CAAC,QAAQ;AAC1C,aAAa,CAAC,CAAC;AACf,SAAS;AACT;AACA,QAAQ,IAAI,QAAQ,CAAC;AACrB,QAAQ,IAAI,KAAK,CAAC;AAClB;AACA,QAAQ,IAAI;AACZ,YAAY,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAC7D,SAAS,CAAC,OAAO,YAAY,EAAE;AAC/B,YAAY,KAAK,GAAG,YAAY,CAAC;AACjC;AACA,YAAY,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE;AAC5D,gBAAgB,KAAK,CAAC,eAAe,GAAG,gBAAgB,CAAC;AACzD,gBAAgB,KAAK,CAAC,WAAW,GAAG;AACpC,oBAAoB,UAAU,EAAE,OAAO;AACvC,oBAAoB,wBAAwB,EAAE,GAAG,CAAC,cAAc;AAChE,oBAAoB,YAAY,EAAE,GAAG,CAAC,IAAI;AAC1C,iBAAiB,CAAC;AAClB,aAAa;AACb,SAAS;AACT;AACA,QAAQ,IAAI,QAAQ,EAAE;AACtB,YAAY,IAAI;AAChB,gBAAgB,uBAAuB,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AACvE;AACA,gBAAgB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC7C,gBAAgB,MAAM,gBAAgB,GAAGW,SAAO,CAAC,QAAQ,CAAC,CAAC;AAC3D;AACA,gBAAgBb,OAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACnF;AACA,gBAAgB,OAAO,IAAI,gBAAgB,CAAC;AAC5C,oBAAoB,UAAU,EAAE,eAAe,CAAC,gBAAgB,CAAC;AACjE,oBAAoB,QAAQ,EAAE,gBAAgB;AAC9C,oBAAoB,QAAQ;AAC5B,oBAAoB,EAAE;AACtB,oBAAoB,YAAY,EAAE,GAAG,CAAC,IAAI;AAC1C,oBAAoB,YAAY,EAAE,GAAG,CAAC,QAAQ;AAC9C,iBAAiB,CAAC,CAAC;AACnB,aAAa,CAAC,OAAO,SAAS,EAAE;AAChC,gBAAgB,KAAK,GAAG,SAAS,CAAC;AAClC,aAAa;AACb,SAAS;AACT;AACA,QAAQA,OAAK,CAAC,8CAA8C,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9E,QAAQ,KAAK,CAAC,OAAO,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACtG,QAAQ,OAAO,IAAI,gBAAgB,CAAC;AACpC,YAAY,KAAK;AACjB,YAAY,EAAE;AACd,YAAY,YAAY,EAAE,GAAG,CAAC,IAAI;AAClC,YAAY,YAAY,EAAE,GAAG,CAAC,QAAQ;AACtC,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,GAAG,EAAE;AAChD,QAAQ,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AACrD,YAAY,MAAM,UAAU;AAC5B,gBAAgB,OAAO,CAAC,QAAQ,CAAC;AACjC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,CAAC,UAAU;AAC5C,gBAAgB,OAAO,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC;AACxD;AACA,YAAY,IAAI,CAAC,UAAU,EAAE;AAC7B,gBAAgB,SAAS;AACzB,aAAa;AACb;AACA,YAAY,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAC/D,gBAAgB,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACjD,oBAAoB,OAAO,IAAI,CAAC,0BAA0B;AAC1D,wBAAwB;AACxB,4BAA4B,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;AACtD,4BAA4B,SAAS,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACnE,yBAAyB;AACzB,wBAAwB;AACxB,4BAA4B,GAAG,GAAG;AAClC,4BAA4B,IAAI,EAAE,oBAAoB;AACtD,4BAA4B,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC;AACxF,yBAAyB;AACzB,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK;AACL;;ACnoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAkBA;AACA,MAAMA,OAAK,GAAGC,6BAAS,CAAC,yCAAyC,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC;AAC/B,IAAI,kBAAkB;AACtB,IAAI,cAAc;AAClB,IAAI,SAAS;AACb,IAAI,GAAG;AACP,IAAI,SAAS;AACb,CAAC,EAAE;AACH,IAAI,MAAM,eAAe,GAAG,kBAAkB,CAAC,MAAM;AACrD,QAAQ,cAAc;AACtB,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE;AAC9B,KAAK,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,eAAe,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM;AACrD,QAAQ,EAAE,cAAc,EAAE,aAAa,CAAC,eAAe,EAAE;AACzD,QAAQ,EAAE,IAAI,EAAE,sBAAsB,EAAE;AACxC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3C,QAAQ,eAAe,CAAC,IAAI,CAAC;AAC7B,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,IAAI,EAAE,YAAY;AAC9B,YAAY,QAAQ,EAAE,EAAE;AACxB,YAAY,OAAO,EAAE;AACrB,gBAAgB,EAAE,EAAE,IAAI,gBAAgB,CAAC;AACzC,oBAAoB,UAAU,EAAE;AAChC,wBAAwB,KAAK,EAAE,SAAS,CAAC,MAAM;AAC/C,4BAA4B,CAAC,GAAG,EAAE,SAAS,KAAK,MAAM,CAAC,MAAM;AAC7D,gCAAgC,GAAG;AACnC,gCAAgC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC;AACzD,6BAA6B;AAC7B,4BAA4B,EAAE;AAC9B,yBAAyB;AACzB,qBAAqB;AACrB,oBAAoB,QAAQ,EAAE,EAAE;AAChC,oBAAoB,EAAE,EAAE,EAAE;AAC1B,oBAAoB,YAAY,EAAE,YAAY;AAC9C,oBAAoB,YAAY,EAAE,EAAE;AACpC,iBAAiB,CAAC;AAClB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA,IAAI,OAAO,eAAe,CAAC;AAC3B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC;AAC9B,IAAI,aAAa;AACjB,IAAI,kBAAkB;AACtB,IAAI,GAAG;AACP,IAAI,UAAU;AACd,IAAI,kBAAkB;AACtB,CAAC,EAAE;AACH,IAAI,MAAM,cAAc,GAAG,kBAAkB,CAAC,MAAM;AACpD,QAAQ,aAAa;AACrB,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE;AAC9B,KAAK,CAAC;AACN;AACA,IAAI,cAAc,CAAC,OAAO;AAC1B,QAAQ,IAAI,UAAU;AACtB,cAAc,kBAAkB,CAAC,gBAAgB,CAAC,UAAU,CAAC;AAC7D,cAAc,kBAAkB,CAAC,uBAAuB,EAAE,CAAC;AAC3D,KAAK,CAAC;AACN;AACA,IAAI,IAAI,kBAAkB,EAAE;AAC5B,QAAQ,cAAc,CAAC,OAAO;AAC9B,YAAY,GAAG,kBAAkB,CAAC,QAAQ;AAC1C,gBAAgB,kBAAkB;AAClC,gBAAgB,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,EAAE;AACnD,aAAa;AACb,SAAS,CAAC;AACV,KAAK;AACL;AACA,IAAI,OAAO,cAAc,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,0BAA0B,SAAS,KAAK,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,aAAa,EAAE;AAC/B,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,QAAQ,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;AACjD,QAAQ,IAAI,CAAC,WAAW,GAAG,EAAE,aAAa,EAAE,CAAC;AAC7C,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM,2BAA2B,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC;AAChB,QAAQ,oBAAoB,GAAG,IAAI,GAAG,EAAE;AACxC,QAAQ,UAAU,EAAE,cAAc,GAAG,IAAI;AACzC,QAAQ,SAAS,EAAE,aAAa,GAAG,IAAI;AACvC,QAAQ,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE;AAC3B,QAAQ,UAAU;AAClB,QAAQ,wBAAwB;AAChC,QAAQ,SAAS,GAAG,EAAE;AACtB,QAAQ,kBAAkB,GAAG,IAAI;AACjC,QAAQ,WAAW,GAAG,IAAI;AAC1B,QAAQ,YAAY,GAAG,IAAI,GAAG,EAAE;AAChC,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,QAAQ,qBAAqB;AAC7B,QAAQ,0BAA0B;AAClC,QAAQ,aAAa;AACrB,QAAQ,kBAAkB;AAC1B,KAAK,GAAG,EAAE,EAAE;AACZ,QAAQ,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC;AAC1D,YAAY,oBAAoB;AAChC,YAAY,GAAG;AACf,YAAY,wBAAwB;AACpC,YAAY,YAAY;AACxB,YAAY,QAAQ;AACpB,YAAY,qBAAqB;AACjC,YAAY,0BAA0B;AACtC,YAAY,aAAa;AACzB,YAAY,kBAAkB;AAC9B,SAAS,CAAC,CAAC;AACX;AACA,QAAQ,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE;AACnC,YAAY,eAAe,EAAE,qBAAqB,CAAC;AACnD,gBAAgB,cAAc;AAC9B,gBAAgB,kBAAkB;AAClC,gBAAgB,GAAG;AACnB,gBAAgB,SAAS;AACzB,gBAAgB,SAAS;AACzB,aAAa,CAAC;AACd,YAAY,cAAc;AAC1B,YAAY,cAAc,EAAE,oBAAoB,CAAC;AACjD,gBAAgB,aAAa;AAC7B,gBAAgB,kBAAkB;AAClC,gBAAgB,GAAG;AACnB,gBAAgB,UAAU;AAC1B,gBAAgB,kBAAkB;AAClC,aAAa,CAAC;AACd,YAAY,aAAa;AACzB,YAAY,kBAAkB;AAC9B,YAAY,WAAW,EAAE,IAAI,GAAG,EAAE;AAClC,YAAY,GAAG;AACf,YAAY,aAAa,EAAE,IAAI,OAAO,EAAE;AACxC,YAAY,UAAU;AACtB,YAAY,SAAS;AACrB,YAAY,kBAAkB;AAC9B,YAAY,WAAW;AACvB,YAAY,YAAY;AACxB,YAAY,SAAS;AACrB,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,MAAM,EAAE,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACnD;AACA,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qBAAqB,CAAC,QAAQ,EAAE,EAAE,mBAAmB,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;AAC1E,QAAQ,MAAM;AACd,YAAY,eAAe;AAC3B,YAAY,cAAc;AAC1B,YAAY,GAAG;AACf,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvC;AACA,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACvB,YAAY,OAAO,IAAI,WAAW,CAAC,GAAG,eAAe,EAAE,GAAG,cAAc,CAAC,CAAC;AAC1E,SAAS;AACT;AACA,QAAQ,MAAM,aAAa,GAAGC,wBAAI,CAAC,OAAO,CAACA,wBAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;AACxE;AACA,QAAQF,OAAK,CAAC,CAAC,sBAAsB,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD;AACA,QAAQ,OAAO,IAAI,CAAC,oBAAoB;AACxC,YAAY,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;AACtD,YAAY,aAAa;AACzB,YAAY,mBAAmB;AAC/B,SAAS,CAAC;AACV,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,CAAC,UAAU,EAAE;AAClC,QAAQ,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjD;AACA,QAAQ,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;AACzC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,UAAU,GAAG;AACjB,QAAQ,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjD;AACA,QAAQ,KAAK,CAAC,eAAe,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;AAC7D,QAAQ,KAAK,CAAC,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAC3D,QAAQ,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;AAClC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,sBAAsB,CAAC,aAAa,EAAE,qBAAqB,GAAG,KAAK,EAAE;AACzE,QAAQ,MAAM;AACd,YAAY,eAAe;AAC3B,YAAY,kBAAkB;AAC9B,YAAY,WAAW;AACvB,YAAY,GAAG;AACf,YAAY,WAAW;AACvB,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvC;AACA,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,OAAO,eAAe,CAAC;AACnC,SAAS;AACT;AACA,QAAQ,IAAI,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACzD;AACA;AACA,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAYA,OAAK,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,YAAY,OAAO,WAAW,CAAC;AAC/B,SAAS;AACT,QAAQA,OAAK,CAAC,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD;AACA,QAAQ,MAAM,QAAQ,GAAGqB,sBAAE,CAAC,OAAO,EAAE,CAAC;AACtC;AACA;AACA,QAAQ,IAAI,aAAa,KAAK,QAAQ,IAAI,GAAG,KAAK,QAAQ,EAAE;AAC5D,YAAYrB,OAAK,CAAC,6CAA6C,CAAC,CAAC;AACjE,YAAY,IAAI,qBAAqB,EAAE;AACvC,gBAAgB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,8BAA8B,CAAC,aAAa,CAAC,CAAC;AAClG;AACA,gBAAgB,IAAI,QAAQ,EAAE;AAC9B,oBAAoB,sBAAsB;AAC1C,wBAAwB,QAAQ;AAChC,wBAAwB,iCAAiC;AACzD,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;AACrE,SAAS;AACT;AACA;AACA,QAAQ,IAAI;AACZ,YAAY,WAAW,GAAG,kBAAkB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC5E,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB;AACA,YAAY,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;AACzC,gBAAgBA,OAAK,CAAC,4CAA4C,CAAC,CAAC;AACpE,gBAAgB,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;AACzE,aAAa;AACb,YAAY,MAAM,KAAK,CAAC;AACxB,SAAS;AACT;AACA,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE;AAC5D,YAAYA,OAAK,CAAC,yCAAyC,CAAC,CAAC;AAC7D,YAAY,WAAW,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC,CAAC;AACpD,YAAY,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AACjE,SAAS;AACT;AACA;AACA,QAAQ,MAAM,UAAU,GAAGE,wBAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACvD,QAAQ,MAAM,iBAAiB,GAAG,UAAU,IAAI,UAAU,KAAK,aAAa;AAC5E,cAAc,IAAI,CAAC,sBAAsB;AACzC,gBAAgB,UAAU;AAC1B,gBAAgB,qBAAqB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC;AAC/D,aAAa;AACb,cAAc,eAAe,CAAC;AAC9B;AACA,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,YAAY,WAAW,CAAC,OAAO,CAAC,GAAG,iBAAiB,CAAC,CAAC;AACtD,SAAS,MAAM;AACf,YAAY,WAAW,GAAG,iBAAiB,CAAC;AAC5C,SAAS;AACT;AACA;AACA,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AAC7D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,CAAC,aAAa,EAAE,WAAW,EAAE;AAC7C,QAAQ,MAAM,EAAE,WAAW,EAAE,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3D;AACA,QAAQ,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AACnC,QAAQ,WAAW,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AACpD;AACA,QAAQ,OAAO,WAAW,CAAC;AAC3B,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,CAAC,WAAW,EAAE,aAAa,EAAE,mBAAmB,EAAE;AAC1E,QAAQ,MAAM;AACd,YAAY,cAAc;AAC1B,YAAY,kBAAkB;AAC9B,YAAY,aAAa;AACzB,YAAY,WAAW;AACvB,YAAY,YAAY;AACxB,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvC;AACA,QAAQ,IAAI,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC9D;AACA,QAAQ,IAAI,CAAC,gBAAgB,EAAE;AAC/B,YAAY,gBAAgB,GAAG,WAAW,CAAC;AAC3C;AACA;AACA,YAAY;AACZ,gBAAgB,WAAW;AAC3B,gBAAgB,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;AACnD,gBAAgB,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;AACtD,cAAc;AACd,gBAAgB,MAAM,QAAQ,GAAGmB,sBAAE,CAAC,OAAO,EAAE,CAAC;AAC9C;AACA,gBAAgBrB,OAAK,CAAC,gDAAgD,EAAE,QAAQ,CAAC,CAAC;AAClF;AACA,gBAAgB,MAAM,mBAAmB,GAAG,kBAAkB,CAAC,eAAe;AAC9E,oBAAoB,QAAQ;AAC5B,oBAAoB,EAAE,IAAI,EAAE,gBAAgB,EAAE;AAC9C,iBAAiB,CAAC;AAClB;AACA,gBAAgB;AAChB,oBAAoB,mBAAmB,CAAC,MAAM,GAAG,CAAC;AAClD,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AACvD,kBAAkB;AAClB,oBAAoB,MAAM,WAAW;AACrC,wBAAwB,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD;AACA,oBAAoB,sBAAsB;AAC1C,wBAAwB,WAAW,CAAC,QAAQ;AAC5C,wBAAwB,6BAA6B;AACrD,qBAAqB,CAAC;AACtB,iBAAiB;AACjB;AACA,gBAAgB,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAChF,aAAa;AACb;AACA;AACA,YAAY,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3C,gBAAgB,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AAC3E,aAAa;AACb;AACA;AACA,YAAY,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC;AAClD,gBAAgB,YAAY;AAC5B,aAAa,CAAC,CAAC;AACf;AACA,YAAY,SAAS,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAC5D;AACA;AACA,YAAY,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAC5C,YAAY,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;AAC7D;AACA,YAAYA,OAAK;AACjB,gBAAgB,wCAAwC;AACxD,gBAAgB,gBAAgB;AAChC,gBAAgB,aAAa;AAC7B,aAAa,CAAC;AACd,SAAS;AACT;AACA;AACA,QAAQ,IAAI,CAAC,mBAAmB,IAAI,WAAW,IAAI,gBAAgB,CAAC,MAAM,IAAI,CAAC,EAAE;AACjF,YAAY,MAAM,IAAI,0BAA0B,CAAC,aAAa,CAAC,CAAC;AAChE,SAAS;AACT;AACA,QAAQ,OAAO,gBAAgB,CAAC;AAChC,KAAK;AACL;;AC/gBA;AACA;AACA;AACA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,GAAGsB,6BAAW,CAAC,sBAAsB,CAAC,CAAC;AAClD,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,iBAAiB,CAAC,cAAc,EAAE;AAC3C,IAAI,uBAAuB;AAC3B,IAAI,wBAAwB;AAC5B,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AACpB,CAAC,EAAE;AACH;AACA,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC;AAC1B,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,MAAM,eAAe,GAAG,EAAE,CAAC;AAC/B,IAAI,MAAM,aAAa,GAAG,EAAE,CAAC;AAC7B,IAAI,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;AAC1D,IAAI,MAAM,yBAAyB,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;AAC7E,IAAI,MAAM,uBAAuB,GAAG,CAAC,gBAAgB,EAAE,+BAA+B,CAAC,CAAC;AACxF;AACA;AACA,IAAI,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;AAClC,QAAQ,IAAI,GAAG,IAAI,cAAc,IAAI,OAAO,cAAc,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;AACjF,YAAY,UAAU,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;AAClD,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,KAAK,MAAM,GAAG,IAAI,yBAAyB,EAAE;AACjD,QAAQ,IAAI,GAAG,IAAI,cAAc,IAAI,OAAO,cAAc,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;AACjF;AACA;AACA,YAAY,UAAU,CAAC,eAAe,GAAG,eAAe,CAAC;AACzD;AACA,YAAY,IAAI,GAAG,KAAK,QAAQ,EAAE;AAClC,gBAAgB,KAAK,CAAC,CAAC,kBAAkB,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC,CAAC,CAAC;AAC3G;AACA,gBAAgB,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE;AAC/C,oBAAoB,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;AACpD,iBAAiB;AACjB;AACA,gBAAgB,eAAe,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC;AACtE,gBAAgB,SAAS;AACzB,aAAa;AACb;AACA;AACA,YAAY,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,OAAO,cAAc,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;AAChF,gBAAgB,eAAe,CAAC,GAAG,CAAC,GAAG;AACvC,oBAAoB,GAAG,cAAc,CAAC,GAAG,CAAC;AAC1C,iBAAiB,CAAC;AAClB,aAAa,MAAM;AACnB,gBAAgB,eAAe,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;AAC3D,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,KAAK,MAAM,GAAG,IAAI,uBAAuB,EAAE;AAC/C,QAAQ,IAAI,GAAG,IAAI,cAAc,IAAI,OAAO,cAAc,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;AACjF,YAAY,UAAU,CAAC,aAAa,GAAG,aAAa,CAAC;AACrD,YAAY,aAAa,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;AACrD,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,IAAI,eAAe,CAAC,aAAa,EAAE;AACvC;AACA,QAAQ,IAAI,aAAa,IAAI,eAAe,CAAC,aAAa,EAAE;AAC5D,YAAY,eAAe,CAAC,WAAW,GAAG,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;AACpF,YAAY,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;AAC7D,SAAS;AACT;AACA,QAAQ,IAAI,YAAY,IAAI,eAAe,CAAC,aAAa,EAAE;AAC3D,YAAY,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;AAClF,YAAY,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;AAC5D,SAAS;AACT;AACA;AACA,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AACrE,YAAY,OAAO,eAAe,CAAC,aAAa,CAAC;AACjD,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,IAAI,cAAc,CAAC,QAAQ,EAAE;AACjC,QAAQ,UAAU,CAAC,KAAK,GAAG,CAAC,gBAAgB,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAChG,KAAK;AACL;AACA;AACA,IAAI,IAAI,cAAc,CAAC,OAAO,IAAI,OAAO,cAAc,CAAC,OAAO,KAAK,QAAQ,EAAE;AAC9E,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAChE;AACA,QAAQ,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;AAChC;AACA,QAAQ,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AACtE;AACA,YAAY,KAAK,CAAC,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AACvD,YAAY,KAAK,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,aAAa,EAAE,wBAAwB,CAAC,CAAC,CAAC,CAAC;AAC7F;AACA,YAAY,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACnF;AACA,YAAY,IAAI,KAAK,EAAE;AACvB,gBAAgB,MAAM,KAAK,CAAC;AAC5B,aAAa;AACb;AACA,YAAY,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC;AACpD;AACA;AACA,YAAY,IAAI,MAAM,CAAC,UAAU,EAAE;AACnC,gBAAgB,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;AAC5E,oBAAoB,IAAI,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACvD,wBAAwB,KAAK,CAAC,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;AACrF;AACA,wBAAwB,OAAO,CAAC,OAAO,CAAC;AACxC,4BAA4B,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC;AAC3D,4BAA4B,SAAS,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;AAC7F,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB;AACrB;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,IAAI,cAAc,CAAC,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,KAAK,QAAQ,EAAE;AACtE,QAAQ,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC/D;AACA;AACA,YAAY,IAAI,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC7C,gBAAgB,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAC7D;AACA,gBAAgB,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC/C;AACA;AACA,oBAAoB,OAAO,CAAC,OAAO,CAAC,GAAG,iBAAiB,CAAC;AACzD,wBAAwB,QAAQ,EAAE,cAAc,CAAC,QAAQ;AACzD,wBAAwB,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AACpD,qBAAqB,EAAE;AACvB,wBAAwB,uBAAuB;AAC/C,wBAAwB,wBAAwB;AAChD,qBAAqB,CAAC,CAAC,CAAC;AACxB,iBAAiB,MAAM,IAAI,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC5D;AACA;AACA,oBAAoB,OAAO,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC;AACtD,wBAAwB,QAAQ,EAAE,cAAc,CAAC,QAAQ;AACzD,wBAAwB,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC;AAC1D,qBAAqB,EAAE;AACvB,wBAAwB,uBAAuB;AAC/C,wBAAwB,wBAAwB;AAChD,qBAAqB,CAAC,CAAC,CAAC;AACxB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5C,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACjC,KAAK;AACL;AACA,IAAI,OAAO,OAAO,CAAC;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,CAAC;AACjB;AACA,IAAI,WAAW,CAAC;AAChB,QAAQ,aAAa,GAAG,OAAO,CAAC,GAAG,EAAE;AACrC,QAAQ,wBAAwB,GAAG,aAAa;AAChD,QAAQ,iBAAiB;AACzB,QAAQ,SAAS;AACjB,KAAK,GAAG,EAAE,EAAE;AACZ,QAAQ,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AAC3C,QAAQ,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;AACjE,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,kBAAkB,CAAC;AACjD,YAAY,GAAG,EAAE,aAAa;AAC9B,YAAY,wBAAwB;AACpC,YAAY,kBAAkB,GAAG;AACjC;AACA,gBAAgB,IAAI,CAAC,SAAS,EAAE;AAChC,oBAAoB,MAAM,IAAI,SAAS,CAAC,0DAA0D,CAAC,CAAC;AACpG,iBAAiB;AACjB;AACA,gBAAgB,OAAO,SAAS,CAAC;AACjC,aAAa;AACb,YAAY,0BAA0B,GAAG;AACzC;AACA,gBAAgB,IAAI,CAAC,iBAAiB,EAAE;AACxC,oBAAoB,MAAM,IAAI,SAAS,CAAC,kEAAkE,CAAC,CAAC;AAC5G,iBAAiB;AACjB;AACA,gBAAgB,OAAO,iBAAiB,CAAC;AACzC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,CAAC,cAAc,EAAE;AAC3B,QAAQ,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,cAAc,EAAE;AACrE,YAAY,QAAQ,EAAE,IAAI,CAAC,aAAa;AACxC,SAAS,CAAC,CAAC;AACX;AACA,QAAQ,MAAM,SAAS,GAAG,EAAE,CAAC;AAC7B,QAAQ,IAAI,iBAAiB,GAAG,KAAK,CAAC;AACtC;AACA,QAAQ,aAAa,CAAC,OAAO,CAAC,UAAU,IAAI;AAC5C,YAAY,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC9C,gBAAgB,iBAAiB,GAAG,iBAAiB,IAAI,UAAU,CAAC,aAAa,CAAC;AAClF,gBAAgB,SAAS,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,UAAU,EAAE;AAChE,oBAAoB,uBAAuB,EAAEpB,wBAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,kBAAkB,CAAC;AAC9F,oBAAoB,wBAAwB,EAAEA,wBAAI,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,kBAAkB,CAAC;AAC1G,oBAAoB,kBAAkB,EAAE,aAAa,CAAC,kBAAkB;AACxE,oBAAoB,gBAAgB,EAAE,aAAa,CAAC,gBAAgB;AACpE,iBAAiB,CAAC,CAAC,CAAC;AACpB,aAAa;AACb,SAAS,CAAC,CAAC;AACX;AACA;AACA,QAAQ,IAAI,iBAAiB,EAAE;AAC/B,YAAY,SAAS,CAAC,OAAO,CAAC;AAC9B,gBAAgB,OAAO,EAAE,CAAC,QAAQ,IAAI;AACtC;AACA;AACA;AACA,oBAAoB,MAAM,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC9E;AACA;AACA,oBAAoB,OAAO,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACzF,iBAAiB,CAAC;AAClB,aAAa,CAAC,CAAC;AACf,SAAS;AACT;AACA,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,GAAG,CAAC,SAAS,EAAE;AACnB,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC;AAC3B,YAAY,GAAG,EAAE,SAAS;AAC1B,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,CAAC,GAAG,eAAe,EAAE;AAChC,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC;AAC3B,YAAY,OAAO,EAAE,eAAe;AACpC,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,CAAC,GAAG,OAAO,EAAE;AACxB,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC;AAC3B,YAAY,OAAO;AACnB,SAAS,CAAC,CAAC;AACX,KAAK;AACL;;AC5TA;AACA;AACA;AACA;AAuBA;AACA;AACA;AACA;AACA;AACK,MAAC,MAAM,GAAG;AACf,IAAI,WAAW;AACf,qCAAIqB,aAA+B;AACnC,IAAI,2BAA2B;AAC/B,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,uBAAuB;AAC3B,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB;AACA;AACA,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,MAAM;AACV;;;;;"}