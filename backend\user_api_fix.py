#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复用户API问题的独立测试
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, jsonify, request
from flask_cors import CORS
from src.models.inventory import db
from src.models.user import User

# 创建Flask应用，禁用静态文件
app = Flask(__name__, static_folder=None, static_url_path=None)
app.config['SECRET_KEY'] = 'test_key'

# 启用CORS
CORS(app)

# 数据库配置
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'src', 'database', 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

@app.route('/')
def index():
    return jsonify({'message': '用户API测试服务器', 'status': 'running'})

@app.route('/api/users', methods=['GET'])
def get_users():
    """获取用户列表"""
    try:
        users = User.query.all()
        result = [user.to_dict() for user in users]
        return jsonify({'code': 200, 'message': '获取用户列表成功', 'success': True, 'data': result})
    except Exception as e:
        return jsonify({'code': 500, 'message': f'获取用户列表失败：{str(e)}', 'success': False}), 500

@app.route('/api/users', methods=['POST'])
def create_user():
    """创建用户"""
    try:
        data = request.json
        
        # 验证必填字段
        if not data or not data.get('username'):
            return jsonify({'code': 400, 'message': '用户名不能为空', 'success': False}), 400
        if not data.get('email'):
            return jsonify({'code': 400, 'message': '邮箱不能为空', 'success': False}), 400
        
        # 检查用户名是否已存在
        existing_user = User.query.filter_by(username=data.get('username')).first()
        if existing_user:
            return jsonify({'code': 400, 'message': '用户名已存在，请使用其他用户名', 'success': False}), 400
        
        # 检查邮箱是否已存在
        existing_email = User.query.filter_by(email=data.get('email')).first()
        if existing_email:
            return jsonify({'code': 400, 'message': '邮箱已存在，请使用其他邮箱', 'success': False}), 400
        
        # 创建用户
        user = User(
            username=data.get('username'),
            email=data.get('email')
        )
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify({'code': 200, 'message': '用户创建成功', 'success': True, 'data': user.to_dict()})
    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'message': f'创建用户失败：{str(e)}', 'success': False}), 500

@app.route('/api/users/<int:user_id>', methods=['GET'])
def get_user(user_id):
    """获取单个用户"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'code': 404, 'message': '用户不存在', 'success': False}), 404
        return jsonify({'code': 200, 'message': '获取用户信息成功', 'success': True, 'data': user.to_dict()})
    except Exception as e:
        return jsonify({'code': 500, 'message': f'获取用户信息失败：{str(e)}', 'success': False}), 500

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
def delete_user(user_id):
    """删除用户"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'code': 404, 'message': '用户不存在', 'success': False}), 404
        
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({'code': 200, 'message': '用户删除成功', 'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'message': f'删除用户失败：{str(e)}', 'success': False}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    
    print("🚀 启动用户API修复测试服务器...")
    print("📡 用户API地址: http://localhost:5003/api/users")
    print("🏠 主页: http://localhost:5003/")
    app.run(host='0.0.0.0', port=5003, debug=True)
