"use strict";
exports.gu = void 0;
var _index = require("./gu/_lib/formatDistance.cjs");
var _index2 = require("./gu/_lib/formatLong.cjs");
var _index3 = require("./gu/_lib/formatRelative.cjs");
var _index4 = require("./gu/_lib/localize.cjs");
var _index5 = require("./gu/_lib/match.cjs");

/**
 * @category Locales
 * @summary Gujarati locale (India).
 * @language Gujarati
 * @iso-639-2 guj
 * <AUTHOR> [@ManadayM](https://github.com/manadaym)
 */
const gu = (exports.gu = {
  code: "gu",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
