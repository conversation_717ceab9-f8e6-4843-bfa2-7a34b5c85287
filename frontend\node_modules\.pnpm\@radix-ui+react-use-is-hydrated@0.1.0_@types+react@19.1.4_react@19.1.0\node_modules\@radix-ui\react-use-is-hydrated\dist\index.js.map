{"version": 3, "sources": ["../src/index.ts", "../src/use-is-hydrated.tsx"], "sourcesContent": ["export { useIsHydrated } from './use-is-hydrated';\n", "import { useSyncExternalStore } from 'use-sync-external-store/shim';\n\n/**\n * Determines whether or not the component tree has been hydrated.\n */\nexport function useIsHydrated() {\n  return useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\n\nfunction subscribe() {\n  return () => {};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAAqC;AAK9B,SAAS,gBAAgB;AAC9B,aAAO;AAAA,IACL;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AACF;AAEA,SAAS,YAAY;AACnB,SAAO,MAAM;AAAA,EAAC;AAChB;", "names": []}