(()=>{var q;function Z(B){return Z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},Z(B)}function I(B,C){var H=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);C&&(J=J.filter(function(M){return Object.getOwnPropertyDescriptor(B,M).enumerable})),H.push.apply(H,J)}return H}function F(B){for(var C=1;C<arguments.length;C++){var H=arguments[C]!=null?arguments[C]:{};C%2?I(Object(H),!0).forEach(function(J){T(B,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(H)):I(Object(H)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(H,J))})}return B}function T(B,C,H){if(C=E(C),C in B)Object.defineProperty(B,C,{value:H,enumerable:!0,configurable:!0,writable:!0});else B[C]=H;return B}function E(B){var C=D(B,"string");return Z(C)=="symbol"?C:String(C)}function D(B,C){if(Z(B)!="object"||!B)return B;var H=B[Symbol.toPrimitive];if(H!==void 0){var J=H.call(B,C||"default");if(Z(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}var G=Object.defineProperty,XB=function B(C,H){for(var J in H)G(C,J,{get:H[J],enumerable:!0,configurable:!0,set:function M(Q){return H[J]=function(){return Q}}})};function O(B){var C=B.charAt(0).toLowerCase();if(P.indexOf(C)!=-1||N.indexOf(C)!=-1)return!0;var H=B.split(" ")[0],J=parseInt(H);if(!isNaN(J)&&S.indexOf(J%10)!=-1&&x.indexOf(parseInt(H.substring(0,2)))==-1)return!0;return!1}var V={lessThanXSeconds:{standalone:{one:"manner w\xE9i eng Sekonn",other:"manner w\xE9i {{count}} Sekonnen"},withPreposition:{one:"manner w\xE9i enger Sekonn",other:"manner w\xE9i {{count}} Sekonnen"}},xSeconds:{standalone:{one:"eng Sekonn",other:"{{count}} Sekonnen"},withPreposition:{one:"enger Sekonn",other:"{{count}} Sekonnen"}},halfAMinute:{standalone:"eng hallef Minutt",withPreposition:"enger hallwer Minutt"},lessThanXMinutes:{standalone:{one:"manner w\xE9i eng Minutt",other:"manner w\xE9i {{count}} Minutten"},withPreposition:{one:"manner w\xE9i enger Minutt",other:"manner w\xE9i {{count}} Minutten"}},xMinutes:{standalone:{one:"eng Minutt",other:"{{count}} Minutten"},withPreposition:{one:"enger Minutt",other:"{{count}} Minutten"}},aboutXHours:{standalone:{one:"ongef\xE9ier eng Stonn",other:"ongef\xE9ier {{count}} Stonnen"},withPreposition:{one:"ongef\xE9ier enger Stonn",other:"ongef\xE9ier {{count}} Stonnen"}},xHours:{standalone:{one:"eng Stonn",other:"{{count}} Stonnen"},withPreposition:{one:"enger Stonn",other:"{{count}} Stonnen"}},xDays:{standalone:{one:"een Dag",other:"{{count}} Deeg"},withPreposition:{one:"engem Dag",other:"{{count}} Deeg"}},aboutXWeeks:{standalone:{one:"ongef\xE9ier eng Woch",other:"ongef\xE9ier {{count}} Wochen"},withPreposition:{one:"ongef\xE9ier enger Woche",other:"ongef\xE9ier {{count}} Wochen"}},xWeeks:{standalone:{one:"eng Woch",other:"{{count}} Wochen"},withPreposition:{one:"enger Woch",other:"{{count}} Wochen"}},aboutXMonths:{standalone:{one:"ongef\xE9ier ee Mount",other:"ongef\xE9ier {{count}} M\xE9int"},withPreposition:{one:"ongef\xE9ier engem Mount",other:"ongef\xE9ier {{count}} M\xE9int"}},xMonths:{standalone:{one:"ee Mount",other:"{{count}} M\xE9int"},withPreposition:{one:"engem Mount",other:"{{count}} M\xE9int"}},aboutXYears:{standalone:{one:"ongef\xE9ier ee Joer",other:"ongef\xE9ier {{count}} Joer"},withPreposition:{one:"ongef\xE9ier engem Joer",other:"ongef\xE9ier {{count}} Joer"}},xYears:{standalone:{one:"ee Joer",other:"{{count}} Joer"},withPreposition:{one:"engem Joer",other:"{{count}} Joer"}},overXYears:{standalone:{one:"m\xE9i w\xE9i ee Joer",other:"m\xE9i w\xE9i {{count}} Joer"},withPreposition:{one:"m\xE9i w\xE9i engem Joer",other:"m\xE9i w\xE9i {{count}} Joer"}},almostXYears:{standalone:{one:"bal ee Joer",other:"bal {{count}} Joer"},withPreposition:{one:"bal engem Joer",other:"bal {{count}} Joer"}}},N=["d","h","n","t","z"],P=["a,","e","i","o","u"],S=[0,1,2,3,8,9],x=[40,50,60,70],R=function B(C,H,J){var M,Q=V[C],U=J!==null&&J!==void 0&&J.addSuffix?Q.withPreposition:Q.standalone;if(typeof U==="string")M=U;else if(H===1)M=U.one;else M=U.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"a"+(O(M)?"n":"")+" "+M;else return"viru"+(O(M)?"n":"")+" "+M;return M};function L(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=C.width?String(C.width):B.defaultWidth,J=B.formats[H]||B.formats[B.defaultWidth];return J}}var w={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.yy"},W={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},j={full:"{{date}} 'um' {{time}}",long:"{{date}} 'um' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},_={date:L({formats:w,defaultWidth:"full"}),time:L({formats:W,defaultWidth:"full"}),dateTime:L({formats:j,defaultWidth:"full"})},b={lastWeek:function B(C){var H=C.getDay(),J="'l\xE4schte";if(H===2||H===4)J+="n";return J+="' eeee 'um' p",J},yesterday:"'g\xEBschter um' p",today:"'haut um' p",tomorrow:"'moien um' p",nextWeek:"eeee 'um' p",other:"P"},f=function B(C,H,J,M){var Q=b[C];if(typeof Q==="function")return Q(H);return Q};function $(B){return function(C,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",M;if(J==="formatting"&&B.formattingValues){var Q=B.defaultFormattingWidth||B.defaultWidth,U=H!==null&&H!==void 0&&H.width?String(H.width):Q;M=B.formattingValues[U]||B.formattingValues[Q]}else{var X=B.defaultWidth,K=H!==null&&H!==void 0&&H.width?String(H.width):B.defaultWidth;M=B.values[K]||B.values[X]}var Y=B.argumentCallback?B.argumentCallback(C):C;return M[Y]}}var v={narrow:["v.Chr.","n.Chr."],abbreviated:["v.Chr.","n.Chr."],wide:["viru Christus","no Christus"]},h={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. Quartal","2. Quartal","3. Quartal","4. Quartal"]},c={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","M\xE4e","Abr","Mee","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],wide:["Januar","Februar","M\xE4erz","Abr\xEBll","Mee","Juni","Juli","August","September","Oktober","November","Dezember"]},m={narrow:["S","M","D","M","D","F","S"],short:["So","M\xE9","D\xEB","M\xEB","Do","Fr","Sa"],abbreviated:["So.","M\xE9.","D\xEB.","M\xEB.","Do.","Fr.","Sa."],wide:["Sonndeg","M\xE9indeg","D\xEBnschdeg","M\xEBttwoch","Donneschdeg","Freideg","Samschdeg"]},k={narrow:{am:"mo.",pm:"nom\xEB.",midnight:"M\xEBtternuecht",noon:"M\xEBtteg",morning:"Moien",afternoon:"Nom\xEBtteg",evening:"Owend",night:"Nuecht"},abbreviated:{am:"moies",pm:"nom\xEBttes",midnight:"M\xEBtternuecht",noon:"M\xEBtteg",morning:"Moien",afternoon:"Nom\xEBtteg",evening:"Owend",night:"Nuecht"},wide:{am:"moies",pm:"nom\xEBttes",midnight:"M\xEBtternuecht",noon:"M\xEBtteg",morning:"Moien",afternoon:"Nom\xEBtteg",evening:"Owend",night:"Nuecht"}},y={narrow:{am:"mo.",pm:"nom.",midnight:"M\xEBtternuecht",noon:"m\xEBttes",morning:"moies",afternoon:"nom\xEBttes",evening:"owes",night:"nuets"},abbreviated:{am:"moies",pm:"nom\xEBttes",midnight:"M\xEBtternuecht",noon:"m\xEBttes",morning:"moies",afternoon:"nom\xEBttes",evening:"owes",night:"nuets"},wide:{am:"moies",pm:"nom\xEBttes",midnight:"M\xEBtternuecht",noon:"m\xEBttes",morning:"moies",afternoon:"nom\xEBttes",evening:"owes",night:"nuets"}},d=function B(C,H){var J=Number(C);return J+"."},l={ordinalNumber:d,era:$({values:v,defaultWidth:"wide"}),quarter:$({values:h,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:$({values:c,defaultWidth:"wide"}),day:$({values:m,defaultWidth:"wide"}),dayPeriod:$({values:k,defaultWidth:"wide",formattingValues:y,defaultFormattingWidth:"wide"})};function z(B){return function(C){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,M=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Q=C.match(M);if(!Q)return null;var U=Q[0],X=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],K=Array.isArray(X)?u(X,function(A){return A.test(U)}):p(X,function(A){return A.test(U)}),Y;Y=B.valueCallback?B.valueCallback(K):K,Y=H.valueCallback?H.valueCallback(Y):Y;var UB=C.slice(U.length);return{value:Y,rest:UB}}}function p(B,C){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&C(B[H]))return H;return}function u(B,C){for(var H=0;H<B.length;H++)if(C(B[H]))return H;return}function g(B){return function(C){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=C.match(B.matchPattern);if(!J)return null;var M=J[0],Q=C.match(B.parsePattern);if(!Q)return null;var U=B.valueCallback?B.valueCallback(Q[0]):Q[0];U=H.valueCallback?H.valueCallback(U):U;var X=C.slice(M.length);return{value:U,rest:X}}}var i=/^(\d+)(\.)?/i,n=/\d+/i,s={narrow:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,abbreviated:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,wide:/^(viru Christus|virun eiser Zäitrechnung|no Christus|eiser Zäitrechnung)/i},o={any:[/^v/i,/^n/i]},a={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? Quartal/i},r={any:[/1/i,/2/i,/3/i,/4/i]},e={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mäe|abr|mee|jun|jul|aug|sep|okt|nov|dez)/i,wide:/^(januar|februar|mäerz|abrëll|mee|juni|juli|august|september|oktober|november|dezember)/i},t={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mä/i,/^ab/i,/^me/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},BB={narrow:/^[smdf]/i,short:/^(so|mé|dë|më|do|fr|sa)/i,abbreviated:/^(son?|méi?|dën?|mët?|don?|fre?|sam?)\.?/i,wide:/^(sonndeg|méindeg|dënschdeg|mëttwoch|donneschdeg|freideg|samschdeg)/i},CB={any:[/^so/i,/^mé/i,/^dë/i,/^më/i,/^do/i,/^f/i,/^sa/i]},HB={narrow:/^(mo\.?|nomë\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,abbreviated:/^(moi\.?|nomët\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,wide:/^(moies|nomëttes|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i},JB={any:{am:/^m/i,pm:/^n/i,midnight:/^Mëtter/i,noon:/^mëttes/i,morning:/moies/i,afternoon:/nomëttes/i,evening:/owes/i,night:/nuets/i}},MB={ordinalNumber:g({matchPattern:i,parsePattern:n,valueCallback:function B(C){return parseInt(C,10)}}),era:z({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),quarter:z({matchPatterns:a,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:z({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:t,defaultParseWidth:"any"}),day:z({matchPatterns:BB,defaultMatchWidth:"wide",parsePatterns:CB,defaultParseWidth:"any"}),dayPeriod:z({matchPatterns:HB,defaultMatchWidth:"wide",parsePatterns:JB,defaultParseWidth:"any"})},QB={code:"lb",formatDistance:R,formatLong:_,formatRelative:f,localize:l,match:MB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=F(F({},window.dateFns),{},{locale:F(F({},(q=window.dateFns)===null||q===void 0?void 0:q.locale),{},{lb:QB})})})();

//# debugId=53C46364D5ADD64264756E2164756E21
