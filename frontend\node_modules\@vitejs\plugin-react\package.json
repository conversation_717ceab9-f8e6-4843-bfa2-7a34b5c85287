{"name": "@vitejs/plugin-react", "version": "4.6.0", "license": "MIT", "author": "<PERSON>", "description": "The default Vite plugin for React projects", "keywords": ["vite", "vite-plugin", "react", "babel", "react-refresh", "fast refresh"], "contributors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "files": ["dist"], "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && pnpm run patch-cjs && tsx scripts/copyRefreshRuntime.ts", "patch-cjs": "tsx ../../scripts/patchCJS.ts", "prepublishOnly": "npm run build", "test-unit": "vitest run"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-react.git", "directory": "packages/plugin-react"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-react/issues"}, "homepage": "https://github.com/vitejs/vite-plugin-react/tree/main/packages/plugin-react#readme", "dependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.19", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0"}, "devDependencies": {"@vitejs/react-common": "workspace:*", "babel-plugin-react-compiler": "19.1.0-rc.2", "react": "^19.1.0", "react-dom": "^19.1.0", "rolldown": "1.0.0-beta.19", "unbuild": "^3.5.0", "vitest": "^3.2.4"}}