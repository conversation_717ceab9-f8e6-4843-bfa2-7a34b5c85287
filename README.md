# 电商库存管理系统

一个基于Web的现代化库存管理解决方案，专为电商企业设计。

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- npm 或 pnpm

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd inventory-management-system
```

2. **启动后端**
```bash
cd backend
source venv/bin/activate
pip install -r requirements.txt
python src/main.py
```

3. **启动前端**
```bash
cd frontend
pnpm install
pnpm run dev
```

4. **访问系统**
- 前端：http://localhost:5173
- 后端API：http://localhost:5000

## 功能特色

### 核心功能模块

✅ **仪表板**：库存概览和关键指标  
✅ **商品管理**：商品信息的增删改查  
✅ **仓库管理**：多仓库信息管理  
✅ **库存查询**：实时库存状态查询  
✅ **入库管理**：采购入库、退货入库、调拨入库  
✅ **出库管理**：销售出库、调拨出库、报损出库  
✅ **库存预警**：低库存自动提醒  

### 入库管理功能

- **多种入库类型**：支持采购入库、退货入库、调拨入库
- **供应商管理**：关联供应商信息
- **批量商品处理**：一次入库单可包含多个商品
- **实时库存更新**：入库后自动更新库存数量
- **入库单追踪**：完整的入库记录和状态管理

### 出库管理功能

- **多种出库类型**：支持销售出库、调拨出库、报损出库
- **客户管理**：关联客户信息
- **库存检查**：出库前自动检查库存充足性
- **批量商品处理**：一次出库单可包含多个商品
- **出库单追踪**：完整的出库记录和状态管理

### 仓库管理功能

- **多仓库支持**：管理多个仓库的基本信息
- **仓库容量管理**：记录仓库容量和位置信息
- **库存分仓管理**：每个商品可在不同仓库分别管理库存# 🛠️ 技术栈

### 前端
- React 19
- Tailwind CSS
- shadcn/ui
- React Router
- Lucide Icons

### 后端
- Flask
- SQLAlchemy
- SQLite
- Flask-CORS

## 📁 项目结构

```
inventory-management-system/
├── backend/                 # 后端代码
│   ├── src/
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # API路由
│   │   └── main.py         # 主程序
│   ├── venv/               # 虚拟环境
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # React组件
│   │   └── App.jsx         # 主应用
│   ├── package.json        # Node.js依赖
│   └── vite.config.js      # Vite配置
└── README.md
```

## 🔧 开发指南

### 后端开发

1. 激活虚拟环境：
```bash
cd backend
source venv/bin/activate
```

2. 安装新依赖后更新requirements.txt：
```bash
pip freeze > requirements.txt
```

3. 数据库迁移：
```bash
# 数据库会自动创建和初始化
python src/main.py
```

### 前端开发

1. 安装依赖：
```bash
cd frontend
pnpm install
```

2. 开发模式：
```bash
pnpm run dev
```

3. 构建生产版本：
```bash
pnpm run build
```

## 📊 数据库设计

### 核心表结构

- `products` - 商品信息
- `warehouses` - 仓库信息
- `inventories` - 库存信息
- `suppliers` - 供应商信息
- `customers` - 客户信息
- `inbound_orders` - 入库单
- `outbound_orders` - 出库单

## 🔌 API接口

### 商品管理
- `GET /api/products` - 获取商品列表
- `POST /api/products` - 创建商品

### 库存管理
- `GET /api/inventory` - 查询库存
- `GET /api/inventory/warnings` - 获取库存预警

### 入库管理
- `POST /api/inbound` - 创建入库单
- `POST /api/inbound/{id}/confirm` - 确认入库

### 出库管理
- `POST /api/outbound` - 创建出库单
- `POST /api/outbound/{id}/confirm` - 确认出库

## 🚀 部署指南

### 本地部署

1. 按照快速开始步骤安装和运行
2. 确保前后端服务都正常启动
3. 通过浏览器访问前端地址

### 生产部署

1. 构建前端：
```bash
cd frontend
pnpm run build
```

2. 配置生产环境变量
3. 使用生产级Web服务器（如Nginx + Gunicorn）

## 🐛 故障排除

### 常见问题

1. **端口冲突**：修改配置文件中的端口号
2. **依赖安装失败**：检查Python/Node.js版本
3. **数据库连接失败**：检查数据库文件权限
4. **CORS错误**：确认后端CORS配置正确

### 日志查看

- 后端日志：终端输出
- 前端日志：浏览器开发者工具Console

## 📝 更新日志

### v1.0.0 (2025-06-19)
- ✨ 初始版本发布
- ✨ 完成基础功能模块
- ✨ 实现前后端分离架构
- ✨ 添加响应式UI设计

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request


