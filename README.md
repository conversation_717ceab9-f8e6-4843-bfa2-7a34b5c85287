# 🏪 电商库存管理系统

<div align="center">

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Python](https://img.shields.io/badge/python-3.8+-blue.svg)
![React](https://img.shields.io/badge/react-18.0+-blue.svg)
![Flask](https://img.shields.io/badge/flask-2.0+-green.svg)

**一个现代化的电商库存管理系统，基于 React + Flask 架构开发**

[功能特性](#-功能特性) • [快速开始](#-快速开始) • [技术栈](#-技术栈) • [项目结构](#-项目结构) • [API文档](#-api文档)

</div>

---

## 📋 项目概述

电商库存管理系统是一个全栈Web应用，专为电商企业设计，提供完整的库存管理解决方案。系统采用前后端分离架构，具有现代化的用户界面和强大的后端API。

### 🎯 核心价值

- **📊 数据驱动决策** - 实时仪表板展示关键业务指标
- **🔄 全流程管理** - 覆盖商品、仓库、入库、出库全业务流程
- **⚠️ 智能预警** - 自动库存预警，避免缺货风险
- **🎨 用户友好** - 响应式设计，支持多设备访问
- **🔧 易于扩展** - 模块化架构，便于功能扩展

## ✨ 功能特性

### 📈 仪表板
- 📦 商品总数统计
- 🏢 仓库数量展示
- ⚠️ 低库存商品预警
- 💰 库存总价值计算
- 📊 实时数据更新

### 🛍️ 商品管理
- ➕ 新增商品信息
- 📝 编辑商品详情
- 🗑️ 删除商品（安全检查）
- 🔍 商品信息查询
- 🏷️ SKU管理

### 🏢 仓库管理
- 🏗️ 仓库信息维护
- 📍 仓库位置管理
- 📏 仓库容量设置
- 📋 仓库列表查看

### 📦 库存管理
- 🔍 实时库存查询
- 📊 库存状态监控
- 🔒 库存锁定管理
- ⚖️ 可用库存计算

### 📥 入库管理
- 📋 入库单创建
- ✅ 入库确认处理
- 📝 入库明细记录
- 🏪 多仓库支持

### 📤 出库管理
- 📋 出库单创建
- ✅ 出库确认处理
- 📝 出库明细记录
- 🔒 库存预占机制

### ⚠️ 库存预警
- 🚨 低库存自动提醒
- 📊 预警阈值设置
- 📈 库存趋势分析
- 📧 预警通知机制

## 🚀 快速开始

### 📋 环境要求

| 软件 | 版本要求 | 说明 |
|------|----------|------|
| Node.js | 16.0+ | 前端开发环境 |
| Python | 3.8+ | 后端运行环境 |
| npm/yarn | 最新版 | 包管理工具 |

### 🔧 安装步骤

#### 1️⃣ 克隆项目
```bash
git clone <repository-url>
cd 电商库存管理系统
```

#### 2️⃣ 后端环境配置
```bash
# 进入后端目录
cd backend

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

#### 3️⃣ 前端环境配置
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 构建前端
npm run build
```

### 🎬 启动系统

#### 方法一：使用启动脚本（推荐）
```bash
# 在后端目录下
cd backend
python start_backend.py
```

#### 方法二：分别启动
```bash
# 启动后端（终端1）
cd backend
python src/main.py

# 启动前端（终端2）
cd frontend/dist
python -m http.server 8080
```

### 🌐 访问系统

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost:8080 | 用户界面 |
| 后端API | http://localhost:5000/api | API接口 |
| API文档 | 查看 `backend/API文档.md` | 接口说明 |

## 🛠️ 技术栈

### 前端技术
| 技术 | 版本 | 用途 |
|------|------|------|
| React | 18.0+ | 用户界面框架 |
| Vite | 6.0+ | 构建工具 |
| Tailwind CSS | 3.0+ | CSS框架 |
| Lucide React | 最新 | 图标库 |
| React Router | 6.0+ | 路由管理 |

### 后端技术
| 技术 | 版本 | 用途 |
|------|------|------|
| Python | 3.8+ | 编程语言 |
| Flask | 2.0+ | Web框架 |
| SQLAlchemy | 1.4+ | ORM框架 |
| SQLite | 3.0+ | 数据库 |
| Flask-CORS | 最新 | 跨域支持 |

### 开发工具
- **代码编辑器**: VS Code / PyCharm
- **版本控制**: Git
- **包管理**: npm + pip
- **API测试**: Postman / curl

## 📁 项目结构

```
电商库存管理系统/
├── 📄 README.md                    # 项目说明文档
├── 📄 系统使用说明.md              # 用户使用指南
├── 📄 项目交付说明.md              # 部署说明
├── 📄 项目交付清单.md              # 交付清单
├── 📄 电商行业库存管理系统大作业报告.pdf
├── 📂 backend/                     # 后端服务
│   ├── 📄 requirements.txt         # Python依赖
│   ├── 📄 start_backend.py         # 启动脚本
│   ├── 📄 simple_backend.py        # 简化服务
│   ├── 📄 init_data.py            # 数据初始化
│   ├── 📄 API文档.md              # API文档
│   ├── 📂 venv/                   # 虚拟环境
│   └── 📂 src/                    # 源代码
│       ├── 📄 main.py             # Flask应用
│       ├── 📂 database/           # 数据库
│       │   └── 📄 app.db          # SQLite文件
│       ├── 📂 models/             # 数据模型
│       │   ├── 📄 inventory.py    # 库存模型
│       │   └── 📄 user.py         # 用户模型
│       ├── 📂 routes/             # API路由
│       │   ├── 📄 inventory.py    # 库存API
│       │   └── 📄 user.py         # 用户API
│       └── 📂 utils/              # 工具类
│           └── 📄 response.py     # 响应工具
└── 📂 frontend/                   # 前端应用
    ├── 📄 package.json            # 依赖配置
    ├── 📄 vite.config.js          # 构建配置
    ├── 📂 src/                    # 源代码
    │   ├── 📄 App.jsx             # 主组件
    │   ├── 📄 App.css             # 样式文件
    │   └── 📂 components/         # UI组件
    ├── 📂 public/                 # 静态资源
    ├── 📂 dist/                   # 构建输出
    └── 📂 node_modules/           # 依赖包
```

## 📚 API文档

### 🔗 主要API端点

| 模块 | 方法 | 端点 | 说明 |
|------|------|------|------|
| 商品 | GET | `/api/products` | 获取商品列表 |
| 商品 | POST | `/api/products` | 创建商品 |
| 商品 | DELETE | `/api/products/{id}` | 删除商品 |
| 仓库 | GET | `/api/warehouses` | 获取仓库列表 |
| 仓库 | POST | `/api/warehouses` | 创建仓库 |
| 库存 | GET | `/api/inventory` | 查询库存 |
| 预警 | GET | `/api/inventory/warnings` | 获取预警信息 |

### 📖 详细文档
完整的API文档请参考：[backend/API文档.md](backend/API文档.md)

## 🔧 配置说明

### 环境变量
```bash
# 后端配置
FLASK_ENV=development
FLASK_DEBUG=True
DATABASE_URL=sqlite:///app.db

# 前端配置
VITE_API_BASE_URL=http://localhost:5000/api
```

### 数据库配置
系统默认使用SQLite数据库，文件位置：`backend/src/database/app.db`

如需使用其他数据库，请修改 `backend/src/models/inventory.py` 中的数据库连接配置。

## 🧪 测试

### 后端测试
```bash
cd backend
python -m pytest tests/
```

### 前端测试
```bash
cd frontend
npm run test
```

### API测试
```bash
# 测试后端连接
curl http://localhost:5000/

# 测试商品API
curl http://localhost:5000/api/products
```

## 📦 部署

### 生产环境部署

#### 1. 后端部署
```bash
# 使用 Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 src.main:app
```

#### 2. 前端部署
```bash
# 构建生产版本
npm run build

# 使用 Nginx 或其他 Web 服务器托管 dist 目录
```

### Docker 部署
```dockerfile
# Dockerfile 示例
FROM python:3.9-slim
WORKDIR /app
COPY backend/ .
RUN pip install -r requirements.txt
EXPOSE 5000
CMD ["python", "src/main.py"]
```

## 🐛 故障排除

### 常见问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 端口冲突 | 5000/8080端口被占用 | 修改端口或结束占用进程 |
| 依赖安装失败 | Python/Node.js版本不兼容 | 检查并升级到要求版本 |
| 数据库连接失败 | 数据库文件权限问题 | 检查文件权限和路径 |
| CORS错误 | 跨域请求被阻止 | 确认后端CORS配置 |
| 页面空白 | 前端构建或API连接问题 | 检查浏览器控制台错误 |

### 日志查看
- **后端日志**: 终端输出和Flask日志
- **前端日志**: 浏览器开发者工具Console
- **API日志**: 网络请求状态

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📝 更新日志

### v1.0.0 (2024-12-24)
- ✨ 初始版本发布
- 🎉 完整的库存管理功能
- 📱 响应式用户界面
- 🔧 RESTful API接口
- 🗑️ 商品删除功能
- ⚠️ 库存预警系统
