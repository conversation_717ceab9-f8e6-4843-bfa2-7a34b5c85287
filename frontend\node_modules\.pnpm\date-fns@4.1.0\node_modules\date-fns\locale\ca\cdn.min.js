(()=>{var $;function C(G){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},C(G)}function A(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function Q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?A(Object(J),!0).forEach(function(X){N(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):A(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function N(G,H,J){if(H=z(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function z(G){var H=E(G,"string");return C(H)=="symbol"?H:String(H)}function E(G,H){if(C(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(C(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,XG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},S={lessThanXSeconds:{one:"menys d'un segon",eleven:"menys d'onze segons",other:"menys de {{count}} segons"},xSeconds:{one:"1 segon",other:"{{count}} segons"},halfAMinute:"mig minut",lessThanXMinutes:{one:"menys d'un minut",eleven:"menys d'onze minuts",other:"menys de {{count}} minuts"},xMinutes:{one:"1 minut",other:"{{count}} minuts"},aboutXHours:{one:"aproximadament una hora",other:"aproximadament {{count}} hores"},xHours:{one:"1 hora",other:"{{count}} hores"},xDays:{one:"1 dia",other:"{{count}} dies"},aboutXWeeks:{one:"aproximadament una setmana",other:"aproximadament {{count}} setmanes"},xWeeks:{one:"1 setmana",other:"{{count}} setmanes"},aboutXMonths:{one:"aproximadament un mes",other:"aproximadament {{count}} mesos"},xMonths:{one:"1 mes",other:"{{count}} mesos"},aboutXYears:{one:"aproximadament un any",other:"aproximadament {{count}} anys"},xYears:{one:"1 any",other:"{{count}} anys"},overXYears:{one:"m\xE9s d'un any",eleven:"m\xE9s d'onze anys",other:"m\xE9s de {{count}} anys"},almostXYears:{one:"gaireb\xE9 un any",other:"gaireb\xE9 {{count}} anys"}},D=function G(H,J,X){var Y,Z=S[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else if(J===11&&Z.eleven)Y=Z.eleven;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"en "+Y;else return"fa "+Y;return Y};function K(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE, d 'de' MMMM y",long:"d 'de' MMMM y",medium:"d MMM y",short:"dd/MM/y"},R={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} 'a les' {{time}}",long:"{{date}} 'a les' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:K({formats:M,defaultWidth:"full"}),time:K({formats:R,defaultWidth:"full"}),dateTime:K({formats:L,defaultWidth:"full"})},j={lastWeek:"'el' eeee 'passat a la' LT",yesterday:"'ahir a la' p",today:"'avui a la' p",tomorrow:"'dem\xE0 a la' p",nextWeek:"eeee 'a la' p",other:"P"},w={lastWeek:"'el' eeee 'passat a les' p",yesterday:"'ahir a les' p",today:"'avui a les' p",tomorrow:"'dem\xE0 a les' p",nextWeek:"eeee 'a les' p",other:"P"},_=function G(H,J,X,Y){if(J.getHours()!==1)return w[H];return j[H]};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,T=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[T]||G.formattingValues[Z]}else{var U=G.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[q]||G.values[U]}var B=G.argumentCallback?G.argumentCallback(H):H;return Y[B]}}var f={narrow:["aC","dC"],abbreviated:["a. de C.","d. de C."],wide:["abans de Crist","despr\xE9s de Crist"]},F={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1r trimestre","2n trimestre","3r trimestre","4t trimestre"]},v={narrow:["GN","FB","M\xC7","AB","MG","JN","JL","AG","ST","OC","NV","DS"],abbreviated:["gen.","febr.","mar\xE7","abr.","maig","juny","jul.","ag.","set.","oct.","nov.","des."],wide:["gener","febrer","mar\xE7","abril","maig","juny","juliol","agost","setembre","octubre","novembre","desembre"]},P={narrow:["dg.","dl.","dt.","dm.","dj.","dv.","ds."],short:["dg.","dl.","dt.","dm.","dj.","dv.","ds."],abbreviated:["dg.","dl.","dt.","dm.","dj.","dv.","ds."],wide:["diumenge","dilluns","dimarts","dimecres","dijous","divendres","dissabte"]},k={narrow:{am:"am",pm:"pm",midnight:"mitjanit",noon:"migdia",morning:"mat\xED",afternoon:"tarda",evening:"vespre",night:"nit"},abbreviated:{am:"a.m.",pm:"p.m.",midnight:"mitjanit",noon:"migdia",morning:"mat\xED",afternoon:"tarda",evening:"vespre",night:"nit"},wide:{am:"ante meridiem",pm:"post meridiem",midnight:"mitjanit",noon:"migdia",morning:"mat\xED",afternoon:"tarda",evening:"vespre",night:"nit"}},h={narrow:{am:"am",pm:"pm",midnight:"de la mitjanit",noon:"del migdia",morning:"del mat\xED",afternoon:"de la tarda",evening:"del vespre",night:"de la nit"},abbreviated:{am:"AM",pm:"PM",midnight:"de la mitjanit",noon:"del migdia",morning:"del mat\xED",afternoon:"de la tarda",evening:"del vespre",night:"de la nit"},wide:{am:"ante meridiem",pm:"post meridiem",midnight:"de la mitjanit",noon:"del migdia",morning:"del mat\xED",afternoon:"de la tarda",evening:"del vespre",night:"de la nit"}},b=function G(H,J){var X=Number(H),Y=X%100;if(Y>20||Y<10)switch(Y%10){case 1:return X+"r";case 2:return X+"n";case 3:return X+"r";case 4:return X+"t"}return X+"\xE8"},y={ordinalNumber:b,era:I({values:f,defaultWidth:"wide"}),quarter:I({values:F,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:P,defaultWidth:"wide"}),dayPeriod:I({values:k,defaultWidth:"wide",formattingValues:h,defaultFormattingWidth:"wide"})};function O(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var T=Z[0],U=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(U)?c(U,function(x){return x.test(T)}):m(U,function(x){return x.test(T)}),B;B=G.valueCallback?G.valueCallback(q):q,B=J.valueCallback?J.valueCallback(B):B;var JG=H.slice(T.length);return{value:B,rest:JG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function p(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var T=G.valueCallback?G.valueCallback(Z[0]):Z[0];T=J.valueCallback?J.valueCallback(T):T;var U=H.slice(Y.length);return{value:T,rest:U}}}var g=/^(\d+)(è|r|n|r|t)?/i,d=/\d+/i,u={narrow:/^(aC|dC)/i,abbreviated:/^(a. de C.|d. de C.)/i,wide:/^(abans de Crist|despr[eé]s de Crist)/i},l={narrow:[/^aC/i,/^dC/i],abbreviated:[/^(a. de C.)/i,/^(d. de C.)/i],wide:[/^(abans de Crist)/i,/^(despr[eé]s de Crist)/i]},i={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](è|r|n|r|t)? trimestre/i},n={any:[/1/i,/2/i,/3/i,/4/i]},s={narrow:/^(GN|FB|MÇ|AB|MG|JN|JL|AG|ST|OC|NV|DS)/i,abbreviated:/^(gen.|febr.|març|abr.|maig|juny|jul.|ag.|set.|oct.|nov.|des.)/i,wide:/^(gener|febrer|març|abril|maig|juny|juliol|agost|setembre|octubre|novembre|desembre)/i},o={narrow:[/^GN/i,/^FB/i,/^MÇ/i,/^AB/i,/^MG/i,/^JN/i,/^JL/i,/^AG/i,/^ST/i,/^OC/i,/^NV/i,/^DS/i],abbreviated:[/^gen./i,/^febr./i,/^març/i,/^abr./i,/^maig/i,/^juny/i,/^jul./i,/^ag./i,/^set./i,/^oct./i,/^nov./i,/^des./i],wide:[/^gener/i,/^febrer/i,/^març/i,/^abril/i,/^maig/i,/^juny/i,/^juliol/i,/^agost/i,/^setembre/i,/^octubre/i,/^novembre/i,/^desembre/i]},r={narrow:/^(dg\.|dl\.|dt\.|dm\.|dj\.|dv\.|ds\.)/i,short:/^(dg\.|dl\.|dt\.|dm\.|dj\.|dv\.|ds\.)/i,abbreviated:/^(dg\.|dl\.|dt\.|dm\.|dj\.|dv\.|ds\.)/i,wide:/^(diumenge|dilluns|dimarts|dimecres|dijous|divendres|dissabte)/i},a={narrow:[/^dg./i,/^dl./i,/^dt./i,/^dm./i,/^dj./i,/^dv./i,/^ds./i],abbreviated:[/^dg./i,/^dl./i,/^dt./i,/^dm./i,/^dj./i,/^dv./i,/^ds./i],wide:[/^diumenge/i,/^dilluns/i,/^dimarts/i,/^dimecres/i,/^dijous/i,/^divendres/i,/^disssabte/i]},e={narrow:/^(a|p|mn|md|(del|de la) (matí|tarda|vespre|nit))/i,abbreviated:/^([ap]\.?\s?m\.?|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i,wide:/^(ante meridiem|post meridiem|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i},t={any:{am:/^a/i,pm:/^p/i,midnight:/^mitjanit/i,noon:/^migdia/i,morning:/matí/i,afternoon:/tarda/i,evening:/vespre/i,night:/nit/i}},GG={ordinalNumber:p({matchPattern:g,parsePattern:d,valueCallback:function G(H){return parseInt(H,10)}}),era:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"wide"}),quarter:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:O({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"wide"}),day:O({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"wide"}),dayPeriod:O({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:t,defaultParseWidth:"any"})},HG={code:"ca",formatDistance:D,formatLong:V,formatRelative:_,localize:y,match:GG,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{ca:HG})})})();

//# debugId=F5D76590C5B18A4364756E2164756E21
