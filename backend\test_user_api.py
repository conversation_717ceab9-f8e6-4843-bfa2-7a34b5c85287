#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户API
"""

import os
import sys
import requests
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

API_BASE_URL = 'http://localhost:5000/api'

def test_user_api():
    """测试用户API"""
    print("🧪 开始测试用户API...")
    
    try:
        # 1. 测试获取用户列表
        print("\n1️⃣ 测试获取用户列表...")
        response = requests.get(f"{API_BASE_URL}/users")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"错误: {response.text}")
        
        # 2. 测试创建用户
        print("\n2️⃣ 测试创建用户...")
        user_data = {
            "username": "api_test_user",
            "email": "<EMAIL>"
        }
        response = requests.post(f"{API_BASE_URL}/users", 
                               headers={'Content-Type': 'application/json'},
                               data=json.dumps(user_data))
        print(f"状态码: {response.status_code}")
        if response.status_code in [200, 201]:
            result = response.json()
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"错误: {response.text}")
        
        # 3. 测试重复用户名
        print("\n3️⃣ 测试重复用户名...")
        response = requests.post(f"{API_BASE_URL}/users", 
                               headers={'Content-Type': 'application/json'},
                               data=json.dumps(user_data))
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 4. 测试缺少必填字段
        print("\n4️⃣ 测试缺少必填字段...")
        invalid_data = {"username": "incomplete_user"}  # 缺少email
        response = requests.post(f"{API_BASE_URL}/users", 
                               headers={'Content-Type': 'application/json'},
                               data=json.dumps(invalid_data))
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        print("\n✅ 用户API测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保后端正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_user_api()
