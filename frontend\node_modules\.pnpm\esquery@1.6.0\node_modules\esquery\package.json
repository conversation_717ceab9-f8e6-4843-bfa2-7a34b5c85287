{"name": "esquery", "version": "1.6.0", "author": "<PERSON> <<EMAIL>>", "contributors": [], "description": "A query library for ECMAScript AST using a CSS selector like query language.", "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "files": ["dist/*.js", "dist/*.map", "parser.js", "license.txt", "README.md"], "nyc": {"branches": 100, "lines": 100, "functions": 100, "statements": 100, "reporter": ["html", "text"], "exclude": ["parser.js", "dist", "tests"]}, "scripts": {"prepublishOnly": "npm run build && npm test", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "test": "nyc npm run mocha && npm run lint", "test:ci": "npm run mocha", "lint": "eslint ."}, "repository": {"type": "git", "url": "https://github.com/estools/esquery.git"}, "bugs": "https://github.com/estools/esquery/issues", "homepage": "https://github.com/estools/esquery/", "keywords": ["ast", "ecmascript", "javascript", "query"], "devDependencies": {"@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/register": "^7.9.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-json": "^4.0.2", "@rollup/plugin-node-resolve": "^7.1.3", "babel-plugin-transform-es2017-object-entries": "0.0.5", "chai": "4.2.0", "eslint": "^6.8.0", "esprima": "~4.0.1", "mocha": "7.1.1", "nyc": "^15.0.1", "pegjs": "~0.10.0", "rollup": "^1.32.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0"}, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10"}, "dependencies": {"estraverse": "^5.1.0"}}