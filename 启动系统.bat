@echo off
chcp 65001 >nul
echo 🚀 启动电商库存管理系统
echo ================================

echo.
echo 📋 检查环境...
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python
    pause
    exit /b 1
)

where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo ✅ 环境检查完成

echo.
echo 🔧 清理旧进程...
taskkill /f /im python.exe >nul 2>&1
echo ✅ 进程清理完成

echo.
echo 📦 构建前端...
cd frontend
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)
echo ✅ 前端构建完成

echo.
echo 🔧 启动后端服务...
cd ..\backend
start "后端服务" cmd /k "echo 🔧 后端API服务 && python simple_backend.py"

echo.
echo 📱 等待后端启动...
timeout /t 3 /nobreak >nul

echo.
echo 🌐 启动前端服务...
cd ..\frontend\dist
start "前端服务" cmd /k "echo 📱 前端Web服务 && python -m http.server 8080"

echo.
echo 📱 等待前端启动...
timeout /t 3 /nobreak >nul

echo.
echo 🌐 打开浏览器...
start http://localhost:8080

echo.
echo ✅ 系统启动完成！
echo.
echo 📋 服务地址：
echo    前端应用: http://localhost:8080
echo    后端API:  http://localhost:5000
echo    诊断页面: http://localhost:8080/debug.html
echo.
echo 💡 提示：
echo    - 如果页面空白，请访问诊断页面检查问题
echo    - 关闭此窗口不会停止服务
echo    - 要停止服务，请关闭对应的命令行窗口
echo.
pause
