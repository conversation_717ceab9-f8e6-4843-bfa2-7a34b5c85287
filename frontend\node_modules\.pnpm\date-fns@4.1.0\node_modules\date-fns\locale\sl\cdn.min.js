(()=>{var A;function I(G){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},I(G)}function x(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Z){return Object.getOwnPropertyDescriptor(G,Z).enumerable})),J.push.apply(J,X)}return J}function T(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?x(Object(J),!0).forEach(function(X){E(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):x(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function E(G,H,J){if(H=N(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function N(G){var H=z(G,"string");return I(H)=="symbol"?H:String(H)}function z(G,H){if(I(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(I(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,YG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Z(Y){return J[X]=function(){return Y}}})};function D(G){return G.one!==void 0}function S(G){switch(G%100){case 1:return"one";case 2:return"two";case 3:case 4:return"few";default:return"other"}}var M={lessThanXSeconds:{present:{one:"manj kot {{count}} sekunda",two:"manj kot {{count}} sekundi",few:"manj kot {{count}} sekunde",other:"manj kot {{count}} sekund"},past:{one:"manj kot {{count}} sekundo",two:"manj kot {{count}} sekundama",few:"manj kot {{count}} sekundami",other:"manj kot {{count}} sekundami"},future:{one:"manj kot {{count}} sekundo",two:"manj kot {{count}} sekundi",few:"manj kot {{count}} sekunde",other:"manj kot {{count}} sekund"}},xSeconds:{present:{one:"{{count}} sekunda",two:"{{count}} sekundi",few:"{{count}} sekunde",other:"{{count}} sekund"},past:{one:"{{count}} sekundo",two:"{{count}} sekundama",few:"{{count}} sekundami",other:"{{count}} sekundami"},future:{one:"{{count}} sekundo",two:"{{count}} sekundi",few:"{{count}} sekunde",other:"{{count}} sekund"}},halfAMinute:"pol minute",lessThanXMinutes:{present:{one:"manj kot {{count}} minuta",two:"manj kot {{count}} minuti",few:"manj kot {{count}} minute",other:"manj kot {{count}} minut"},past:{one:"manj kot {{count}} minuto",two:"manj kot {{count}} minutama",few:"manj kot {{count}} minutami",other:"manj kot {{count}} minutami"},future:{one:"manj kot {{count}} minuto",two:"manj kot {{count}} minuti",few:"manj kot {{count}} minute",other:"manj kot {{count}} minut"}},xMinutes:{present:{one:"{{count}} minuta",two:"{{count}} minuti",few:"{{count}} minute",other:"{{count}} minut"},past:{one:"{{count}} minuto",two:"{{count}} minutama",few:"{{count}} minutami",other:"{{count}} minutami"},future:{one:"{{count}} minuto",two:"{{count}} minuti",few:"{{count}} minute",other:"{{count}} minut"}},aboutXHours:{present:{one:"pribli\u017Eno {{count}} ura",two:"pribli\u017Eno {{count}} uri",few:"pribli\u017Eno {{count}} ure",other:"pribli\u017Eno {{count}} ur"},past:{one:"pribli\u017Eno {{count}} uro",two:"pribli\u017Eno {{count}} urama",few:"pribli\u017Eno {{count}} urami",other:"pribli\u017Eno {{count}} urami"},future:{one:"pribli\u017Eno {{count}} uro",two:"pribli\u017Eno {{count}} uri",few:"pribli\u017Eno {{count}} ure",other:"pribli\u017Eno {{count}} ur"}},xHours:{present:{one:"{{count}} ura",two:"{{count}} uri",few:"{{count}} ure",other:"{{count}} ur"},past:{one:"{{count}} uro",two:"{{count}} urama",few:"{{count}} urami",other:"{{count}} urami"},future:{one:"{{count}} uro",two:"{{count}} uri",few:"{{count}} ure",other:"{{count}} ur"}},xDays:{present:{one:"{{count}} dan",two:"{{count}} dni",few:"{{count}} dni",other:"{{count}} dni"},past:{one:"{{count}} dnem",two:"{{count}} dnevoma",few:"{{count}} dnevi",other:"{{count}} dnevi"},future:{one:"{{count}} dan",two:"{{count}} dni",few:"{{count}} dni",other:"{{count}} dni"}},aboutXWeeks:{one:"pribli\u017Eno {{count}} teden",two:"pribli\u017Eno {{count}} tedna",few:"pribli\u017Eno {{count}} tedne",other:"pribli\u017Eno {{count}} tednov"},xWeeks:{one:"{{count}} teden",two:"{{count}} tedna",few:"{{count}} tedne",other:"{{count}} tednov"},aboutXMonths:{present:{one:"pribli\u017Eno {{count}} mesec",two:"pribli\u017Eno {{count}} meseca",few:"pribli\u017Eno {{count}} mesece",other:"pribli\u017Eno {{count}} mesecev"},past:{one:"pribli\u017Eno {{count}} mesecem",two:"pribli\u017Eno {{count}} mesecema",few:"pribli\u017Eno {{count}} meseci",other:"pribli\u017Eno {{count}} meseci"},future:{one:"pribli\u017Eno {{count}} mesec",two:"pribli\u017Eno {{count}} meseca",few:"pribli\u017Eno {{count}} mesece",other:"pribli\u017Eno {{count}} mesecev"}},xMonths:{present:{one:"{{count}} mesec",two:"{{count}} meseca",few:"{{count}} meseci",other:"{{count}} mesecev"},past:{one:"{{count}} mesecem",two:"{{count}} mesecema",few:"{{count}} meseci",other:"{{count}} meseci"},future:{one:"{{count}} mesec",two:"{{count}} meseca",few:"{{count}} mesece",other:"{{count}} mesecev"}},aboutXYears:{present:{one:"pribli\u017Eno {{count}} leto",two:"pribli\u017Eno {{count}} leti",few:"pribli\u017Eno {{count}} leta",other:"pribli\u017Eno {{count}} let"},past:{one:"pribli\u017Eno {{count}} letom",two:"pribli\u017Eno {{count}} letoma",few:"pribli\u017Eno {{count}} leti",other:"pribli\u017Eno {{count}} leti"},future:{one:"pribli\u017Eno {{count}} leto",two:"pribli\u017Eno {{count}} leti",few:"pribli\u017Eno {{count}} leta",other:"pribli\u017Eno {{count}} let"}},xYears:{present:{one:"{{count}} leto",two:"{{count}} leti",few:"{{count}} leta",other:"{{count}} let"},past:{one:"{{count}} letom",two:"{{count}} letoma",few:"{{count}} leti",other:"{{count}} leti"},future:{one:"{{count}} leto",two:"{{count}} leti",few:"{{count}} leta",other:"{{count}} let"}},overXYears:{present:{one:"ve\u010D kot {{count}} leto",two:"ve\u010D kot {{count}} leti",few:"ve\u010D kot {{count}} leta",other:"ve\u010D kot {{count}} let"},past:{one:"ve\u010D kot {{count}} letom",two:"ve\u010D kot {{count}} letoma",few:"ve\u010D kot {{count}} leti",other:"ve\u010D kot {{count}} leti"},future:{one:"ve\u010D kot {{count}} leto",two:"ve\u010D kot {{count}} leti",few:"ve\u010D kot {{count}} leta",other:"ve\u010D kot {{count}} let"}},almostXYears:{present:{one:"skoraj {{count}} leto",two:"skoraj {{count}} leti",few:"skoraj {{count}} leta",other:"skoraj {{count}} let"},past:{one:"skoraj {{count}} letom",two:"skoraj {{count}} letoma",few:"skoraj {{count}} leti",other:"skoraj {{count}} leti"},future:{one:"skoraj {{count}} leto",two:"skoraj {{count}} leti",few:"skoraj {{count}} leta",other:"skoraj {{count}} let"}}},R=function G(H,J,X){var Z="",Y="present";if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)Y="future",Z="\u010Dez ";else Y="past",Z="pred ";var C=M[H];if(typeof C==="string")Z+=C;else{var U=S(J);if(D(C))Z+=C[U].replace("{{count}}",String(J));else Z+=C[Y][U].replace("{{count}}",String(J))}return Z};function $(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var L={full:"EEEE, dd. MMMM y",long:"dd. MMMM y",medium:"d. MMM y",short:"d. MM. yy"},V={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},j={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},w={date:$({formats:L,defaultWidth:"full"}),time:$({formats:V,defaultWidth:"full"}),dateTime:$({formats:j,defaultWidth:"full"})},_={lastWeek:function G(H){var J=H.getDay();switch(J){case 0:return"'prej\u0161njo nedeljo ob' p";case 3:return"'prej\u0161njo sredo ob' p";case 6:return"'prej\u0161njo soboto ob' p";default:return"'prej\u0161nji' EEEE 'ob' p"}},yesterday:"'v\u010Deraj ob' p",today:"'danes ob' p",tomorrow:"'jutri ob' p",nextWeek:function G(H){var J=H.getDay();switch(J){case 0:return"'naslednjo nedeljo ob' p";case 3:return"'naslednjo sredo ob' p";case 6:return"'naslednjo soboto ob' p";default:return"'naslednji' EEEE 'ob' p"}},other:"P"},F=function G(H,J,X,Z){var Y=_[H];if(typeof Y==="function")return Y(J);return Y};function O(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Z;if(X==="formatting"&&G.formattingValues){var Y=G.defaultFormattingWidth||G.defaultWidth,C=J!==null&&J!==void 0&&J.width?String(J.width):Y;Z=G.formattingValues[C]||G.formattingValues[Y]}else{var U=G.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Z=G.values[q]||G.values[U]}var B=G.argumentCallback?G.argumentCallback(H):H;return Z[B]}}var P={narrow:["pr. n. \u0161t.","po n. \u0161t."],abbreviated:["pr. n. \u0161t.","po n. \u0161t."],wide:["pred na\u0161im \u0161tetjem","po na\u0161em \u0161tetju"]},v={narrow:["1","2","3","4"],abbreviated:["1. \u010Det.","2. \u010Det.","3. \u010Det.","4. \u010Det."],wide:["1. \u010Detrtletje","2. \u010Detrtletje","3. \u010Detrtletje","4. \u010Detrtletje"]},f={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan.","feb.","mar.","apr.","maj","jun.","jul.","avg.","sep.","okt.","nov.","dec."],wide:["januar","februar","marec","april","maj","junij","julij","avgust","september","oktober","november","december"]},k={narrow:["n","p","t","s","\u010D","p","s"],short:["ned.","pon.","tor.","sre.","\u010Det.","pet.","sob."],abbreviated:["ned.","pon.","tor.","sre.","\u010Det.","pet.","sob."],wide:["nedelja","ponedeljek","torek","sreda","\u010Detrtek","petek","sobota"]},b={narrow:{am:"d",pm:"p",midnight:"24.00",noon:"12.00",morning:"j",afternoon:"p",evening:"v",night:"n"},abbreviated:{am:"dop.",pm:"pop.",midnight:"poln.",noon:"pold.",morning:"jut.",afternoon:"pop.",evening:"ve\u010D.",night:"no\u010D"},wide:{am:"dop.",pm:"pop.",midnight:"polno\u010D",noon:"poldne",morning:"jutro",afternoon:"popoldne",evening:"ve\u010Der",night:"no\u010D"}},h={narrow:{am:"d",pm:"p",midnight:"24.00",noon:"12.00",morning:"zj",afternoon:"p",evening:"zv",night:"po"},abbreviated:{am:"dop.",pm:"pop.",midnight:"opoln.",noon:"opold.",morning:"zjut.",afternoon:"pop.",evening:"zve\u010D.",night:"pono\u010Di"},wide:{am:"dop.",pm:"pop.",midnight:"opolno\u010Di",noon:"opoldne",morning:"zjutraj",afternoon:"popoldan",evening:"zve\u010Der",night:"pono\u010Di"}},m=function G(H,J){var X=Number(H);return X+"."},y={ordinalNumber:m,era:O({values:P,defaultWidth:"wide"}),quarter:O({values:v,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:O({values:f,defaultWidth:"wide"}),day:O({values:k,defaultWidth:"wide"}),dayPeriod:O({values:b,defaultWidth:"wide",formattingValues:h,defaultFormattingWidth:"wide"})};function Q(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Z=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Y=H.match(Z);if(!Y)return null;var C=Y[0],U=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(U)?p(U,function(K){return K.test(C)}):c(U,function(K){return K.test(C)}),B;B=G.valueCallback?G.valueCallback(q):q,B=J.valueCallback?J.valueCallback(B):B;var XG=H.slice(C.length);return{value:B,rest:XG}}}function c(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function p(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function g(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Z=X[0],Y=H.match(G.parsePattern);if(!Y)return null;var C=G.valueCallback?G.valueCallback(Y[0]):Y[0];C=J.valueCallback?J.valueCallback(C):C;var U=H.slice(Z.length);return{value:C,rest:U}}}var d=/^(\d+)\./i,u=/\d+/i,l={abbreviated:/^(pr\. n\. št\.|po n\. št\.)/i,wide:/^(pred Kristusom|pred na[sš]im [sš]tetjem|po Kristusu|po na[sš]em [sš]tetju|na[sš]ega [sš]tetja)/i},i={any:[/^pr/i,/^(po|na[sš]em)/i]},n={narrow:/^[1234]/i,abbreviated:/^[1234]\.\s?[čc]et\.?/i,wide:/^[1234]\. [čc]etrtletje/i},s={any:[/1/i,/2/i,/3/i,/4/i]},o={narrow:/^[jfmasond]/i,abbreviated:/^(jan\.|feb\.|mar\.|apr\.|maj|jun\.|jul\.|avg\.|sep\.|okt\.|nov\.|dec\.)/i,wide:/^(januar|februar|marec|april|maj|junij|julij|avgust|september|oktober|november|december)/i},r={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],abbreviated:[/^ja/i,/^fe/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^av/i,/^s/i,/^o/i,/^n/i,/^d/i],wide:[/^ja/i,/^fe/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^av/i,/^s/i,/^o/i,/^n/i,/^d/i]},a={narrow:/^[nptsčc]/i,short:/^(ned\.|pon\.|tor\.|sre\.|[cč]et\.|pet\.|sob\.)/i,abbreviated:/^(ned\.|pon\.|tor\.|sre\.|[cč]et\.|pet\.|sob\.)/i,wide:/^(nedelja|ponedeljek|torek|sreda|[cč]etrtek|petek|sobota)/i},e={narrow:[/^n/i,/^p/i,/^t/i,/^s/i,/^[cč]/i,/^p/i,/^s/i],any:[/^n/i,/^po/i,/^t/i,/^sr/i,/^[cč]/i,/^pe/i,/^so/i]},t={narrow:/^(d|po?|z?v|n|z?j|24\.00|12\.00)/i,any:/^(dop\.|pop\.|o?poln(\.|o[cč]i?)|o?pold(\.|ne)|z?ve[cč](\.|er)|(po)?no[cč]i?|popold(ne|an)|jut(\.|ro)|zjut(\.|raj))/i},GG={narrow:{am:/^d/i,pm:/^p/i,midnight:/^24/i,noon:/^12/i,morning:/^(z?j)/i,afternoon:/^p/i,evening:/^(z?v)/i,night:/^(n|po)/i},any:{am:/^dop\./i,pm:/^pop\./i,midnight:/^o?poln/i,noon:/^o?pold/i,morning:/j/i,afternoon:/^pop\./i,evening:/^z?ve/i,night:/(po)?no/i}},HG={ordinalNumber:g({matchPattern:d,parsePattern:u,valueCallback:function G(H){return parseInt(H,10)}}),era:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),quarter:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"wide"}),day:Q({matchPatterns:a,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:t,defaultMatchWidth:"any",parsePatterns:GG,defaultParseWidth:"any"})},JG={code:"sl",formatDistance:R,formatLong:w,formatRelative:F,localize:y,match:HG,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=T(T({},window.dateFns),{},{locale:T(T({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{sl:JG})})})();

//# debugId=102152910A5BC61E64756E2164756E21
