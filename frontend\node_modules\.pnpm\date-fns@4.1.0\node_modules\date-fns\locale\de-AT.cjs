"use strict";
exports.deAT = void 0;
var _index = require("./de/_lib/formatDistance.cjs");
var _index2 = require("./de/_lib/formatLong.cjs");
var _index3 = require("./de/_lib/formatRelative.cjs");
var _index4 = require("./de/_lib/match.cjs");

var _index5 = require("./de-AT/_lib/localize.cjs"); // difference to 'de' locale

/**
 * @category Locales
 * @summary German locale (Austria).
 * @language German
 * @iso-639-2 deu
 * <AUTHOR> [@cstenglein](https://github.com/cstenglein)
 */
const deAT = (exports.deAT = {
  code: "de-AT",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index5.localize,
  match: _index4.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
