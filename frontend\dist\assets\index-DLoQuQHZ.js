(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))c(d);new MutationObserver(d=>{for(const m of d)if(m.type==="childList")for(const g of m.addedNodes)g.tagName==="LINK"&&g.rel==="modulepreload"&&c(g)}).observe(document,{childList:!0,subtree:!0});function f(d){const m={};return d.integrity&&(m.integrity=d.integrity),d.referrerPolicy&&(m.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?m.credentials="include":d.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function c(d){if(d.ep)return;d.ep=!0;const m=f(d);fetch(d.href,m)}})();var Jr={exports:{}},au={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mh;function Xv(){if(mh)return au;mh=1;var i=Symbol.for("react.transitional.element"),o=Symbol.for("react.fragment");function f(c,d,m){var g=null;if(m!==void 0&&(g=""+m),d.key!==void 0&&(g=""+d.key),"key"in d){m={};for(var A in d)A!=="key"&&(m[A]=d[A])}else m=d;return d=m.ref,{$$typeof:i,type:c,key:g,ref:d!==void 0?d:null,props:m}}return au.Fragment=o,au.jsx=f,au.jsxs=f,au}var yh;function kv(){return yh||(yh=1,Jr.exports=Xv()),Jr.exports}var s=kv(),$r={exports:{}},re={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vh;function Vv(){if(vh)return re;vh=1;var i=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),m=Symbol.for("react.consumer"),g=Symbol.for("react.context"),A=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),j=Symbol.for("react.lazy"),U=Symbol.iterator;function O(b){return b===null||typeof b!="object"?null:(b=U&&b[U]||b["@@iterator"],typeof b=="function"?b:null)}var X={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},D=Object.assign,G={};function Q(b,q,K){this.props=b,this.context=q,this.refs=G,this.updater=K||X}Q.prototype.isReactComponent={},Q.prototype.setState=function(b,q){if(typeof b!="object"&&typeof b!="function"&&b!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,b,q,"setState")},Q.prototype.forceUpdate=function(b){this.updater.enqueueForceUpdate(this,b,"forceUpdate")};function Y(){}Y.prototype=Q.prototype;function Z(b,q,K){this.props=b,this.context=q,this.refs=G,this.updater=K||X}var I=Z.prototype=new Y;I.constructor=Z,D(I,Q.prototype),I.isPureReactComponent=!0;var ce=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},V=Object.prototype.hasOwnProperty;function ye(b,q,K,L,$,de){return K=de.ref,{$$typeof:i,type:b,key:q,ref:K!==void 0?K:null,props:de}}function J(b,q){return ye(b.type,q,void 0,void 0,void 0,b.props)}function ve(b){return typeof b=="object"&&b!==null&&b.$$typeof===i}function Qe(b){var q={"=":"=0",":":"=2"};return"$"+b.replace(/[=:]/g,function(K){return q[K]})}var it=/\/+/g;function qe(b,q){return typeof b=="object"&&b!==null&&b.key!=null?Qe(""+b.key):q.toString(36)}function Gt(){}function qt(b){switch(b.status){case"fulfilled":return b.value;case"rejected":throw b.reason;default:switch(typeof b.status=="string"?b.then(Gt,Gt):(b.status="pending",b.then(function(q){b.status==="pending"&&(b.status="fulfilled",b.value=q)},function(q){b.status==="pending"&&(b.status="rejected",b.reason=q)})),b.status){case"fulfilled":return b.value;case"rejected":throw b.reason}}throw b}function Ne(b,q,K,L,$){var de=typeof b;(de==="undefined"||de==="boolean")&&(b=null);var ie=!1;if(b===null)ie=!0;else switch(de){case"bigint":case"string":case"number":ie=!0;break;case"object":switch(b.$$typeof){case i:case o:ie=!0;break;case j:return ie=b._init,Ne(ie(b._payload),q,K,L,$)}}if(ie)return $=$(b),ie=L===""?"."+qe(b,0):L,ce($)?(K="",ie!=null&&(K=ie.replace(it,"$&/")+"/"),Ne($,q,K,"",function(gt){return gt})):$!=null&&(ve($)&&($=J($,K+($.key==null||b&&b.key===$.key?"":(""+$.key).replace(it,"$&/")+"/")+ie)),q.push($)),1;ie=0;var ge=L===""?".":L+":";if(ce(b))for(var Re=0;Re<b.length;Re++)L=b[Re],de=ge+qe(L,Re),ie+=Ne(L,q,K,de,$);else if(Re=O(b),typeof Re=="function")for(b=Re.call(b),Re=0;!(L=b.next()).done;)L=L.value,de=ge+qe(L,Re++),ie+=Ne(L,q,K,de,$);else if(de==="object"){if(typeof b.then=="function")return Ne(qt(b),q,K,L,$);throw q=String(b),Error("Objects are not valid as a React child (found: "+(q==="[object Object]"?"object with keys {"+Object.keys(b).join(", ")+"}":q)+"). If you meant to render a collection of children, use an array instead.")}return ie}function N(b,q,K){if(b==null)return b;var L=[],$=0;return Ne(b,L,"","",function(de){return q.call(K,de,$++)}),L}function k(b){if(b._status===-1){var q=b._result;q=q(),q.then(function(K){(b._status===0||b._status===-1)&&(b._status=1,b._result=K)},function(K){(b._status===0||b._status===-1)&&(b._status=2,b._result=K)}),b._status===-1&&(b._status=0,b._result=q)}if(b._status===1)return b._result.default;throw b._result}var B=typeof reportError=="function"?reportError:function(b){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof b=="object"&&b!==null&&typeof b.message=="string"?String(b.message):String(b),error:b});if(!window.dispatchEvent(q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",b);return}console.error(b)};function Se(){}return re.Children={map:N,forEach:function(b,q,K){N(b,function(){q.apply(this,arguments)},K)},count:function(b){var q=0;return N(b,function(){q++}),q},toArray:function(b){return N(b,function(q){return q})||[]},only:function(b){if(!ve(b))throw Error("React.Children.only expected to receive a single React element child.");return b}},re.Component=Q,re.Fragment=f,re.Profiler=d,re.PureComponent=Z,re.StrictMode=c,re.Suspense=v,re.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,re.__COMPILER_RUNTIME={__proto__:null,c:function(b){return S.H.useMemoCache(b)}},re.cache=function(b){return function(){return b.apply(null,arguments)}},re.cloneElement=function(b,q,K){if(b==null)throw Error("The argument must be a React element, but you passed "+b+".");var L=D({},b.props),$=b.key,de=void 0;if(q!=null)for(ie in q.ref!==void 0&&(de=void 0),q.key!==void 0&&($=""+q.key),q)!V.call(q,ie)||ie==="key"||ie==="__self"||ie==="__source"||ie==="ref"&&q.ref===void 0||(L[ie]=q[ie]);var ie=arguments.length-2;if(ie===1)L.children=K;else if(1<ie){for(var ge=Array(ie),Re=0;Re<ie;Re++)ge[Re]=arguments[Re+2];L.children=ge}return ye(b.type,$,void 0,void 0,de,L)},re.createContext=function(b){return b={$$typeof:g,_currentValue:b,_currentValue2:b,_threadCount:0,Provider:null,Consumer:null},b.Provider=b,b.Consumer={$$typeof:m,_context:b},b},re.createElement=function(b,q,K){var L,$={},de=null;if(q!=null)for(L in q.key!==void 0&&(de=""+q.key),q)V.call(q,L)&&L!=="key"&&L!=="__self"&&L!=="__source"&&($[L]=q[L]);var ie=arguments.length-2;if(ie===1)$.children=K;else if(1<ie){for(var ge=Array(ie),Re=0;Re<ie;Re++)ge[Re]=arguments[Re+2];$.children=ge}if(b&&b.defaultProps)for(L in ie=b.defaultProps,ie)$[L]===void 0&&($[L]=ie[L]);return ye(b,de,void 0,void 0,null,$)},re.createRef=function(){return{current:null}},re.forwardRef=function(b){return{$$typeof:A,render:b}},re.isValidElement=ve,re.lazy=function(b){return{$$typeof:j,_payload:{_status:-1,_result:b},_init:k}},re.memo=function(b,q){return{$$typeof:y,type:b,compare:q===void 0?null:q}},re.startTransition=function(b){var q=S.T,K={};S.T=K;try{var L=b(),$=S.S;$!==null&&$(K,L),typeof L=="object"&&L!==null&&typeof L.then=="function"&&L.then(Se,B)}catch(de){B(de)}finally{S.T=q}},re.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},re.use=function(b){return S.H.use(b)},re.useActionState=function(b,q,K){return S.H.useActionState(b,q,K)},re.useCallback=function(b,q){return S.H.useCallback(b,q)},re.useContext=function(b){return S.H.useContext(b)},re.useDebugValue=function(){},re.useDeferredValue=function(b,q){return S.H.useDeferredValue(b,q)},re.useEffect=function(b,q,K){var L=S.H;if(typeof K=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return L.useEffect(b,q)},re.useId=function(){return S.H.useId()},re.useImperativeHandle=function(b,q,K){return S.H.useImperativeHandle(b,q,K)},re.useInsertionEffect=function(b,q){return S.H.useInsertionEffect(b,q)},re.useLayoutEffect=function(b,q){return S.H.useLayoutEffect(b,q)},re.useMemo=function(b,q){return S.H.useMemo(b,q)},re.useOptimistic=function(b,q){return S.H.useOptimistic(b,q)},re.useReducer=function(b,q,K){return S.H.useReducer(b,q,K)},re.useRef=function(b){return S.H.useRef(b)},re.useState=function(b){return S.H.useState(b)},re.useSyncExternalStore=function(b,q,K){return S.H.useSyncExternalStore(b,q,K)},re.useTransition=function(){return S.H.useTransition()},re.version="19.1.0",re}var ph;function os(){return ph||(ph=1,$r.exports=Vv()),$r.exports}var T=os(),Wr={exports:{}},nu={},Fr={exports:{}},Pr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gh;function Qv(){return gh||(gh=1,function(i){function o(N,k){var B=N.length;N.push(k);e:for(;0<B;){var Se=B-1>>>1,b=N[Se];if(0<d(b,k))N[Se]=k,N[B]=b,B=Se;else break e}}function f(N){return N.length===0?null:N[0]}function c(N){if(N.length===0)return null;var k=N[0],B=N.pop();if(B!==k){N[0]=B;e:for(var Se=0,b=N.length,q=b>>>1;Se<q;){var K=2*(Se+1)-1,L=N[K],$=K+1,de=N[$];if(0>d(L,B))$<b&&0>d(de,L)?(N[Se]=de,N[$]=B,Se=$):(N[Se]=L,N[K]=B,Se=K);else if($<b&&0>d(de,B))N[Se]=de,N[$]=B,Se=$;else break e}}return k}function d(N,k){var B=N.sortIndex-k.sortIndex;return B!==0?B:N.id-k.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;i.unstable_now=function(){return m.now()}}else{var g=Date,A=g.now();i.unstable_now=function(){return g.now()-A}}var v=[],y=[],j=1,U=null,O=3,X=!1,D=!1,G=!1,Q=!1,Y=typeof setTimeout=="function"?setTimeout:null,Z=typeof clearTimeout=="function"?clearTimeout:null,I=typeof setImmediate<"u"?setImmediate:null;function ce(N){for(var k=f(y);k!==null;){if(k.callback===null)c(y);else if(k.startTime<=N)c(y),k.sortIndex=k.expirationTime,o(v,k);else break;k=f(y)}}function S(N){if(G=!1,ce(N),!D)if(f(v)!==null)D=!0,V||(V=!0,qe());else{var k=f(y);k!==null&&Ne(S,k.startTime-N)}}var V=!1,ye=-1,J=5,ve=-1;function Qe(){return Q?!0:!(i.unstable_now()-ve<J)}function it(){if(Q=!1,V){var N=i.unstable_now();ve=N;var k=!0;try{e:{D=!1,G&&(G=!1,Z(ye),ye=-1),X=!0;var B=O;try{t:{for(ce(N),U=f(v);U!==null&&!(U.expirationTime>N&&Qe());){var Se=U.callback;if(typeof Se=="function"){U.callback=null,O=U.priorityLevel;var b=Se(U.expirationTime<=N);if(N=i.unstable_now(),typeof b=="function"){U.callback=b,ce(N),k=!0;break t}U===f(v)&&c(v),ce(N)}else c(v);U=f(v)}if(U!==null)k=!0;else{var q=f(y);q!==null&&Ne(S,q.startTime-N),k=!1}}break e}finally{U=null,O=B,X=!1}k=void 0}}finally{k?qe():V=!1}}}var qe;if(typeof I=="function")qe=function(){I(it)};else if(typeof MessageChannel<"u"){var Gt=new MessageChannel,qt=Gt.port2;Gt.port1.onmessage=it,qe=function(){qt.postMessage(null)}}else qe=function(){Y(it,0)};function Ne(N,k){ye=Y(function(){N(i.unstable_now())},k)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(N){N.callback=null},i.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):J=0<N?Math.floor(1e3/N):5},i.unstable_getCurrentPriorityLevel=function(){return O},i.unstable_next=function(N){switch(O){case 1:case 2:case 3:var k=3;break;default:k=O}var B=O;O=k;try{return N()}finally{O=B}},i.unstable_requestPaint=function(){Q=!0},i.unstable_runWithPriority=function(N,k){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var B=O;O=N;try{return k()}finally{O=B}},i.unstable_scheduleCallback=function(N,k,B){var Se=i.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?Se+B:Se):B=Se,N){case 1:var b=-1;break;case 2:b=250;break;case 5:b=1073741823;break;case 4:b=1e4;break;default:b=5e3}return b=B+b,N={id:j++,callback:k,priorityLevel:N,startTime:B,expirationTime:b,sortIndex:-1},B>Se?(N.sortIndex=B,o(y,N),f(v)===null&&N===f(y)&&(G?(Z(ye),ye=-1):G=!0,Ne(S,B-Se))):(N.sortIndex=b,o(v,N),D||X||(D=!0,V||(V=!0,qe()))),N},i.unstable_shouldYield=Qe,i.unstable_wrapCallback=function(N){var k=O;return function(){var B=O;O=k;try{return N.apply(this,arguments)}finally{O=B}}}}(Pr)),Pr}var bh;function Zv(){return bh||(bh=1,Fr.exports=Qv()),Fr.exports}var Ir={exports:{}},lt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xh;function Kv(){if(xh)return lt;xh=1;var i=os();function o(v){var y="https://react.dev/errors/"+v;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var j=2;j<arguments.length;j++)y+="&args[]="+encodeURIComponent(arguments[j])}return"Minified React error #"+v+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(){}var c={d:{f,r:function(){throw Error(o(522))},D:f,C:f,L:f,m:f,X:f,S:f,M:f},p:0,findDOMNode:null},d=Symbol.for("react.portal");function m(v,y,j){var U=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:U==null?null:""+U,children:v,containerInfo:y,implementation:j}}var g=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function A(v,y){if(v==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return lt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,lt.createPortal=function(v,y){var j=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(o(299));return m(v,y,null,j)},lt.flushSync=function(v){var y=g.T,j=c.p;try{if(g.T=null,c.p=2,v)return v()}finally{g.T=y,c.p=j,c.d.f()}},lt.preconnect=function(v,y){typeof v=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,c.d.C(v,y))},lt.prefetchDNS=function(v){typeof v=="string"&&c.d.D(v)},lt.preinit=function(v,y){if(typeof v=="string"&&y&&typeof y.as=="string"){var j=y.as,U=A(j,y.crossOrigin),O=typeof y.integrity=="string"?y.integrity:void 0,X=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;j==="style"?c.d.S(v,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:U,integrity:O,fetchPriority:X}):j==="script"&&c.d.X(v,{crossOrigin:U,integrity:O,fetchPriority:X,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},lt.preinitModule=function(v,y){if(typeof v=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var j=A(y.as,y.crossOrigin);c.d.M(v,{crossOrigin:j,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&c.d.M(v)},lt.preload=function(v,y){if(typeof v=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var j=y.as,U=A(j,y.crossOrigin);c.d.L(v,j,{crossOrigin:U,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},lt.preloadModule=function(v,y){if(typeof v=="string")if(y){var j=A(y.as,y.crossOrigin);c.d.m(v,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:j,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else c.d.m(v)},lt.requestFormReset=function(v){c.d.r(v)},lt.unstable_batchedUpdates=function(v,y){return v(y)},lt.useFormState=function(v,y,j){return g.H.useFormState(v,y,j)},lt.useFormStatus=function(){return g.H.useHostTransitionStatus()},lt.version="19.1.0",lt}var Sh;function Bh(){if(Sh)return Ir.exports;Sh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(o){console.error(o)}}return i(),Ir.exports=Kv(),Ir.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jh;function Jv(){if(jh)return nu;jh=1;var i=Zv(),o=os(),f=Bh();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function m(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function g(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function A(e){if(m(e)!==e)throw Error(c(188))}function v(e){var t=e.alternate;if(!t){if(t=m(e),t===null)throw Error(c(188));return t!==e?null:e}for(var l=e,a=t;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return A(n),e;if(u===a)return A(n),t;u=u.sibling}throw Error(c(188))}if(l.return!==a.return)l=n,a=u;else{for(var r=!1,h=n.child;h;){if(h===l){r=!0,l=n,a=u;break}if(h===a){r=!0,a=n,l=u;break}h=h.sibling}if(!r){for(h=u.child;h;){if(h===l){r=!0,l=u,a=n;break}if(h===a){r=!0,a=u,l=n;break}h=h.sibling}if(!r)throw Error(c(189))}}if(l.alternate!==a)throw Error(c(190))}if(l.tag!==3)throw Error(c(188));return l.stateNode.current===l?e:t}function y(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=y(e),t!==null)return t;e=e.sibling}return null}var j=Object.assign,U=Symbol.for("react.element"),O=Symbol.for("react.transitional.element"),X=Symbol.for("react.portal"),D=Symbol.for("react.fragment"),G=Symbol.for("react.strict_mode"),Q=Symbol.for("react.profiler"),Y=Symbol.for("react.provider"),Z=Symbol.for("react.consumer"),I=Symbol.for("react.context"),ce=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),V=Symbol.for("react.suspense_list"),ye=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),ve=Symbol.for("react.activity"),Qe=Symbol.for("react.memo_cache_sentinel"),it=Symbol.iterator;function qe(e){return e===null||typeof e!="object"?null:(e=it&&e[it]||e["@@iterator"],typeof e=="function"?e:null)}var Gt=Symbol.for("react.client.reference");function qt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Gt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case D:return"Fragment";case Q:return"Profiler";case G:return"StrictMode";case S:return"Suspense";case V:return"SuspenseList";case ve:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case X:return"Portal";case I:return(e.displayName||"Context")+".Provider";case Z:return(e._context.displayName||"Context")+".Consumer";case ce:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ye:return t=e.displayName||null,t!==null?t:qt(e.type)||"Memo";case J:t=e._payload,e=e._init;try{return qt(e(t))}catch{}}return null}var Ne=Array.isArray,N=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=f.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,B={pending:!1,data:null,method:null,action:null},Se=[],b=-1;function q(e){return{current:e}}function K(e){0>b||(e.current=Se[b],Se[b]=null,b--)}function L(e,t){b++,Se[b]=e.current,e.current=t}var $=q(null),de=q(null),ie=q(null),ge=q(null);function Re(e,t){switch(L(ie,t),L(de,e),L($,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Xd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Xd(t),e=kd(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}K($),L($,e)}function gt(){K($),K(de),K(ie)}function yl(e){e.memoizedState!==null&&L(ge,e);var t=$.current,l=kd(t,e.type);t!==l&&(L(de,e),L($,l))}function vl(e){de.current===e&&(K($),K(de)),ge.current===e&&(K(ge),Pn._currentValue=B)}var pl=Object.prototype.hasOwnProperty,Ui=i.unstable_scheduleCallback,Hi=i.unstable_cancelCallback,xm=i.unstable_shouldYield,Sm=i.unstable_requestPaint,Xt=i.unstable_now,jm=i.unstable_getCurrentPriorityLevel,xs=i.unstable_ImmediatePriority,Ss=i.unstable_UserBlockingPriority,hu=i.unstable_NormalPriority,Em=i.unstable_LowPriority,js=i.unstable_IdlePriority,_m=i.log,Tm=i.unstable_setDisableYieldValue,un=null,bt=null;function gl(e){if(typeof _m=="function"&&Tm(e),bt&&typeof bt.setStrictMode=="function")try{bt.setStrictMode(un,e)}catch{}}var xt=Math.clz32?Math.clz32:zm,Am=Math.log,Rm=Math.LN2;function zm(e){return e>>>=0,e===0?32:31-(Am(e)/Rm|0)|0}var mu=256,yu=4194304;function Jl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function vu(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var n=0,u=e.suspendedLanes,r=e.pingedLanes;e=e.warmLanes;var h=a&134217727;return h!==0?(a=h&~u,a!==0?n=Jl(a):(r&=h,r!==0?n=Jl(r):l||(l=h&~e,l!==0&&(n=Jl(l))))):(h=a&~u,h!==0?n=Jl(h):r!==0?n=Jl(r):l||(l=a&~e,l!==0&&(n=Jl(l)))),n===0?0:t!==0&&t!==n&&(t&u)===0&&(u=n&-n,l=t&-t,u>=l||u===32&&(l&4194048)!==0)?t:n}function cn(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function wm(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Es(){var e=mu;return mu<<=1,(mu&4194048)===0&&(mu=256),e}function _s(){var e=yu;return yu<<=1,(yu&62914560)===0&&(yu=4194304),e}function qi(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function rn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Om(e,t,l,a,n,u){var r=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var h=e.entanglements,p=e.expirationTimes,R=e.hiddenUpdates;for(l=r&~l;0<l;){var M=31-xt(l),H=1<<M;h[M]=0,p[M]=-1;var z=R[M];if(z!==null)for(R[M]=null,M=0;M<z.length;M++){var w=z[M];w!==null&&(w.lane&=-536870913)}l&=~H}a!==0&&Ts(e,a,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(r&~t))}function Ts(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-xt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function As(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-xt(l),n=1<<a;n&t|e[a]&t&&(e[a]|=t),l&=~n}}function Bi(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Yi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Rs(){var e=k.p;return e!==0?e:(e=window.event,e===void 0?32:rh(e.type))}function Nm(e,t){var l=k.p;try{return k.p=e,t()}finally{k.p=l}}var bl=Math.random().toString(36).slice(2),et="__reactFiber$"+bl,rt="__reactProps$"+bl,ya="__reactContainer$"+bl,Li="__reactEvents$"+bl,Mm="__reactListeners$"+bl,Dm="__reactHandles$"+bl,zs="__reactResources$"+bl,sn="__reactMarker$"+bl;function Gi(e){delete e[et],delete e[rt],delete e[Li],delete e[Mm],delete e[Dm]}function va(e){var t=e[et];if(t)return t;for(var l=e.parentNode;l;){if(t=l[ya]||l[et]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=Kd(e);e!==null;){if(l=e[et])return l;e=Kd(e)}return t}e=l,l=e.parentNode}return null}function pa(e){if(e=e[et]||e[ya]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function on(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function ga(e){var t=e[zs];return t||(t=e[zs]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ze(e){e[sn]=!0}var ws=new Set,Os={};function $l(e,t){ba(e,t),ba(e+"Capture",t)}function ba(e,t){for(Os[e]=t,e=0;e<t.length;e++)ws.add(t[e])}var Cm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ns={},Ms={};function Um(e){return pl.call(Ms,e)?!0:pl.call(Ns,e)?!1:Cm.test(e)?Ms[e]=!0:(Ns[e]=!0,!1)}function pu(e,t,l){if(Um(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function gu(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function Wt(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var Xi,Ds;function xa(e){if(Xi===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);Xi=t&&t[1]||"",Ds=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Xi+e+Ds}var ki=!1;function Vi(e,t){if(!e||ki)return"";ki=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(w){var z=w}Reflect.construct(e,[],H)}else{try{H.call()}catch(w){z=w}e.call(H.prototype)}}else{try{throw Error()}catch(w){z=w}(H=e())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(w){if(w&&z&&typeof w.stack=="string")return[w.stack,z.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),r=u[0],h=u[1];if(r&&h){var p=r.split(`
`),R=h.split(`
`);for(n=a=0;a<p.length&&!p[a].includes("DetermineComponentFrameRoot");)a++;for(;n<R.length&&!R[n].includes("DetermineComponentFrameRoot");)n++;if(a===p.length||n===R.length)for(a=p.length-1,n=R.length-1;1<=a&&0<=n&&p[a]!==R[n];)n--;for(;1<=a&&0<=n;a--,n--)if(p[a]!==R[n]){if(a!==1||n!==1)do if(a--,n--,0>n||p[a]!==R[n]){var M=`
`+p[a].replace(" at new "," at ");return e.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",e.displayName)),M}while(1<=a&&0<=n);break}}}finally{ki=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?xa(l):""}function Hm(e){switch(e.tag){case 26:case 27:case 5:return xa(e.type);case 16:return xa("Lazy");case 13:return xa("Suspense");case 19:return xa("SuspenseList");case 0:case 15:return Vi(e.type,!1);case 11:return Vi(e.type.render,!1);case 1:return Vi(e.type,!0);case 31:return xa("Activity");default:return""}}function Cs(e){try{var t="";do t+=Hm(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function zt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Us(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function qm(e){var t=Us(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(r){a=""+r,u.call(this,r)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(r){a=""+r},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function bu(e){e._valueTracker||(e._valueTracker=qm(e))}function Hs(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=Us(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function xu(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Bm=/[\n"\\]/g;function wt(e){return e.replace(Bm,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Qi(e,t,l,a,n,u,r,h){e.name="",r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?e.type=r:e.removeAttribute("type"),t!=null?r==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+zt(t)):e.value!==""+zt(t)&&(e.value=""+zt(t)):r!=="submit"&&r!=="reset"||e.removeAttribute("value"),t!=null?Zi(e,r,zt(t)):l!=null?Zi(e,r,zt(l)):a!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.name=""+zt(h):e.removeAttribute("name")}function qs(e,t,l,a,n,u,r,h){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;l=l!=null?""+zt(l):"",t=t!=null?""+zt(t):l,h||t===e.value||(e.value=t),e.defaultValue=t}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=h?e.checked:!!a,e.defaultChecked=!!a,r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.name=r)}function Zi(e,t,l){t==="number"&&xu(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function Sa(e,t,l,a){if(e=e.options,t){t={};for(var n=0;n<l.length;n++)t["$"+l[n]]=!0;for(l=0;l<e.length;l++)n=t.hasOwnProperty("$"+e[l].value),e[l].selected!==n&&(e[l].selected=n),n&&a&&(e[l].defaultSelected=!0)}else{for(l=""+zt(l),t=null,n=0;n<e.length;n++){if(e[n].value===l){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function Bs(e,t,l){if(t!=null&&(t=""+zt(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+zt(l):""}function Ys(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(c(92));if(Ne(a)){if(1<a.length)throw Error(c(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=zt(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function ja(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var Ym=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ls(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||Ym.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function Gs(e,t,l){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in t)a=t[n],t.hasOwnProperty(n)&&l[n]!==a&&Ls(e,n,a)}else for(var u in t)t.hasOwnProperty(u)&&Ls(e,u,t[u])}function Ki(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Lm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Gm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Su(e){return Gm.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ji=null;function $i(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ea=null,_a=null;function Xs(e){var t=pa(e);if(t&&(e=t.stateNode)){var l=e[rt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Qi(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+wt(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var n=a[rt]||null;if(!n)throw Error(c(90));Qi(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&Hs(a)}break e;case"textarea":Bs(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&Sa(e,!!l.multiple,t,!1)}}}var Wi=!1;function ks(e,t,l){if(Wi)return e(t,l);Wi=!0;try{var a=e(t);return a}finally{if(Wi=!1,(Ea!==null||_a!==null)&&(ii(),Ea&&(t=Ea,e=_a,_a=Ea=null,Xs(t),e)))for(t=0;t<e.length;t++)Xs(e[t])}}function fn(e,t){var l=e.stateNode;if(l===null)return null;var a=l[rt]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(c(231,t,typeof l));return l}var Ft=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Fi=!1;if(Ft)try{var dn={};Object.defineProperty(dn,"passive",{get:function(){Fi=!0}}),window.addEventListener("test",dn,dn),window.removeEventListener("test",dn,dn)}catch{Fi=!1}var xl=null,Pi=null,ju=null;function Vs(){if(ju)return ju;var e,t=Pi,l=t.length,a,n="value"in xl?xl.value:xl.textContent,u=n.length;for(e=0;e<l&&t[e]===n[e];e++);var r=l-e;for(a=1;a<=r&&t[l-a]===n[u-a];a++);return ju=n.slice(e,1<a?1-a:void 0)}function Eu(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function _u(){return!0}function Qs(){return!1}function st(e){function t(l,a,n,u,r){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=r,this.currentTarget=null;for(var h in e)e.hasOwnProperty(h)&&(l=e[h],this[h]=l?l(u):u[h]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?_u:Qs,this.isPropagationStopped=Qs,this}return j(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=_u)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=_u)},persist:function(){},isPersistent:_u}),t}var Wl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Tu=st(Wl),hn=j({},Wl,{view:0,detail:0}),Xm=st(hn),Ii,ec,mn,Au=j({},hn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:lc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==mn&&(mn&&e.type==="mousemove"?(Ii=e.screenX-mn.screenX,ec=e.screenY-mn.screenY):ec=Ii=0,mn=e),Ii)},movementY:function(e){return"movementY"in e?e.movementY:ec}}),Zs=st(Au),km=j({},Au,{dataTransfer:0}),Vm=st(km),Qm=j({},hn,{relatedTarget:0}),tc=st(Qm),Zm=j({},Wl,{animationName:0,elapsedTime:0,pseudoElement:0}),Km=st(Zm),Jm=j({},Wl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$m=st(Jm),Wm=j({},Wl,{data:0}),Ks=st(Wm),Fm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Pm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Im={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ey(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Im[e])?!!t[e]:!1}function lc(){return ey}var ty=j({},hn,{key:function(e){if(e.key){var t=Fm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Eu(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Pm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:lc,charCode:function(e){return e.type==="keypress"?Eu(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Eu(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ly=st(ty),ay=j({},Au,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Js=st(ay),ny=j({},hn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:lc}),uy=st(ny),iy=j({},Wl,{propertyName:0,elapsedTime:0,pseudoElement:0}),cy=st(iy),ry=j({},Au,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),sy=st(ry),oy=j({},Wl,{newState:0,oldState:0}),fy=st(oy),dy=[9,13,27,32],ac=Ft&&"CompositionEvent"in window,yn=null;Ft&&"documentMode"in document&&(yn=document.documentMode);var hy=Ft&&"TextEvent"in window&&!yn,$s=Ft&&(!ac||yn&&8<yn&&11>=yn),Ws=" ",Fs=!1;function Ps(e,t){switch(e){case"keyup":return dy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Is(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ta=!1;function my(e,t){switch(e){case"compositionend":return Is(t);case"keypress":return t.which!==32?null:(Fs=!0,Ws);case"textInput":return e=t.data,e===Ws&&Fs?null:e;default:return null}}function yy(e,t){if(Ta)return e==="compositionend"||!ac&&Ps(e,t)?(e=Vs(),ju=Pi=xl=null,Ta=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return $s&&t.locale!=="ko"?null:t.data;default:return null}}var vy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function eo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!vy[e.type]:t==="textarea"}function to(e,t,l,a){Ea?_a?_a.push(a):_a=[a]:Ea=a,t=di(t,"onChange"),0<t.length&&(l=new Tu("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var vn=null,pn=null;function py(e){qd(e,0)}function Ru(e){var t=on(e);if(Hs(t))return e}function lo(e,t){if(e==="change")return t}var ao=!1;if(Ft){var nc;if(Ft){var uc="oninput"in document;if(!uc){var no=document.createElement("div");no.setAttribute("oninput","return;"),uc=typeof no.oninput=="function"}nc=uc}else nc=!1;ao=nc&&(!document.documentMode||9<document.documentMode)}function uo(){vn&&(vn.detachEvent("onpropertychange",io),pn=vn=null)}function io(e){if(e.propertyName==="value"&&Ru(pn)){var t=[];to(t,pn,e,$i(e)),ks(py,t)}}function gy(e,t,l){e==="focusin"?(uo(),vn=t,pn=l,vn.attachEvent("onpropertychange",io)):e==="focusout"&&uo()}function by(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ru(pn)}function xy(e,t){if(e==="click")return Ru(t)}function Sy(e,t){if(e==="input"||e==="change")return Ru(t)}function jy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var St=typeof Object.is=="function"?Object.is:jy;function gn(e,t){if(St(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!pl.call(t,n)||!St(e[n],t[n]))return!1}return!0}function co(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ro(e,t){var l=co(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=co(l)}}function so(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?so(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function oo(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=xu(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=xu(e.document)}return t}function ic(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Ey=Ft&&"documentMode"in document&&11>=document.documentMode,Aa=null,cc=null,bn=null,rc=!1;function fo(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;rc||Aa==null||Aa!==xu(a)||(a=Aa,"selectionStart"in a&&ic(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),bn&&gn(bn,a)||(bn=a,a=di(cc,"onSelect"),0<a.length&&(t=new Tu("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=Aa)))}function Fl(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var Ra={animationend:Fl("Animation","AnimationEnd"),animationiteration:Fl("Animation","AnimationIteration"),animationstart:Fl("Animation","AnimationStart"),transitionrun:Fl("Transition","TransitionRun"),transitionstart:Fl("Transition","TransitionStart"),transitioncancel:Fl("Transition","TransitionCancel"),transitionend:Fl("Transition","TransitionEnd")},sc={},ho={};Ft&&(ho=document.createElement("div").style,"AnimationEvent"in window||(delete Ra.animationend.animation,delete Ra.animationiteration.animation,delete Ra.animationstart.animation),"TransitionEvent"in window||delete Ra.transitionend.transition);function Pl(e){if(sc[e])return sc[e];if(!Ra[e])return e;var t=Ra[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in ho)return sc[e]=t[l];return e}var mo=Pl("animationend"),yo=Pl("animationiteration"),vo=Pl("animationstart"),_y=Pl("transitionrun"),Ty=Pl("transitionstart"),Ay=Pl("transitioncancel"),po=Pl("transitionend"),go=new Map,oc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");oc.push("scrollEnd");function Bt(e,t){go.set(e,t),$l(t,[e])}var bo=new WeakMap;function Ot(e,t){if(typeof e=="object"&&e!==null){var l=bo.get(e);return l!==void 0?l:(t={value:e,source:t,stack:Cs(t)},bo.set(e,t),t)}return{value:e,source:t,stack:Cs(t)}}var Nt=[],za=0,fc=0;function zu(){for(var e=za,t=fc=za=0;t<e;){var l=Nt[t];Nt[t++]=null;var a=Nt[t];Nt[t++]=null;var n=Nt[t];Nt[t++]=null;var u=Nt[t];if(Nt[t++]=null,a!==null&&n!==null){var r=a.pending;r===null?n.next=n:(n.next=r.next,r.next=n),a.pending=n}u!==0&&xo(l,n,u)}}function wu(e,t,l,a){Nt[za++]=e,Nt[za++]=t,Nt[za++]=l,Nt[za++]=a,fc|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function dc(e,t,l,a){return wu(e,t,l,a),Ou(e)}function wa(e,t){return wu(e,null,null,t),Ou(e)}function xo(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=e.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,n&&t!==null&&(n=31-xt(l),e=u.hiddenUpdates,a=e[n],a===null?e[n]=[t]:a.push(t),t.lane=l|536870912),u):null}function Ou(e){if(50<Vn)throw Vn=0,gr=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Oa={};function Ry(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jt(e,t,l,a){return new Ry(e,t,l,a)}function hc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Pt(e,t){var l=e.alternate;return l===null?(l=jt(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function So(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Nu(e,t,l,a,n,u){var r=0;if(a=e,typeof e=="function")hc(e)&&(r=1);else if(typeof e=="string")r=wv(e,l,$.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ve:return e=jt(31,l,t,n),e.elementType=ve,e.lanes=u,e;case D:return Il(l.children,n,u,t);case G:r=8,n|=24;break;case Q:return e=jt(12,l,t,n|2),e.elementType=Q,e.lanes=u,e;case S:return e=jt(13,l,t,n),e.elementType=S,e.lanes=u,e;case V:return e=jt(19,l,t,n),e.elementType=V,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Y:case I:r=10;break e;case Z:r=9;break e;case ce:r=11;break e;case ye:r=14;break e;case J:r=16,a=null;break e}r=29,l=Error(c(130,e===null?"null":typeof e,"")),a=null}return t=jt(r,l,t,n),t.elementType=e,t.type=a,t.lanes=u,t}function Il(e,t,l,a){return e=jt(7,e,a,t),e.lanes=l,e}function mc(e,t,l){return e=jt(6,e,null,t),e.lanes=l,e}function yc(e,t,l){return t=jt(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Na=[],Ma=0,Mu=null,Du=0,Mt=[],Dt=0,ea=null,It=1,el="";function ta(e,t){Na[Ma++]=Du,Na[Ma++]=Mu,Mu=e,Du=t}function jo(e,t,l){Mt[Dt++]=It,Mt[Dt++]=el,Mt[Dt++]=ea,ea=e;var a=It;e=el;var n=32-xt(a)-1;a&=~(1<<n),l+=1;var u=32-xt(t)+n;if(30<u){var r=n-n%5;u=(a&(1<<r)-1).toString(32),a>>=r,n-=r,It=1<<32-xt(t)+n|l<<n|a,el=u+e}else It=1<<u|l<<n|a,el=e}function vc(e){e.return!==null&&(ta(e,1),jo(e,1,0))}function pc(e){for(;e===Mu;)Mu=Na[--Ma],Na[Ma]=null,Du=Na[--Ma],Na[Ma]=null;for(;e===ea;)ea=Mt[--Dt],Mt[Dt]=null,el=Mt[--Dt],Mt[Dt]=null,It=Mt[--Dt],Mt[Dt]=null}var ct=null,Ce=null,xe=!1,la=null,kt=!1,gc=Error(c(519));function aa(e){var t=Error(c(418,""));throw jn(Ot(t,e)),gc}function Eo(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[et]=e,t[rt]=a,l){case"dialog":me("cancel",t),me("close",t);break;case"iframe":case"object":case"embed":me("load",t);break;case"video":case"audio":for(l=0;l<Zn.length;l++)me(Zn[l],t);break;case"source":me("error",t);break;case"img":case"image":case"link":me("error",t),me("load",t);break;case"details":me("toggle",t);break;case"input":me("invalid",t),qs(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),bu(t);break;case"select":me("invalid",t);break;case"textarea":me("invalid",t),Ys(t,a.value,a.defaultValue,a.children),bu(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||Gd(t.textContent,l)?(a.popover!=null&&(me("beforetoggle",t),me("toggle",t)),a.onScroll!=null&&me("scroll",t),a.onScrollEnd!=null&&me("scrollend",t),a.onClick!=null&&(t.onclick=hi),t=!0):t=!1,t||aa(e)}function _o(e){for(ct=e.return;ct;)switch(ct.tag){case 5:case 13:kt=!1;return;case 27:case 3:kt=!0;return;default:ct=ct.return}}function xn(e){if(e!==ct)return!1;if(!xe)return _o(e),xe=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||Cr(e.type,e.memoizedProps)),l=!l),l&&Ce&&aa(e),_o(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){Ce=Lt(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}Ce=null}}else t===27?(t=Ce,Hl(e.type)?(e=Br,Br=null,Ce=e):Ce=t):Ce=ct?Lt(e.stateNode.nextSibling):null;return!0}function Sn(){Ce=ct=null,xe=!1}function To(){var e=la;return e!==null&&(dt===null?dt=e:dt.push.apply(dt,e),la=null),e}function jn(e){la===null?la=[e]:la.push(e)}var bc=q(null),na=null,tl=null;function Sl(e,t,l){L(bc,t._currentValue),t._currentValue=l}function ll(e){e._currentValue=bc.current,K(bc)}function xc(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function Sc(e,t,l,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var r=n.child;u=u.firstContext;e:for(;u!==null;){var h=u;u=n;for(var p=0;p<t.length;p++)if(h.context===t[p]){u.lanes|=l,h=u.alternate,h!==null&&(h.lanes|=l),xc(u.return,l,e),a||(r=null);break e}u=h.next}}else if(n.tag===18){if(r=n.return,r===null)throw Error(c(341));r.lanes|=l,u=r.alternate,u!==null&&(u.lanes|=l),xc(r,l,e),r=null}else r=n.child;if(r!==null)r.return=n;else for(r=n;r!==null;){if(r===e){r=null;break}if(n=r.sibling,n!==null){n.return=r.return,r=n;break}r=r.return}n=r}}function En(e,t,l,a){e=null;for(var n=t,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var r=n.alternate;if(r===null)throw Error(c(387));if(r=r.memoizedProps,r!==null){var h=n.type;St(n.pendingProps.value,r.value)||(e!==null?e.push(h):e=[h])}}else if(n===ge.current){if(r=n.alternate,r===null)throw Error(c(387));r.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Pn):e=[Pn])}n=n.return}e!==null&&Sc(t,e,l,a),t.flags|=262144}function Cu(e){for(e=e.firstContext;e!==null;){if(!St(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ua(e){na=e,tl=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function tt(e){return Ao(na,e)}function Uu(e,t){return na===null&&ua(e),Ao(e,t)}function Ao(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},tl===null){if(e===null)throw Error(c(308));tl=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else tl=tl.next=t;return l}var zy=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},wy=i.unstable_scheduleCallback,Oy=i.unstable_NormalPriority,Xe={$$typeof:I,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function jc(){return{controller:new zy,data:new Map,refCount:0}}function _n(e){e.refCount--,e.refCount===0&&wy(Oy,function(){e.controller.abort()})}var Tn=null,Ec=0,Da=0,Ca=null;function Ny(e,t){if(Tn===null){var l=Tn=[];Ec=0,Da=Tr(),Ca={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Ec++,t.then(Ro,Ro),t}function Ro(){if(--Ec===0&&Tn!==null){Ca!==null&&(Ca.status="fulfilled");var e=Tn;Tn=null,Da=0,Ca=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function My(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var n=0;n<l.length;n++)(0,l[n])(t)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var zo=N.S;N.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Ny(e,t),zo!==null&&zo(e,t)};var ia=q(null);function _c(){var e=ia.current;return e!==null?e:we.pooledCache}function Hu(e,t){t===null?L(ia,ia.current):L(ia,t.pool)}function wo(){var e=_c();return e===null?null:{parent:Xe._currentValue,pool:e}}var An=Error(c(460)),Oo=Error(c(474)),qu=Error(c(542)),Tc={then:function(){}};function No(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Bu(){}function Mo(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(Bu,Bu),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Co(e),e;default:if(typeof t.status=="string")t.then(Bu,Bu);else{if(e=we,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=a}},function(a){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Co(e),e}throw Rn=t,An}}var Rn=null;function Do(){if(Rn===null)throw Error(c(459));var e=Rn;return Rn=null,e}function Co(e){if(e===An||e===qu)throw Error(c(483))}var jl=!1;function Ac(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Rc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function El(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function _l(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(je&2)!==0){var n=a.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),a.pending=t,t=Ou(e),xo(e,null,l),t}return wu(e,a,t,l),Ou(e)}function zn(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,As(e,l)}}function zc(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var r={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=r:u=u.next=r,l=l.next}while(l!==null);u===null?n=u=t:u=u.next=t}else n=u=t;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var wc=!1;function wn(){if(wc){var e=Ca;if(e!==null)throw e}}function On(e,t,l,a){wc=!1;var n=e.updateQueue;jl=!1;var u=n.firstBaseUpdate,r=n.lastBaseUpdate,h=n.shared.pending;if(h!==null){n.shared.pending=null;var p=h,R=p.next;p.next=null,r===null?u=R:r.next=R,r=p;var M=e.alternate;M!==null&&(M=M.updateQueue,h=M.lastBaseUpdate,h!==r&&(h===null?M.firstBaseUpdate=R:h.next=R,M.lastBaseUpdate=p))}if(u!==null){var H=n.baseState;r=0,M=R=p=null,h=u;do{var z=h.lane&-536870913,w=z!==h.lane;if(w?(pe&z)===z:(a&z)===z){z!==0&&z===Da&&(wc=!0),M!==null&&(M=M.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});e:{var ue=e,te=h;z=t;var Ae=l;switch(te.tag){case 1:if(ue=te.payload,typeof ue=="function"){H=ue.call(Ae,H,z);break e}H=ue;break e;case 3:ue.flags=ue.flags&-65537|128;case 0:if(ue=te.payload,z=typeof ue=="function"?ue.call(Ae,H,z):ue,z==null)break e;H=j({},H,z);break e;case 2:jl=!0}}z=h.callback,z!==null&&(e.flags|=64,w&&(e.flags|=8192),w=n.callbacks,w===null?n.callbacks=[z]:w.push(z))}else w={lane:z,tag:h.tag,payload:h.payload,callback:h.callback,next:null},M===null?(R=M=w,p=H):M=M.next=w,r|=z;if(h=h.next,h===null){if(h=n.shared.pending,h===null)break;w=h,h=w.next,w.next=null,n.lastBaseUpdate=w,n.shared.pending=null}}while(!0);M===null&&(p=H),n.baseState=p,n.firstBaseUpdate=R,n.lastBaseUpdate=M,u===null&&(n.shared.lanes=0),Ml|=r,e.lanes=r,e.memoizedState=H}}function Uo(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function Ho(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)Uo(l[e],t)}var Ua=q(null),Yu=q(0);function qo(e,t){e=sl,L(Yu,e),L(Ua,t),sl=e|t.baseLanes}function Oc(){L(Yu,sl),L(Ua,Ua.current)}function Nc(){sl=Yu.current,K(Ua),K(Yu)}var Tl=0,se=null,_e=null,Le=null,Lu=!1,Ha=!1,ca=!1,Gu=0,Nn=0,qa=null,Dy=0;function Be(){throw Error(c(321))}function Mc(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!St(e[l],t[l]))return!1;return!0}function Dc(e,t,l,a,n,u){return Tl=u,se=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,N.H=e===null||e.memoizedState===null?Sf:jf,ca=!1,u=l(a,n),ca=!1,Ha&&(u=Yo(t,l,a,n)),Bo(e),u}function Bo(e){N.H=Ku;var t=_e!==null&&_e.next!==null;if(Tl=0,Le=_e=se=null,Lu=!1,Nn=0,qa=null,t)throw Error(c(300));e===null||Ke||(e=e.dependencies,e!==null&&Cu(e)&&(Ke=!0))}function Yo(e,t,l,a){se=e;var n=0;do{if(Ha&&(qa=null),Nn=0,Ha=!1,25<=n)throw Error(c(301));if(n+=1,Le=_e=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}N.H=Ly,u=t(l,a)}while(Ha);return u}function Cy(){var e=N.H,t=e.useState()[0];return t=typeof t.then=="function"?Mn(t):t,e=e.useState()[0],(_e!==null?_e.memoizedState:null)!==e&&(se.flags|=1024),t}function Cc(){var e=Gu!==0;return Gu=0,e}function Uc(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function Hc(e){if(Lu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Lu=!1}Tl=0,Le=_e=se=null,Ha=!1,Nn=Gu=0,qa=null}function ot(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Le===null?se.memoizedState=Le=e:Le=Le.next=e,Le}function Ge(){if(_e===null){var e=se.alternate;e=e!==null?e.memoizedState:null}else e=_e.next;var t=Le===null?se.memoizedState:Le.next;if(t!==null)Le=t,_e=e;else{if(e===null)throw se.alternate===null?Error(c(467)):Error(c(310));_e=e,e={memoizedState:_e.memoizedState,baseState:_e.baseState,baseQueue:_e.baseQueue,queue:_e.queue,next:null},Le===null?se.memoizedState=Le=e:Le=Le.next=e}return Le}function qc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Mn(e){var t=Nn;return Nn+=1,qa===null&&(qa=[]),e=Mo(qa,e,t),t=se,(Le===null?t.memoizedState:Le.next)===null&&(t=t.alternate,N.H=t===null||t.memoizedState===null?Sf:jf),e}function Xu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Mn(e);if(e.$$typeof===I)return tt(e)}throw Error(c(438,String(e)))}function Bc(e){var t=null,l=se.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=se.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=qc(),se.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=Qe;return t.index++,l}function al(e,t){return typeof t=="function"?t(e):t}function ku(e){var t=Ge();return Yc(t,_e,e)}function Yc(e,t,l){var a=e.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=l;var n=e.baseQueue,u=a.pending;if(u!==null){if(n!==null){var r=n.next;n.next=u.next,u.next=r}t.baseQueue=n=u,a.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{t=n.next;var h=r=null,p=null,R=t,M=!1;do{var H=R.lane&-536870913;if(H!==R.lane?(pe&H)===H:(Tl&H)===H){var z=R.revertLane;if(z===0)p!==null&&(p=p.next={lane:0,revertLane:0,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null}),H===Da&&(M=!0);else if((Tl&z)===z){R=R.next,z===Da&&(M=!0);continue}else H={lane:0,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},p===null?(h=p=H,r=u):p=p.next=H,se.lanes|=z,Ml|=z;H=R.action,ca&&l(u,H),u=R.hasEagerState?R.eagerState:l(u,H)}else z={lane:H,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},p===null?(h=p=z,r=u):p=p.next=z,se.lanes|=H,Ml|=H;R=R.next}while(R!==null&&R!==t);if(p===null?r=u:p.next=h,!St(u,e.memoizedState)&&(Ke=!0,M&&(l=Ca,l!==null)))throw l;e.memoizedState=u,e.baseState=r,e.baseQueue=p,a.lastRenderedState=u}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Lc(e){var t=Ge(),l=t.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=e;var a=l.dispatch,n=l.pending,u=t.memoizedState;if(n!==null){l.pending=null;var r=n=n.next;do u=e(u,r.action),r=r.next;while(r!==n);St(u,t.memoizedState)||(Ke=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),l.lastRenderedState=u}return[u,a]}function Lo(e,t,l){var a=se,n=Ge(),u=xe;if(u){if(l===void 0)throw Error(c(407));l=l()}else l=t();var r=!St((_e||n).memoizedState,l);r&&(n.memoizedState=l,Ke=!0),n=n.queue;var h=ko.bind(null,a,n,e);if(Dn(2048,8,h,[e]),n.getSnapshot!==t||r||Le!==null&&Le.memoizedState.tag&1){if(a.flags|=2048,Ba(9,Vu(),Xo.bind(null,a,n,l,t),null),we===null)throw Error(c(349));u||(Tl&124)!==0||Go(a,t,l)}return l}function Go(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=se.updateQueue,t===null?(t=qc(),se.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function Xo(e,t,l,a){t.value=l,t.getSnapshot=a,Vo(t)&&Qo(e)}function ko(e,t,l){return l(function(){Vo(t)&&Qo(e)})}function Vo(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!St(e,l)}catch{return!0}}function Qo(e){var t=wa(e,2);t!==null&&Rt(t,e,2)}function Gc(e){var t=ot();if(typeof e=="function"){var l=e;if(e=l(),ca){gl(!0);try{l()}finally{gl(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:al,lastRenderedState:e},t}function Zo(e,t,l,a){return e.baseState=l,Yc(e,_e,typeof a=="function"?a:al)}function Uy(e,t,l,a,n){if(Zu(e))throw Error(c(485));if(e=t.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(r){u.listeners.push(r)}};N.T!==null?l(!0):u.isTransition=!1,a(u),l=t.pending,l===null?(u.next=t.pending=u,Ko(t,u)):(u.next=l.next,t.pending=l.next=u)}}function Ko(e,t){var l=t.action,a=t.payload,n=e.state;if(t.isTransition){var u=N.T,r={};N.T=r;try{var h=l(n,a),p=N.S;p!==null&&p(r,h),Jo(e,t,h)}catch(R){Xc(e,t,R)}finally{N.T=u}}else try{u=l(n,a),Jo(e,t,u)}catch(R){Xc(e,t,R)}}function Jo(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){$o(e,t,a)},function(a){return Xc(e,t,a)}):$o(e,t,l)}function $o(e,t,l){t.status="fulfilled",t.value=l,Wo(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,Ko(e,l)))}function Xc(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,Wo(t),t=t.next;while(t!==a)}e.action=null}function Wo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Fo(e,t){return t}function Po(e,t){if(xe){var l=we.formState;if(l!==null){e:{var a=se;if(xe){if(Ce){t:{for(var n=Ce,u=kt;n.nodeType!==8;){if(!u){n=null;break t}if(n=Lt(n.nextSibling),n===null){n=null;break t}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Ce=Lt(n.nextSibling),a=n.data==="F!";break e}}aa(a)}a=!1}a&&(t=l[0])}}return l=ot(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Fo,lastRenderedState:t},l.queue=a,l=gf.bind(null,se,a),a.dispatch=l,a=Gc(!1),u=Kc.bind(null,se,!1,a.queue),a=ot(),n={state:t,dispatch:null,action:e,pending:null},a.queue=n,l=Uy.bind(null,se,n,u,l),n.dispatch=l,a.memoizedState=e,[t,l,!1]}function Io(e){var t=Ge();return ef(t,_e,e)}function ef(e,t,l){if(t=Yc(e,t,Fo)[0],e=ku(al)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=Mn(t)}catch(r){throw r===An?qu:r}else a=t;t=Ge();var n=t.queue,u=n.dispatch;return l!==t.memoizedState&&(se.flags|=2048,Ba(9,Vu(),Hy.bind(null,n,l),null)),[a,u,e]}function Hy(e,t){e.action=t}function tf(e){var t=Ge(),l=_e;if(l!==null)return ef(t,l,e);Ge(),t=t.memoizedState,l=Ge();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function Ba(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=se.updateQueue,t===null&&(t=qc(),se.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function Vu(){return{destroy:void 0,resource:void 0}}function lf(){return Ge().memoizedState}function Qu(e,t,l,a){var n=ot();a=a===void 0?null:a,se.flags|=e,n.memoizedState=Ba(1|t,Vu(),l,a)}function Dn(e,t,l,a){var n=Ge();a=a===void 0?null:a;var u=n.memoizedState.inst;_e!==null&&a!==null&&Mc(a,_e.memoizedState.deps)?n.memoizedState=Ba(t,u,l,a):(se.flags|=e,n.memoizedState=Ba(1|t,u,l,a))}function af(e,t){Qu(8390656,8,e,t)}function nf(e,t){Dn(2048,8,e,t)}function uf(e,t){return Dn(4,2,e,t)}function cf(e,t){return Dn(4,4,e,t)}function rf(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function sf(e,t,l){l=l!=null?l.concat([e]):null,Dn(4,4,rf.bind(null,t,e),l)}function kc(){}function of(e,t){var l=Ge();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&Mc(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function ff(e,t){var l=Ge();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&Mc(t,a[1]))return a[0];if(a=e(),ca){gl(!0);try{e()}finally{gl(!1)}}return l.memoizedState=[a,t],a}function Vc(e,t,l){return l===void 0||(Tl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=md(),se.lanes|=e,Ml|=e,l)}function df(e,t,l,a){return St(l,t)?l:Ua.current!==null?(e=Vc(e,l,a),St(e,t)||(Ke=!0),e):(Tl&42)===0?(Ke=!0,e.memoizedState=l):(e=md(),se.lanes|=e,Ml|=e,t)}function hf(e,t,l,a,n){var u=k.p;k.p=u!==0&&8>u?u:8;var r=N.T,h={};N.T=h,Kc(e,!1,t,l);try{var p=n(),R=N.S;if(R!==null&&R(h,p),p!==null&&typeof p=="object"&&typeof p.then=="function"){var M=My(p,a);Cn(e,t,M,At(e))}else Cn(e,t,a,At(e))}catch(H){Cn(e,t,{then:function(){},status:"rejected",reason:H},At())}finally{k.p=u,N.T=r}}function qy(){}function Qc(e,t,l,a){if(e.tag!==5)throw Error(c(476));var n=mf(e).queue;hf(e,n,t,B,l===null?qy:function(){return yf(e),l(a)})}function mf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:B,baseState:B,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:al,lastRenderedState:B},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:al,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function yf(e){var t=mf(e).next.queue;Cn(e,t,{},At())}function Zc(){return tt(Pn)}function vf(){return Ge().memoizedState}function pf(){return Ge().memoizedState}function By(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=At();e=El(l);var a=_l(t,e,l);a!==null&&(Rt(a,t,l),zn(a,t,l)),t={cache:jc()},e.payload=t;return}t=t.return}}function Yy(e,t,l){var a=At();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Zu(e)?bf(t,l):(l=dc(e,t,l,a),l!==null&&(Rt(l,e,a),xf(l,t,a)))}function gf(e,t,l){var a=At();Cn(e,t,l,a)}function Cn(e,t,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Zu(e))bf(t,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var r=t.lastRenderedState,h=u(r,l);if(n.hasEagerState=!0,n.eagerState=h,St(h,r))return wu(e,t,n,0),we===null&&zu(),!1}catch{}finally{}if(l=dc(e,t,n,a),l!==null)return Rt(l,e,a),xf(l,t,a),!0}return!1}function Kc(e,t,l,a){if(a={lane:2,revertLane:Tr(),action:a,hasEagerState:!1,eagerState:null,next:null},Zu(e)){if(t)throw Error(c(479))}else t=dc(e,l,a,2),t!==null&&Rt(t,e,2)}function Zu(e){var t=e.alternate;return e===se||t!==null&&t===se}function bf(e,t){Ha=Lu=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function xf(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,As(e,l)}}var Ku={readContext:tt,use:Xu,useCallback:Be,useContext:Be,useEffect:Be,useImperativeHandle:Be,useLayoutEffect:Be,useInsertionEffect:Be,useMemo:Be,useReducer:Be,useRef:Be,useState:Be,useDebugValue:Be,useDeferredValue:Be,useTransition:Be,useSyncExternalStore:Be,useId:Be,useHostTransitionStatus:Be,useFormState:Be,useActionState:Be,useOptimistic:Be,useMemoCache:Be,useCacheRefresh:Be},Sf={readContext:tt,use:Xu,useCallback:function(e,t){return ot().memoizedState=[e,t===void 0?null:t],e},useContext:tt,useEffect:af,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,Qu(4194308,4,rf.bind(null,t,e),l)},useLayoutEffect:function(e,t){return Qu(4194308,4,e,t)},useInsertionEffect:function(e,t){Qu(4,2,e,t)},useMemo:function(e,t){var l=ot();t=t===void 0?null:t;var a=e();if(ca){gl(!0);try{e()}finally{gl(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=ot();if(l!==void 0){var n=l(t);if(ca){gl(!0);try{l(t)}finally{gl(!1)}}}else n=t;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=Yy.bind(null,se,e),[a.memoizedState,e]},useRef:function(e){var t=ot();return e={current:e},t.memoizedState=e},useState:function(e){e=Gc(e);var t=e.queue,l=gf.bind(null,se,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:kc,useDeferredValue:function(e,t){var l=ot();return Vc(l,e,t)},useTransition:function(){var e=Gc(!1);return e=hf.bind(null,se,e.queue,!0,!1),ot().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=se,n=ot();if(xe){if(l===void 0)throw Error(c(407));l=l()}else{if(l=t(),we===null)throw Error(c(349));(pe&124)!==0||Go(a,t,l)}n.memoizedState=l;var u={value:l,getSnapshot:t};return n.queue=u,af(ko.bind(null,a,u,e),[e]),a.flags|=2048,Ba(9,Vu(),Xo.bind(null,a,u,l,t),null),l},useId:function(){var e=ot(),t=we.identifierPrefix;if(xe){var l=el,a=It;l=(a&~(1<<32-xt(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=Gu++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=Dy++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Zc,useFormState:Po,useActionState:Po,useOptimistic:function(e){var t=ot();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=Kc.bind(null,se,!0,l),l.dispatch=t,[e,t]},useMemoCache:Bc,useCacheRefresh:function(){return ot().memoizedState=By.bind(null,se)}},jf={readContext:tt,use:Xu,useCallback:of,useContext:tt,useEffect:nf,useImperativeHandle:sf,useInsertionEffect:uf,useLayoutEffect:cf,useMemo:ff,useReducer:ku,useRef:lf,useState:function(){return ku(al)},useDebugValue:kc,useDeferredValue:function(e,t){var l=Ge();return df(l,_e.memoizedState,e,t)},useTransition:function(){var e=ku(al)[0],t=Ge().memoizedState;return[typeof e=="boolean"?e:Mn(e),t]},useSyncExternalStore:Lo,useId:vf,useHostTransitionStatus:Zc,useFormState:Io,useActionState:Io,useOptimistic:function(e,t){var l=Ge();return Zo(l,_e,e,t)},useMemoCache:Bc,useCacheRefresh:pf},Ly={readContext:tt,use:Xu,useCallback:of,useContext:tt,useEffect:nf,useImperativeHandle:sf,useInsertionEffect:uf,useLayoutEffect:cf,useMemo:ff,useReducer:Lc,useRef:lf,useState:function(){return Lc(al)},useDebugValue:kc,useDeferredValue:function(e,t){var l=Ge();return _e===null?Vc(l,e,t):df(l,_e.memoizedState,e,t)},useTransition:function(){var e=Lc(al)[0],t=Ge().memoizedState;return[typeof e=="boolean"?e:Mn(e),t]},useSyncExternalStore:Lo,useId:vf,useHostTransitionStatus:Zc,useFormState:tf,useActionState:tf,useOptimistic:function(e,t){var l=Ge();return _e!==null?Zo(l,_e,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:Bc,useCacheRefresh:pf},Ya=null,Un=0;function Ju(e){var t=Un;return Un+=1,Ya===null&&(Ya=[]),Mo(Ya,e,t)}function Hn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function $u(e,t){throw t.$$typeof===U?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Ef(e){var t=e._init;return t(e._payload)}function _f(e){function t(E,x){if(e){var _=E.deletions;_===null?(E.deletions=[x],E.flags|=16):_.push(x)}}function l(E,x){if(!e)return null;for(;x!==null;)t(E,x),x=x.sibling;return null}function a(E){for(var x=new Map;E!==null;)E.key!==null?x.set(E.key,E):x.set(E.index,E),E=E.sibling;return x}function n(E,x){return E=Pt(E,x),E.index=0,E.sibling=null,E}function u(E,x,_){return E.index=_,e?(_=E.alternate,_!==null?(_=_.index,_<x?(E.flags|=67108866,x):_):(E.flags|=67108866,x)):(E.flags|=1048576,x)}function r(E){return e&&E.alternate===null&&(E.flags|=67108866),E}function h(E,x,_,C){return x===null||x.tag!==6?(x=mc(_,E.mode,C),x.return=E,x):(x=n(x,_),x.return=E,x)}function p(E,x,_,C){var W=_.type;return W===D?M(E,x,_.props.children,C,_.key):x!==null&&(x.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===J&&Ef(W)===x.type)?(x=n(x,_.props),Hn(x,_),x.return=E,x):(x=Nu(_.type,_.key,_.props,null,E.mode,C),Hn(x,_),x.return=E,x)}function R(E,x,_,C){return x===null||x.tag!==4||x.stateNode.containerInfo!==_.containerInfo||x.stateNode.implementation!==_.implementation?(x=yc(_,E.mode,C),x.return=E,x):(x=n(x,_.children||[]),x.return=E,x)}function M(E,x,_,C,W){return x===null||x.tag!==7?(x=Il(_,E.mode,C,W),x.return=E,x):(x=n(x,_),x.return=E,x)}function H(E,x,_){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return x=mc(""+x,E.mode,_),x.return=E,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case O:return _=Nu(x.type,x.key,x.props,null,E.mode,_),Hn(_,x),_.return=E,_;case X:return x=yc(x,E.mode,_),x.return=E,x;case J:var C=x._init;return x=C(x._payload),H(E,x,_)}if(Ne(x)||qe(x))return x=Il(x,E.mode,_,null),x.return=E,x;if(typeof x.then=="function")return H(E,Ju(x),_);if(x.$$typeof===I)return H(E,Uu(E,x),_);$u(E,x)}return null}function z(E,x,_,C){var W=x!==null?x.key:null;if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return W!==null?null:h(E,x,""+_,C);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case O:return _.key===W?p(E,x,_,C):null;case X:return _.key===W?R(E,x,_,C):null;case J:return W=_._init,_=W(_._payload),z(E,x,_,C)}if(Ne(_)||qe(_))return W!==null?null:M(E,x,_,C,null);if(typeof _.then=="function")return z(E,x,Ju(_),C);if(_.$$typeof===I)return z(E,x,Uu(E,_),C);$u(E,_)}return null}function w(E,x,_,C,W){if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return E=E.get(_)||null,h(x,E,""+C,W);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case O:return E=E.get(C.key===null?_:C.key)||null,p(x,E,C,W);case X:return E=E.get(C.key===null?_:C.key)||null,R(x,E,C,W);case J:var fe=C._init;return C=fe(C._payload),w(E,x,_,C,W)}if(Ne(C)||qe(C))return E=E.get(_)||null,M(x,E,C,W,null);if(typeof C.then=="function")return w(E,x,_,Ju(C),W);if(C.$$typeof===I)return w(E,x,_,Uu(x,C),W);$u(x,C)}return null}function ue(E,x,_,C){for(var W=null,fe=null,ee=x,le=x=0,$e=null;ee!==null&&le<_.length;le++){ee.index>le?($e=ee,ee=null):$e=ee.sibling;var be=z(E,ee,_[le],C);if(be===null){ee===null&&(ee=$e);break}e&&ee&&be.alternate===null&&t(E,ee),x=u(be,x,le),fe===null?W=be:fe.sibling=be,fe=be,ee=$e}if(le===_.length)return l(E,ee),xe&&ta(E,le),W;if(ee===null){for(;le<_.length;le++)ee=H(E,_[le],C),ee!==null&&(x=u(ee,x,le),fe===null?W=ee:fe.sibling=ee,fe=ee);return xe&&ta(E,le),W}for(ee=a(ee);le<_.length;le++)$e=w(ee,E,le,_[le],C),$e!==null&&(e&&$e.alternate!==null&&ee.delete($e.key===null?le:$e.key),x=u($e,x,le),fe===null?W=$e:fe.sibling=$e,fe=$e);return e&&ee.forEach(function(Gl){return t(E,Gl)}),xe&&ta(E,le),W}function te(E,x,_,C){if(_==null)throw Error(c(151));for(var W=null,fe=null,ee=x,le=x=0,$e=null,be=_.next();ee!==null&&!be.done;le++,be=_.next()){ee.index>le?($e=ee,ee=null):$e=ee.sibling;var Gl=z(E,ee,be.value,C);if(Gl===null){ee===null&&(ee=$e);break}e&&ee&&Gl.alternate===null&&t(E,ee),x=u(Gl,x,le),fe===null?W=Gl:fe.sibling=Gl,fe=Gl,ee=$e}if(be.done)return l(E,ee),xe&&ta(E,le),W;if(ee===null){for(;!be.done;le++,be=_.next())be=H(E,be.value,C),be!==null&&(x=u(be,x,le),fe===null?W=be:fe.sibling=be,fe=be);return xe&&ta(E,le),W}for(ee=a(ee);!be.done;le++,be=_.next())be=w(ee,E,le,be.value,C),be!==null&&(e&&be.alternate!==null&&ee.delete(be.key===null?le:be.key),x=u(be,x,le),fe===null?W=be:fe.sibling=be,fe=be);return e&&ee.forEach(function(Gv){return t(E,Gv)}),xe&&ta(E,le),W}function Ae(E,x,_,C){if(typeof _=="object"&&_!==null&&_.type===D&&_.key===null&&(_=_.props.children),typeof _=="object"&&_!==null){switch(_.$$typeof){case O:e:{for(var W=_.key;x!==null;){if(x.key===W){if(W=_.type,W===D){if(x.tag===7){l(E,x.sibling),C=n(x,_.props.children),C.return=E,E=C;break e}}else if(x.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===J&&Ef(W)===x.type){l(E,x.sibling),C=n(x,_.props),Hn(C,_),C.return=E,E=C;break e}l(E,x);break}else t(E,x);x=x.sibling}_.type===D?(C=Il(_.props.children,E.mode,C,_.key),C.return=E,E=C):(C=Nu(_.type,_.key,_.props,null,E.mode,C),Hn(C,_),C.return=E,E=C)}return r(E);case X:e:{for(W=_.key;x!==null;){if(x.key===W)if(x.tag===4&&x.stateNode.containerInfo===_.containerInfo&&x.stateNode.implementation===_.implementation){l(E,x.sibling),C=n(x,_.children||[]),C.return=E,E=C;break e}else{l(E,x);break}else t(E,x);x=x.sibling}C=yc(_,E.mode,C),C.return=E,E=C}return r(E);case J:return W=_._init,_=W(_._payload),Ae(E,x,_,C)}if(Ne(_))return ue(E,x,_,C);if(qe(_)){if(W=qe(_),typeof W!="function")throw Error(c(150));return _=W.call(_),te(E,x,_,C)}if(typeof _.then=="function")return Ae(E,x,Ju(_),C);if(_.$$typeof===I)return Ae(E,x,Uu(E,_),C);$u(E,_)}return typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint"?(_=""+_,x!==null&&x.tag===6?(l(E,x.sibling),C=n(x,_),C.return=E,E=C):(l(E,x),C=mc(_,E.mode,C),C.return=E,E=C),r(E)):l(E,x)}return function(E,x,_,C){try{Un=0;var W=Ae(E,x,_,C);return Ya=null,W}catch(ee){if(ee===An||ee===qu)throw ee;var fe=jt(29,ee,null,E.mode);return fe.lanes=C,fe.return=E,fe}finally{}}}var La=_f(!0),Tf=_f(!1),Ct=q(null),Vt=null;function Al(e){var t=e.alternate;L(ke,ke.current&1),L(Ct,e),Vt===null&&(t===null||Ua.current!==null||t.memoizedState!==null)&&(Vt=e)}function Af(e){if(e.tag===22){if(L(ke,ke.current),L(Ct,e),Vt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Vt=e)}}else Rl()}function Rl(){L(ke,ke.current),L(Ct,Ct.current)}function nl(e){K(Ct),Vt===e&&(Vt=null),K(ke)}var ke=q(0);function Wu(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||qr(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Jc(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:j({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var $c={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=At(),n=El(a);n.payload=t,l!=null&&(n.callback=l),t=_l(e,n,a),t!==null&&(Rt(t,e,a),zn(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=At(),n=El(a);n.tag=1,n.payload=t,l!=null&&(n.callback=l),t=_l(e,n,a),t!==null&&(Rt(t,e,a),zn(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=At(),a=El(l);a.tag=2,t!=null&&(a.callback=t),t=_l(e,a,l),t!==null&&(Rt(t,e,l),zn(t,e,l))}};function Rf(e,t,l,a,n,u,r){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,u,r):t.prototype&&t.prototype.isPureReactComponent?!gn(l,a)||!gn(n,u):!0}function zf(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&$c.enqueueReplaceState(t,t.state,null)}function ra(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=j({},l));for(var n in e)l[n]===void 0&&(l[n]=e[n])}return l}var Fu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function wf(e){Fu(e)}function Of(e){console.error(e)}function Nf(e){Fu(e)}function Pu(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Mf(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Wc(e,t,l){return l=El(l),l.tag=3,l.payload={element:null},l.callback=function(){Pu(e,t)},l}function Df(e){return e=El(e),e.tag=3,e}function Cf(e,t,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;e.payload=function(){return n(u)},e.callback=function(){Mf(t,l,a)}}var r=l.stateNode;r!==null&&typeof r.componentDidCatch=="function"&&(e.callback=function(){Mf(t,l,a),typeof n!="function"&&(Dl===null?Dl=new Set([this]):Dl.add(this));var h=a.stack;this.componentDidCatch(a.value,{componentStack:h!==null?h:""})})}function Gy(e,t,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&En(t,l,n,!0),l=Ct.current,l!==null){switch(l.tag){case 13:return Vt===null?xr():l.alternate===null&&Ue===0&&(Ue=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Tc?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),jr(e,a,n)),!1;case 22:return l.flags|=65536,a===Tc?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),jr(e,a,n)),!1}throw Error(c(435,l.tag))}return jr(e,a,n),xr(),!1}if(xe)return t=Ct.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,a!==gc&&(e=Error(c(422),{cause:a}),jn(Ot(e,l)))):(a!==gc&&(t=Error(c(423),{cause:a}),jn(Ot(t,l))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=Ot(a,l),n=Wc(e.stateNode,a,n),zc(e,n),Ue!==4&&(Ue=2)),!1;var u=Error(c(520),{cause:a});if(u=Ot(u,l),kn===null?kn=[u]:kn.push(u),Ue!==4&&(Ue=2),t===null)return!0;a=Ot(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=n&-n,l.lanes|=e,e=Wc(l.stateNode,a,e),zc(l,e),!1;case 1:if(t=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Dl===null||!Dl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=Df(n),Cf(n,e,l,a),zc(l,n),!1}l=l.return}while(l!==null);return!1}var Uf=Error(c(461)),Ke=!1;function We(e,t,l,a){t.child=e===null?Tf(t,null,l,a):La(t,e.child,l,a)}function Hf(e,t,l,a,n){l=l.render;var u=t.ref;if("ref"in a){var r={};for(var h in a)h!=="ref"&&(r[h]=a[h])}else r=a;return ua(t),a=Dc(e,t,l,r,u,n),h=Cc(),e!==null&&!Ke?(Uc(e,t,n),ul(e,t,n)):(xe&&h&&vc(t),t.flags|=1,We(e,t,a,n),t.child)}function qf(e,t,l,a,n){if(e===null){var u=l.type;return typeof u=="function"&&!hc(u)&&u.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=u,Bf(e,t,u,a,n)):(e=Nu(l.type,null,a,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!nr(e,n)){var r=u.memoizedProps;if(l=l.compare,l=l!==null?l:gn,l(r,a)&&e.ref===t.ref)return ul(e,t,n)}return t.flags|=1,e=Pt(u,a),e.ref=t.ref,e.return=t,t.child=e}function Bf(e,t,l,a,n){if(e!==null){var u=e.memoizedProps;if(gn(u,a)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=a=u,nr(e,n))(e.flags&131072)!==0&&(Ke=!0);else return t.lanes=e.lanes,ul(e,t,n)}return Fc(e,t,l,a,n)}function Yf(e,t,l){var a=t.pendingProps,n=a.children,u=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,e!==null){for(n=t.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;t.childLanes=u&~a}else t.childLanes=0,t.child=null;return Lf(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Hu(t,u!==null?u.cachePool:null),u!==null?qo(t,u):Oc(),Af(t);else return t.lanes=t.childLanes=536870912,Lf(e,t,u!==null?u.baseLanes|l:l,l)}else u!==null?(Hu(t,u.cachePool),qo(t,u),Rl(),t.memoizedState=null):(e!==null&&Hu(t,null),Oc(),Rl());return We(e,t,n,l),t.child}function Lf(e,t,l,a){var n=_c();return n=n===null?null:{parent:Xe._currentValue,pool:n},t.memoizedState={baseLanes:l,cachePool:n},e!==null&&Hu(t,null),Oc(),Af(t),e!==null&&En(e,t,a,!0),null}function Iu(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(c(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function Fc(e,t,l,a,n){return ua(t),l=Dc(e,t,l,a,void 0,n),a=Cc(),e!==null&&!Ke?(Uc(e,t,n),ul(e,t,n)):(xe&&a&&vc(t),t.flags|=1,We(e,t,l,n),t.child)}function Gf(e,t,l,a,n,u){return ua(t),t.updateQueue=null,l=Yo(t,a,l,n),Bo(e),a=Cc(),e!==null&&!Ke?(Uc(e,t,u),ul(e,t,u)):(xe&&a&&vc(t),t.flags|=1,We(e,t,l,u),t.child)}function Xf(e,t,l,a,n){if(ua(t),t.stateNode===null){var u=Oa,r=l.contextType;typeof r=="object"&&r!==null&&(u=tt(r)),u=new l(a,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=$c,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=a,u.state=t.memoizedState,u.refs={},Ac(t),r=l.contextType,u.context=typeof r=="object"&&r!==null?tt(r):Oa,u.state=t.memoizedState,r=l.getDerivedStateFromProps,typeof r=="function"&&(Jc(t,l,r,a),u.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(r=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),r!==u.state&&$c.enqueueReplaceState(u,u.state,null),On(t,a,u,n),wn(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){u=t.stateNode;var h=t.memoizedProps,p=ra(l,h);u.props=p;var R=u.context,M=l.contextType;r=Oa,typeof M=="object"&&M!==null&&(r=tt(M));var H=l.getDerivedStateFromProps;M=typeof H=="function"||typeof u.getSnapshotBeforeUpdate=="function",h=t.pendingProps!==h,M||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(h||R!==r)&&zf(t,u,a,r),jl=!1;var z=t.memoizedState;u.state=z,On(t,a,u,n),wn(),R=t.memoizedState,h||z!==R||jl?(typeof H=="function"&&(Jc(t,l,H,a),R=t.memoizedState),(p=jl||Rf(t,l,p,a,z,R,r))?(M||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=R),u.props=a,u.state=R,u.context=r,a=p):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{u=t.stateNode,Rc(e,t),r=t.memoizedProps,M=ra(l,r),u.props=M,H=t.pendingProps,z=u.context,R=l.contextType,p=Oa,typeof R=="object"&&R!==null&&(p=tt(R)),h=l.getDerivedStateFromProps,(R=typeof h=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(r!==H||z!==p)&&zf(t,u,a,p),jl=!1,z=t.memoizedState,u.state=z,On(t,a,u,n),wn();var w=t.memoizedState;r!==H||z!==w||jl||e!==null&&e.dependencies!==null&&Cu(e.dependencies)?(typeof h=="function"&&(Jc(t,l,h,a),w=t.memoizedState),(M=jl||Rf(t,l,M,a,z,w,p)||e!==null&&e.dependencies!==null&&Cu(e.dependencies))?(R||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,w,p),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,w,p)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||r===e.memoizedProps&&z===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&z===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=w),u.props=a,u.state=w,u.context=p,a=M):(typeof u.componentDidUpdate!="function"||r===e.memoizedProps&&z===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&z===e.memoizedState||(t.flags|=1024),a=!1)}return u=a,Iu(e,t),a=(t.flags&128)!==0,u||a?(u=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&a?(t.child=La(t,e.child,null,n),t.child=La(t,null,l,n)):We(e,t,l,n),t.memoizedState=u.state,e=t.child):e=ul(e,t,n),e}function kf(e,t,l,a){return Sn(),t.flags|=256,We(e,t,l,a),t.child}var Pc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ic(e){return{baseLanes:e,cachePool:wo()}}function er(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=Ut),e}function Vf(e,t,l){var a=t.pendingProps,n=!1,u=(t.flags&128)!==0,r;if((r=u)||(r=e!==null&&e.memoizedState===null?!1:(ke.current&2)!==0),r&&(n=!0,t.flags&=-129),r=(t.flags&32)!==0,t.flags&=-33,e===null){if(xe){if(n?Al(t):Rl(),xe){var h=Ce,p;if(p=h){e:{for(p=h,h=kt;p.nodeType!==8;){if(!h){h=null;break e}if(p=Lt(p.nextSibling),p===null){h=null;break e}}h=p}h!==null?(t.memoizedState={dehydrated:h,treeContext:ea!==null?{id:It,overflow:el}:null,retryLane:536870912,hydrationErrors:null},p=jt(18,null,null,0),p.stateNode=h,p.return=t,t.child=p,ct=t,Ce=null,p=!0):p=!1}p||aa(t)}if(h=t.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return qr(h)?t.lanes=32:t.lanes=536870912,null;nl(t)}return h=a.children,a=a.fallback,n?(Rl(),n=t.mode,h=ei({mode:"hidden",children:h},n),a=Il(a,n,l,null),h.return=t,a.return=t,h.sibling=a,t.child=h,n=t.child,n.memoizedState=Ic(l),n.childLanes=er(e,r,l),t.memoizedState=Pc,a):(Al(t),tr(t,h))}if(p=e.memoizedState,p!==null&&(h=p.dehydrated,h!==null)){if(u)t.flags&256?(Al(t),t.flags&=-257,t=lr(e,t,l)):t.memoizedState!==null?(Rl(),t.child=e.child,t.flags|=128,t=null):(Rl(),n=a.fallback,h=t.mode,a=ei({mode:"visible",children:a.children},h),n=Il(n,h,l,null),n.flags|=2,a.return=t,n.return=t,a.sibling=n,t.child=a,La(t,e.child,null,l),a=t.child,a.memoizedState=Ic(l),a.childLanes=er(e,r,l),t.memoizedState=Pc,t=n);else if(Al(t),qr(h)){if(r=h.nextSibling&&h.nextSibling.dataset,r)var R=r.dgst;r=R,a=Error(c(419)),a.stack="",a.digest=r,jn({value:a,source:null,stack:null}),t=lr(e,t,l)}else if(Ke||En(e,t,l,!1),r=(l&e.childLanes)!==0,Ke||r){if(r=we,r!==null&&(a=l&-l,a=(a&42)!==0?1:Bi(a),a=(a&(r.suspendedLanes|l))!==0?0:a,a!==0&&a!==p.retryLane))throw p.retryLane=a,wa(e,a),Rt(r,e,a),Uf;h.data==="$?"||xr(),t=lr(e,t,l)}else h.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=p.treeContext,Ce=Lt(h.nextSibling),ct=t,xe=!0,la=null,kt=!1,e!==null&&(Mt[Dt++]=It,Mt[Dt++]=el,Mt[Dt++]=ea,It=e.id,el=e.overflow,ea=t),t=tr(t,a.children),t.flags|=4096);return t}return n?(Rl(),n=a.fallback,h=t.mode,p=e.child,R=p.sibling,a=Pt(p,{mode:"hidden",children:a.children}),a.subtreeFlags=p.subtreeFlags&65011712,R!==null?n=Pt(R,n):(n=Il(n,h,l,null),n.flags|=2),n.return=t,a.return=t,a.sibling=n,t.child=a,a=n,n=t.child,h=e.child.memoizedState,h===null?h=Ic(l):(p=h.cachePool,p!==null?(R=Xe._currentValue,p=p.parent!==R?{parent:R,pool:R}:p):p=wo(),h={baseLanes:h.baseLanes|l,cachePool:p}),n.memoizedState=h,n.childLanes=er(e,r,l),t.memoizedState=Pc,a):(Al(t),l=e.child,e=l.sibling,l=Pt(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=l,t.memoizedState=null,l)}function tr(e,t){return t=ei({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ei(e,t){return e=jt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function lr(e,t,l){return La(t,e.child,null,l),e=tr(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Qf(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),xc(e.return,t,l)}function ar(e,t,l,a,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function Zf(e,t,l){var a=t.pendingProps,n=a.revealOrder,u=a.tail;if(We(e,t,a.children,l),a=ke.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Qf(e,l,t);else if(e.tag===19)Qf(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(L(ke,a),n){case"forwards":for(l=t.child,n=null;l!==null;)e=l.alternate,e!==null&&Wu(e)===null&&(n=l),l=l.sibling;l=n,l===null?(n=t.child,t.child=null):(n=l.sibling,l.sibling=null),ar(t,!1,n,l,u);break;case"backwards":for(l=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Wu(e)===null){t.child=n;break}e=n.sibling,n.sibling=l,l=n,n=e}ar(t,!0,l,null,u);break;case"together":ar(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ul(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),Ml|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(En(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,l=Pt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Pt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function nr(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Cu(e)))}function Xy(e,t,l){switch(t.tag){case 3:Re(t,t.stateNode.containerInfo),Sl(t,Xe,e.memoizedState.cache),Sn();break;case 27:case 5:yl(t);break;case 4:Re(t,t.stateNode.containerInfo);break;case 10:Sl(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(Al(t),t.flags|=128,null):(l&t.child.childLanes)!==0?Vf(e,t,l):(Al(t),e=ul(e,t,l),e!==null?e.sibling:null);Al(t);break;case 19:var n=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(En(e,t,l,!1),a=(l&t.childLanes)!==0),n){if(a)return Zf(e,t,l);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),L(ke,ke.current),a)break;return null;case 22:case 23:return t.lanes=0,Yf(e,t,l);case 24:Sl(t,Xe,e.memoizedState.cache)}return ul(e,t,l)}function Kf(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ke=!0;else{if(!nr(e,l)&&(t.flags&128)===0)return Ke=!1,Xy(e,t,l);Ke=(e.flags&131072)!==0}else Ke=!1,xe&&(t.flags&1048576)!==0&&jo(t,Du,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,n=a._init;if(a=n(a._payload),t.type=a,typeof a=="function")hc(a)?(e=ra(a,e),t.tag=1,t=Xf(null,t,a,e,l)):(t.tag=0,t=Fc(null,t,a,e,l));else{if(a!=null){if(n=a.$$typeof,n===ce){t.tag=11,t=Hf(null,t,a,e,l);break e}else if(n===ye){t.tag=14,t=qf(null,t,a,e,l);break e}}throw t=qt(a)||a,Error(c(306,t,""))}}return t;case 0:return Fc(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,n=ra(a,t.pendingProps),Xf(e,t,a,n,l);case 3:e:{if(Re(t,t.stateNode.containerInfo),e===null)throw Error(c(387));a=t.pendingProps;var u=t.memoizedState;n=u.element,Rc(e,t),On(t,a,null,l);var r=t.memoizedState;if(a=r.cache,Sl(t,Xe,a),a!==u.cache&&Sc(t,[Xe],l,!0),wn(),a=r.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:r.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=kf(e,t,a,l);break e}else if(a!==n){n=Ot(Error(c(424)),t),jn(n),t=kf(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ce=Lt(e.firstChild),ct=t,xe=!0,la=null,kt=!0,l=Tf(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Sn(),a===n){t=ul(e,t,l);break e}We(e,t,a,l)}t=t.child}return t;case 26:return Iu(e,t),e===null?(l=Fd(t.type,null,t.pendingProps,null))?t.memoizedState=l:xe||(l=t.type,e=t.pendingProps,a=mi(ie.current).createElement(l),a[et]=t,a[rt]=e,Pe(a,l,e),Ze(a),t.stateNode=a):t.memoizedState=Fd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return yl(t),e===null&&xe&&(a=t.stateNode=Jd(t.type,t.pendingProps,ie.current),ct=t,kt=!0,n=Ce,Hl(t.type)?(Br=n,Ce=Lt(a.firstChild)):Ce=n),We(e,t,t.pendingProps.children,l),Iu(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&xe&&((n=a=Ce)&&(a=vv(a,t.type,t.pendingProps,kt),a!==null?(t.stateNode=a,ct=t,Ce=Lt(a.firstChild),kt=!1,n=!0):n=!1),n||aa(t)),yl(t),n=t.type,u=t.pendingProps,r=e!==null?e.memoizedProps:null,a=u.children,Cr(n,u)?a=null:r!==null&&Cr(n,r)&&(t.flags|=32),t.memoizedState!==null&&(n=Dc(e,t,Cy,null,null,l),Pn._currentValue=n),Iu(e,t),We(e,t,a,l),t.child;case 6:return e===null&&xe&&((e=l=Ce)&&(l=pv(l,t.pendingProps,kt),l!==null?(t.stateNode=l,ct=t,Ce=null,e=!0):e=!1),e||aa(t)),null;case 13:return Vf(e,t,l);case 4:return Re(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=La(t,null,a,l):We(e,t,a,l),t.child;case 11:return Hf(e,t,t.type,t.pendingProps,l);case 7:return We(e,t,t.pendingProps,l),t.child;case 8:return We(e,t,t.pendingProps.children,l),t.child;case 12:return We(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,Sl(t,t.type,a.value),We(e,t,a.children,l),t.child;case 9:return n=t.type._context,a=t.pendingProps.children,ua(t),n=tt(n),a=a(n),t.flags|=1,We(e,t,a,l),t.child;case 14:return qf(e,t,t.type,t.pendingProps,l);case 15:return Bf(e,t,t.type,t.pendingProps,l);case 19:return Zf(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=ei(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Pt(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return Yf(e,t,l);case 24:return ua(t),a=tt(Xe),e===null?(n=_c(),n===null&&(n=we,u=jc(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),t.memoizedState={parent:a,cache:n},Ac(t),Sl(t,Xe,n)):((e.lanes&l)!==0&&(Rc(e,t),On(t,null,null,l),wn()),n=e.memoizedState,u=t.memoizedState,n.parent!==a?(n={parent:a,cache:a},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),Sl(t,Xe,a)):(a=u.cache,Sl(t,Xe,a),a!==n.cache&&Sc(t,[Xe],l,!0))),We(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function il(e){e.flags|=4}function Jf(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!lh(t)){if(t=Ct.current,t!==null&&((pe&4194048)===pe?Vt!==null:(pe&62914560)!==pe&&(pe&536870912)===0||t!==Vt))throw Rn=Tc,Oo;e.flags|=8192}}function ti(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?_s():536870912,e.lanes|=t,Va|=t)}function qn(e,t){if(!xe)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function ky(e,t,l){var a=t.pendingProps;switch(pc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return Me(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),ll(Xe),gt(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(xn(t)?il(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,To())),Me(t),null;case 26:return l=t.memoizedState,e===null?(il(t),l!==null?(Me(t),Jf(t,l)):(Me(t),t.flags&=-16777217)):l?l!==e.memoizedState?(il(t),Me(t),Jf(t,l)):(Me(t),t.flags&=-16777217):(e.memoizedProps!==a&&il(t),Me(t),t.flags&=-16777217),null;case 27:vl(t),l=ie.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&il(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return Me(t),null}e=$.current,xn(t)?Eo(t):(e=Jd(n,a,l),t.stateNode=e,il(t))}return Me(t),null;case 5:if(vl(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&il(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return Me(t),null}if(e=$.current,xn(t))Eo(t);else{switch(n=mi(ie.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}e[et]=t,e[rt]=a;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Pe(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&il(t)}}return Me(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&il(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(c(166));if(e=ie.current,xn(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,n=ct,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[et]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||Gd(e.nodeValue,l)),e||aa(t)}else e=mi(e).createTextNode(a),e[et]=t,t.stateNode=e}return Me(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=xn(t),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(c(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(c(317));n[et]=t}else Sn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Me(t),n=!1}else n=To(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(nl(t),t):(nl(t),null)}if(nl(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),ti(t,t.updateQueue),Me(t),null;case 4:return gt(),e===null&&wr(t.stateNode.containerInfo),Me(t),null;case 10:return ll(t.type),Me(t),null;case 19:if(K(ke),n=t.memoizedState,n===null)return Me(t),null;if(a=(t.flags&128)!==0,u=n.rendering,u===null)if(a)qn(n,!1);else{if(Ue!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=Wu(e),u!==null){for(t.flags|=128,qn(n,!1),e=u.updateQueue,t.updateQueue=e,ti(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)So(l,e),l=l.sibling;return L(ke,ke.current&1|2),t.child}e=e.sibling}n.tail!==null&&Xt()>ni&&(t.flags|=128,a=!0,qn(n,!1),t.lanes=4194304)}else{if(!a)if(e=Wu(u),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,ti(t,e),qn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!xe)return Me(t),null}else 2*Xt()-n.renderingStartTime>ni&&l!==536870912&&(t.flags|=128,a=!0,qn(n,!1),t.lanes=4194304);n.isBackwards?(u.sibling=t.child,t.child=u):(e=n.last,e!==null?e.sibling=u:t.child=u,n.last=u)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=Xt(),t.sibling=null,e=ke.current,L(ke,a?e&1|2:e&1),t):(Me(t),null);case 22:case 23:return nl(t),Nc(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),l=t.updateQueue,l!==null&&ti(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&K(ia),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),ll(Xe),Me(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function Vy(e,t){switch(pc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ll(Xe),gt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return vl(t),null;case 13:if(nl(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));Sn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return K(ke),null;case 4:return gt(),null;case 10:return ll(t.type),null;case 22:case 23:return nl(t),Nc(),e!==null&&K(ia),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return ll(Xe),null;case 25:return null;default:return null}}function $f(e,t){switch(pc(t),t.tag){case 3:ll(Xe),gt();break;case 26:case 27:case 5:vl(t);break;case 4:gt();break;case 13:nl(t);break;case 19:K(ke);break;case 10:ll(t.type);break;case 22:case 23:nl(t),Nc(),e!==null&&K(ia);break;case 24:ll(Xe)}}function Bn(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&e)===e){a=void 0;var u=l.create,r=l.inst;a=u(),r.destroy=a}l=l.next}while(l!==n)}}catch(h){ze(t,t.return,h)}}function zl(e,t,l){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&e)===e){var r=a.inst,h=r.destroy;if(h!==void 0){r.destroy=void 0,n=t;var p=l,R=h;try{R()}catch(M){ze(n,p,M)}}}a=a.next}while(a!==u)}}catch(M){ze(t,t.return,M)}}function Wf(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{Ho(t,l)}catch(a){ze(e,e.return,a)}}}function Ff(e,t,l){l.props=ra(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){ze(e,t,a)}}function Yn(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(n){ze(e,t,n)}}function Qt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){ze(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){ze(e,t,n)}else l.current=null}function Pf(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){ze(e,e.return,n)}}function ur(e,t,l){try{var a=e.stateNode;fv(a,e.type,l,t),a[rt]=t}catch(n){ze(e,e.return,n)}}function If(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Hl(e.type)||e.tag===4}function ir(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||If(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Hl(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function cr(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=hi));else if(a!==4&&(a===27&&Hl(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(cr(e,t,l),e=e.sibling;e!==null;)cr(e,t,l),e=e.sibling}function li(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&Hl(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(li(e,t,l),e=e.sibling;e!==null;)li(e,t,l),e=e.sibling}function ed(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Pe(t,a,l),t[et]=e,t[rt]=l}catch(u){ze(e,e.return,u)}}var cl=!1,Ye=!1,rr=!1,td=typeof WeakSet=="function"?WeakSet:Set,Je=null;function Qy(e,t){if(e=e.containerInfo,Mr=xi,e=oo(e),ic(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break e}var r=0,h=-1,p=-1,R=0,M=0,H=e,z=null;t:for(;;){for(var w;H!==l||n!==0&&H.nodeType!==3||(h=r+n),H!==u||a!==0&&H.nodeType!==3||(p=r+a),H.nodeType===3&&(r+=H.nodeValue.length),(w=H.firstChild)!==null;)z=H,H=w;for(;;){if(H===e)break t;if(z===l&&++R===n&&(h=r),z===u&&++M===a&&(p=r),(w=H.nextSibling)!==null)break;H=z,z=H.parentNode}H=w}l=h===-1||p===-1?null:{start:h,end:p}}else l=null}l=l||{start:0,end:0}}else l=null;for(Dr={focusedElem:e,selectionRange:l},xi=!1,Je=t;Je!==null;)if(t=Je,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Je=e;else for(;Je!==null;){switch(t=Je,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,l=t,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var ue=ra(l.type,n,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(ue,u),a.__reactInternalSnapshotBeforeUpdate=e}catch(te){ze(l,l.return,te)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)Hr(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Hr(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,Je=e;break}Je=t.return}}function ld(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:wl(e,l),a&4&&Bn(5,l);break;case 1:if(wl(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(r){ze(l,l.return,r)}else{var n=ra(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(r){ze(l,l.return,r)}}a&64&&Wf(l),a&512&&Yn(l,l.return);break;case 3:if(wl(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{Ho(e,t)}catch(r){ze(l,l.return,r)}}break;case 27:t===null&&a&4&&ed(l);case 26:case 5:wl(e,l),t===null&&a&4&&Pf(l),a&512&&Yn(l,l.return);break;case 12:wl(e,l);break;case 13:wl(e,l),a&4&&ud(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=ev.bind(null,l),gv(e,l))));break;case 22:if(a=l.memoizedState!==null||cl,!a){t=t!==null&&t.memoizedState!==null||Ye,n=cl;var u=Ye;cl=a,(Ye=t)&&!u?Ol(e,l,(l.subtreeFlags&8772)!==0):wl(e,l),cl=n,Ye=u}break;case 30:break;default:wl(e,l)}}function ad(e){var t=e.alternate;t!==null&&(e.alternate=null,ad(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Gi(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Oe=null,ft=!1;function rl(e,t,l){for(l=l.child;l!==null;)nd(e,t,l),l=l.sibling}function nd(e,t,l){if(bt&&typeof bt.onCommitFiberUnmount=="function")try{bt.onCommitFiberUnmount(un,l)}catch{}switch(l.tag){case 26:Ye||Qt(l,t),rl(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Ye||Qt(l,t);var a=Oe,n=ft;Hl(l.type)&&(Oe=l.stateNode,ft=!1),rl(e,t,l),Jn(l.stateNode),Oe=a,ft=n;break;case 5:Ye||Qt(l,t);case 6:if(a=Oe,n=ft,Oe=null,rl(e,t,l),Oe=a,ft=n,Oe!==null)if(ft)try{(Oe.nodeType===9?Oe.body:Oe.nodeName==="HTML"?Oe.ownerDocument.body:Oe).removeChild(l.stateNode)}catch(u){ze(l,t,u)}else try{Oe.removeChild(l.stateNode)}catch(u){ze(l,t,u)}break;case 18:Oe!==null&&(ft?(e=Oe,Zd(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),lu(e)):Zd(Oe,l.stateNode));break;case 4:a=Oe,n=ft,Oe=l.stateNode.containerInfo,ft=!0,rl(e,t,l),Oe=a,ft=n;break;case 0:case 11:case 14:case 15:Ye||zl(2,l,t),Ye||zl(4,l,t),rl(e,t,l);break;case 1:Ye||(Qt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Ff(l,t,a)),rl(e,t,l);break;case 21:rl(e,t,l);break;case 22:Ye=(a=Ye)||l.memoizedState!==null,rl(e,t,l),Ye=a;break;default:rl(e,t,l)}}function ud(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{lu(e)}catch(l){ze(t,t.return,l)}}function Zy(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new td),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new td),t;default:throw Error(c(435,e.tag))}}function sr(e,t){var l=Zy(e);t.forEach(function(a){var n=tv.bind(null,e,a);l.has(a)||(l.add(a),a.then(n,n))})}function Et(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=e,r=t,h=r;e:for(;h!==null;){switch(h.tag){case 27:if(Hl(h.type)){Oe=h.stateNode,ft=!1;break e}break;case 5:Oe=h.stateNode,ft=!1;break e;case 3:case 4:Oe=h.stateNode.containerInfo,ft=!0;break e}h=h.return}if(Oe===null)throw Error(c(160));nd(u,r,n),Oe=null,ft=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)id(t,e),t=t.sibling}var Yt=null;function id(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Et(t,e),_t(e),a&4&&(zl(3,e,e.return),Bn(3,e),zl(5,e,e.return));break;case 1:Et(t,e),_t(e),a&512&&(Ye||l===null||Qt(l,l.return)),a&64&&cl&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Yt;if(Et(t,e),_t(e),a&512&&(Ye||l===null||Qt(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,n=n.ownerDocument||n;t:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[sn]||u[et]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),Pe(u,a,l),u[et]=e,Ze(u),a=u;break e;case"link":var r=eh("link","href",n).get(a+(l.href||""));if(r){for(var h=0;h<r.length;h++)if(u=r[h],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){r.splice(h,1);break t}}u=n.createElement(a),Pe(u,a,l),n.head.appendChild(u);break;case"meta":if(r=eh("meta","content",n).get(a+(l.content||""))){for(h=0;h<r.length;h++)if(u=r[h],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){r.splice(h,1);break t}}u=n.createElement(a),Pe(u,a,l),n.head.appendChild(u);break;default:throw Error(c(468,a))}u[et]=e,Ze(u),a=u}e.stateNode=a}else th(n,e.type,e.stateNode);else e.stateNode=Id(n,a,e.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?th(n,e.type,e.stateNode):Id(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&ur(e,e.memoizedProps,l.memoizedProps)}break;case 27:Et(t,e),_t(e),a&512&&(Ye||l===null||Qt(l,l.return)),l!==null&&a&4&&ur(e,e.memoizedProps,l.memoizedProps);break;case 5:if(Et(t,e),_t(e),a&512&&(Ye||l===null||Qt(l,l.return)),e.flags&32){n=e.stateNode;try{ja(n,"")}catch(w){ze(e,e.return,w)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,ur(e,n,l!==null?l.memoizedProps:n)),a&1024&&(rr=!0);break;case 6:if(Et(t,e),_t(e),a&4){if(e.stateNode===null)throw Error(c(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(w){ze(e,e.return,w)}}break;case 3:if(pi=null,n=Yt,Yt=yi(t.containerInfo),Et(t,e),Yt=n,_t(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{lu(t.containerInfo)}catch(w){ze(e,e.return,w)}rr&&(rr=!1,cd(e));break;case 4:a=Yt,Yt=yi(e.stateNode.containerInfo),Et(t,e),_t(e),Yt=a;break;case 12:Et(t,e),_t(e);break;case 13:Et(t,e),_t(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(yr=Xt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,sr(e,a)));break;case 22:n=e.memoizedState!==null;var p=l!==null&&l.memoizedState!==null,R=cl,M=Ye;if(cl=R||n,Ye=M||p,Et(t,e),Ye=M,cl=R,_t(e),a&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(l===null||p||cl||Ye||sa(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){p=l=t;try{if(u=p.stateNode,n)r=u.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none";else{h=p.stateNode;var H=p.memoizedProps.style,z=H!=null&&H.hasOwnProperty("display")?H.display:null;h.style.display=z==null||typeof z=="boolean"?"":(""+z).trim()}}catch(w){ze(p,p.return,w)}}}else if(t.tag===6){if(l===null){p=t;try{p.stateNode.nodeValue=n?"":p.memoizedProps}catch(w){ze(p,p.return,w)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,sr(e,l))));break;case 19:Et(t,e),_t(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,sr(e,a)));break;case 30:break;case 21:break;default:Et(t,e),_t(e)}}function _t(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(If(a)){l=a;break}a=a.return}if(l==null)throw Error(c(160));switch(l.tag){case 27:var n=l.stateNode,u=ir(e);li(e,u,n);break;case 5:var r=l.stateNode;l.flags&32&&(ja(r,""),l.flags&=-33);var h=ir(e);li(e,h,r);break;case 3:case 4:var p=l.stateNode.containerInfo,R=ir(e);cr(e,R,p);break;default:throw Error(c(161))}}catch(M){ze(e,e.return,M)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function cd(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;cd(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function wl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)ld(e,t.alternate,t),t=t.sibling}function sa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:zl(4,t,t.return),sa(t);break;case 1:Qt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&Ff(t,t.return,l),sa(t);break;case 27:Jn(t.stateNode);case 26:case 5:Qt(t,t.return),sa(t);break;case 22:t.memoizedState===null&&sa(t);break;case 30:sa(t);break;default:sa(t)}e=e.sibling}}function Ol(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,n=e,u=t,r=u.flags;switch(u.tag){case 0:case 11:case 15:Ol(n,u,l),Bn(4,u);break;case 1:if(Ol(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(R){ze(a,a.return,R)}if(a=u,n=a.updateQueue,n!==null){var h=a.stateNode;try{var p=n.shared.hiddenCallbacks;if(p!==null)for(n.shared.hiddenCallbacks=null,n=0;n<p.length;n++)Uo(p[n],h)}catch(R){ze(a,a.return,R)}}l&&r&64&&Wf(u),Yn(u,u.return);break;case 27:ed(u);case 26:case 5:Ol(n,u,l),l&&a===null&&r&4&&Pf(u),Yn(u,u.return);break;case 12:Ol(n,u,l);break;case 13:Ol(n,u,l),l&&r&4&&ud(n,u);break;case 22:u.memoizedState===null&&Ol(n,u,l),Yn(u,u.return);break;case 30:break;default:Ol(n,u,l)}t=t.sibling}}function or(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&_n(l))}function fr(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&_n(e))}function Zt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)rd(e,t,l,a),t=t.sibling}function rd(e,t,l,a){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Zt(e,t,l,a),n&2048&&Bn(9,t);break;case 1:Zt(e,t,l,a);break;case 3:Zt(e,t,l,a),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&_n(e)));break;case 12:if(n&2048){Zt(e,t,l,a),e=t.stateNode;try{var u=t.memoizedProps,r=u.id,h=u.onPostCommit;typeof h=="function"&&h(r,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(p){ze(t,t.return,p)}}else Zt(e,t,l,a);break;case 13:Zt(e,t,l,a);break;case 23:break;case 22:u=t.stateNode,r=t.alternate,t.memoizedState!==null?u._visibility&2?Zt(e,t,l,a):Ln(e,t):u._visibility&2?Zt(e,t,l,a):(u._visibility|=2,Ga(e,t,l,a,(t.subtreeFlags&10256)!==0)),n&2048&&or(r,t);break;case 24:Zt(e,t,l,a),n&2048&&fr(t.alternate,t);break;default:Zt(e,t,l,a)}}function Ga(e,t,l,a,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,r=t,h=l,p=a,R=r.flags;switch(r.tag){case 0:case 11:case 15:Ga(u,r,h,p,n),Bn(8,r);break;case 23:break;case 22:var M=r.stateNode;r.memoizedState!==null?M._visibility&2?Ga(u,r,h,p,n):Ln(u,r):(M._visibility|=2,Ga(u,r,h,p,n)),n&&R&2048&&or(r.alternate,r);break;case 24:Ga(u,r,h,p,n),n&&R&2048&&fr(r.alternate,r);break;default:Ga(u,r,h,p,n)}t=t.sibling}}function Ln(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,n=a.flags;switch(a.tag){case 22:Ln(l,a),n&2048&&or(a.alternate,a);break;case 24:Ln(l,a),n&2048&&fr(a.alternate,a);break;default:Ln(l,a)}t=t.sibling}}var Gn=8192;function Xa(e){if(e.subtreeFlags&Gn)for(e=e.child;e!==null;)sd(e),e=e.sibling}function sd(e){switch(e.tag){case 26:Xa(e),e.flags&Gn&&e.memoizedState!==null&&Nv(Yt,e.memoizedState,e.memoizedProps);break;case 5:Xa(e);break;case 3:case 4:var t=Yt;Yt=yi(e.stateNode.containerInfo),Xa(e),Yt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Gn,Gn=16777216,Xa(e),Gn=t):Xa(e));break;default:Xa(e)}}function od(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Xn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Je=a,dd(a,e)}od(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)fd(e),e=e.sibling}function fd(e){switch(e.tag){case 0:case 11:case 15:Xn(e),e.flags&2048&&zl(9,e,e.return);break;case 3:Xn(e);break;case 12:Xn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ai(e)):Xn(e);break;default:Xn(e)}}function ai(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Je=a,dd(a,e)}od(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:zl(8,t,t.return),ai(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,ai(t));break;default:ai(t)}e=e.sibling}}function dd(e,t){for(;Je!==null;){var l=Je;switch(l.tag){case 0:case 11:case 15:zl(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:_n(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Je=a;else e:for(l=e;Je!==null;){a=Je;var n=a.sibling,u=a.return;if(ad(a),a===l){Je=null;break e}if(n!==null){n.return=u,Je=n;break e}Je=u}}}var Ky={getCacheForType:function(e){var t=tt(Xe),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},Jy=typeof WeakMap=="function"?WeakMap:Map,je=0,we=null,he=null,pe=0,Ee=0,Tt=null,Nl=!1,ka=!1,dr=!1,sl=0,Ue=0,Ml=0,oa=0,hr=0,Ut=0,Va=0,kn=null,dt=null,mr=!1,yr=0,ni=1/0,ui=null,Dl=null,Fe=0,Cl=null,Qa=null,Za=0,vr=0,pr=null,hd=null,Vn=0,gr=null;function At(){if((je&2)!==0&&pe!==0)return pe&-pe;if(N.T!==null){var e=Da;return e!==0?e:Tr()}return Rs()}function md(){Ut===0&&(Ut=(pe&536870912)===0||xe?Es():536870912);var e=Ct.current;return e!==null&&(e.flags|=32),Ut}function Rt(e,t,l){(e===we&&(Ee===2||Ee===9)||e.cancelPendingCommit!==null)&&(Ka(e,0),Ul(e,pe,Ut,!1)),rn(e,l),((je&2)===0||e!==we)&&(e===we&&((je&2)===0&&(oa|=l),Ue===4&&Ul(e,pe,Ut,!1)),Kt(e))}function yd(e,t,l){if((je&6)!==0)throw Error(c(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||cn(e,t),n=a?Fy(e,t):Sr(e,t,!0),u=a;do{if(n===0){ka&&!a&&Ul(e,t,0,!1);break}else{if(l=e.current.alternate,u&&!$y(l)){n=Sr(e,t,!1),u=!1;continue}if(n===2){if(u=t,e.errorRecoveryDisabledLanes&u)var r=0;else r=e.pendingLanes&-536870913,r=r!==0?r:r&536870912?536870912:0;if(r!==0){t=r;e:{var h=e;n=kn;var p=h.current.memoizedState.isDehydrated;if(p&&(Ka(h,r).flags|=256),r=Sr(h,r,!1),r!==2){if(dr&&!p){h.errorRecoveryDisabledLanes|=u,oa|=u,n=4;break e}u=dt,dt=n,u!==null&&(dt===null?dt=u:dt.push.apply(dt,u))}n=r}if(u=!1,n!==2)continue}}if(n===1){Ka(e,0),Ul(e,t,0,!0);break}e:{switch(a=e,u=n,u){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Ul(a,t,Ut,!Nl);break e;case 2:dt=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(n=yr+300-Xt(),10<n)){if(Ul(a,t,Ut,!Nl),vu(a,0,!0)!==0)break e;a.timeoutHandle=Vd(vd.bind(null,a,l,dt,ui,mr,t,Ut,oa,Va,Nl,u,2,-0,0),n);break e}vd(a,l,dt,ui,mr,t,Ut,oa,Va,Nl,u,0,-0,0)}}break}while(!0);Kt(e)}function vd(e,t,l,a,n,u,r,h,p,R,M,H,z,w){if(e.timeoutHandle=-1,H=t.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(Fn={stylesheets:null,count:0,unsuspend:Ov},sd(t),H=Mv(),H!==null)){e.cancelPendingCommit=H(Ed.bind(null,e,t,u,l,a,n,r,h,p,M,1,z,w)),Ul(e,u,r,!R);return}Ed(e,t,u,l,a,n,r,h,p)}function $y(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!St(u(),n))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ul(e,t,l,a){t&=~hr,t&=~oa,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var n=t;0<n;){var u=31-xt(n),r=1<<u;a[u]=-1,n&=~r}l!==0&&Ts(e,l,t)}function ii(){return(je&6)===0?(Qn(0),!1):!0}function br(){if(he!==null){if(Ee===0)var e=he.return;else e=he,tl=na=null,Hc(e),Ya=null,Un=0,e=he;for(;e!==null;)$f(e.alternate,e),e=e.return;he=null}}function Ka(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,hv(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),br(),we=e,he=l=Pt(e.current,null),pe=t,Ee=0,Tt=null,Nl=!1,ka=cn(e,t),dr=!1,Va=Ut=hr=oa=Ml=Ue=0,dt=kn=null,mr=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var n=31-xt(a),u=1<<n;t|=e[n],a&=~u}return sl=t,zu(),l}function pd(e,t){se=null,N.H=Ku,t===An||t===qu?(t=Do(),Ee=3):t===Oo?(t=Do(),Ee=4):Ee=t===Uf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Tt=t,he===null&&(Ue=1,Pu(e,Ot(t,e.current)))}function gd(){var e=N.H;return N.H=Ku,e===null?Ku:e}function bd(){var e=N.A;return N.A=Ky,e}function xr(){Ue=4,Nl||(pe&4194048)!==pe&&Ct.current!==null||(ka=!0),(Ml&134217727)===0&&(oa&134217727)===0||we===null||Ul(we,pe,Ut,!1)}function Sr(e,t,l){var a=je;je|=2;var n=gd(),u=bd();(we!==e||pe!==t)&&(ui=null,Ka(e,t)),t=!1;var r=Ue;e:do try{if(Ee!==0&&he!==null){var h=he,p=Tt;switch(Ee){case 8:br(),r=6;break e;case 3:case 2:case 9:case 6:Ct.current===null&&(t=!0);var R=Ee;if(Ee=0,Tt=null,Ja(e,h,p,R),l&&ka){r=0;break e}break;default:R=Ee,Ee=0,Tt=null,Ja(e,h,p,R)}}Wy(),r=Ue;break}catch(M){pd(e,M)}while(!0);return t&&e.shellSuspendCounter++,tl=na=null,je=a,N.H=n,N.A=u,he===null&&(we=null,pe=0,zu()),r}function Wy(){for(;he!==null;)xd(he)}function Fy(e,t){var l=je;je|=2;var a=gd(),n=bd();we!==e||pe!==t?(ui=null,ni=Xt()+500,Ka(e,t)):ka=cn(e,t);e:do try{if(Ee!==0&&he!==null){t=he;var u=Tt;t:switch(Ee){case 1:Ee=0,Tt=null,Ja(e,t,u,1);break;case 2:case 9:if(No(u)){Ee=0,Tt=null,Sd(t);break}t=function(){Ee!==2&&Ee!==9||we!==e||(Ee=7),Kt(e)},u.then(t,t);break e;case 3:Ee=7;break e;case 4:Ee=5;break e;case 7:No(u)?(Ee=0,Tt=null,Sd(t)):(Ee=0,Tt=null,Ja(e,t,u,7));break;case 5:var r=null;switch(he.tag){case 26:r=he.memoizedState;case 5:case 27:var h=he;if(!r||lh(r)){Ee=0,Tt=null;var p=h.sibling;if(p!==null)he=p;else{var R=h.return;R!==null?(he=R,ci(R)):he=null}break t}}Ee=0,Tt=null,Ja(e,t,u,5);break;case 6:Ee=0,Tt=null,Ja(e,t,u,6);break;case 8:br(),Ue=6;break e;default:throw Error(c(462))}}Py();break}catch(M){pd(e,M)}while(!0);return tl=na=null,N.H=a,N.A=n,je=l,he!==null?0:(we=null,pe=0,zu(),Ue)}function Py(){for(;he!==null&&!xm();)xd(he)}function xd(e){var t=Kf(e.alternate,e,sl);e.memoizedProps=e.pendingProps,t===null?ci(e):he=t}function Sd(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=Gf(l,t,t.pendingProps,t.type,void 0,pe);break;case 11:t=Gf(l,t,t.pendingProps,t.type.render,t.ref,pe);break;case 5:Hc(t);default:$f(l,t),t=he=So(t,sl),t=Kf(l,t,sl)}e.memoizedProps=e.pendingProps,t===null?ci(e):he=t}function Ja(e,t,l,a){tl=na=null,Hc(t),Ya=null,Un=0;var n=t.return;try{if(Gy(e,n,t,l,pe)){Ue=1,Pu(e,Ot(l,e.current)),he=null;return}}catch(u){if(n!==null)throw he=n,u;Ue=1,Pu(e,Ot(l,e.current)),he=null;return}t.flags&32768?(xe||a===1?e=!0:ka||(pe&536870912)!==0?e=!1:(Nl=e=!0,(a===2||a===9||a===3||a===6)&&(a=Ct.current,a!==null&&a.tag===13&&(a.flags|=16384))),jd(t,e)):ci(t)}function ci(e){var t=e;do{if((t.flags&32768)!==0){jd(t,Nl);return}e=t.return;var l=ky(t.alternate,t,sl);if(l!==null){he=l;return}if(t=t.sibling,t!==null){he=t;return}he=t=e}while(t!==null);Ue===0&&(Ue=5)}function jd(e,t){do{var l=Vy(e.alternate,e);if(l!==null){l.flags&=32767,he=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){he=e;return}he=e=l}while(e!==null);Ue=6,he=null}function Ed(e,t,l,a,n,u,r,h,p){e.cancelPendingCommit=null;do ri();while(Fe!==0);if((je&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(u=t.lanes|t.childLanes,u|=fc,Om(e,l,u,r,h,p),e===we&&(he=we=null,pe=0),Qa=t,Cl=e,Za=l,vr=u,pr=n,hd=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,lv(hu,function(){return zd(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=N.T,N.T=null,n=k.p,k.p=2,r=je,je|=4;try{Qy(e,t,l)}finally{je=r,k.p=n,N.T=a}}Fe=1,_d(),Td(),Ad()}}function _d(){if(Fe===1){Fe=0;var e=Cl,t=Qa,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=N.T,N.T=null;var a=k.p;k.p=2;var n=je;je|=4;try{id(t,e);var u=Dr,r=oo(e.containerInfo),h=u.focusedElem,p=u.selectionRange;if(r!==h&&h&&h.ownerDocument&&so(h.ownerDocument.documentElement,h)){if(p!==null&&ic(h)){var R=p.start,M=p.end;if(M===void 0&&(M=R),"selectionStart"in h)h.selectionStart=R,h.selectionEnd=Math.min(M,h.value.length);else{var H=h.ownerDocument||document,z=H&&H.defaultView||window;if(z.getSelection){var w=z.getSelection(),ue=h.textContent.length,te=Math.min(p.start,ue),Ae=p.end===void 0?te:Math.min(p.end,ue);!w.extend&&te>Ae&&(r=Ae,Ae=te,te=r);var E=ro(h,te),x=ro(h,Ae);if(E&&x&&(w.rangeCount!==1||w.anchorNode!==E.node||w.anchorOffset!==E.offset||w.focusNode!==x.node||w.focusOffset!==x.offset)){var _=H.createRange();_.setStart(E.node,E.offset),w.removeAllRanges(),te>Ae?(w.addRange(_),w.extend(x.node,x.offset)):(_.setEnd(x.node,x.offset),w.addRange(_))}}}}for(H=[],w=h;w=w.parentNode;)w.nodeType===1&&H.push({element:w,left:w.scrollLeft,top:w.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<H.length;h++){var C=H[h];C.element.scrollLeft=C.left,C.element.scrollTop=C.top}}xi=!!Mr,Dr=Mr=null}finally{je=n,k.p=a,N.T=l}}e.current=t,Fe=2}}function Td(){if(Fe===2){Fe=0;var e=Cl,t=Qa,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=N.T,N.T=null;var a=k.p;k.p=2;var n=je;je|=4;try{ld(e,t.alternate,t)}finally{je=n,k.p=a,N.T=l}}Fe=3}}function Ad(){if(Fe===4||Fe===3){Fe=0,Sm();var e=Cl,t=Qa,l=Za,a=hd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Fe=5:(Fe=0,Qa=Cl=null,Rd(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(Dl=null),Yi(l),t=t.stateNode,bt&&typeof bt.onCommitFiberRoot=="function")try{bt.onCommitFiberRoot(un,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=N.T,n=k.p,k.p=2,N.T=null;try{for(var u=e.onRecoverableError,r=0;r<a.length;r++){var h=a[r];u(h.value,{componentStack:h.stack})}}finally{N.T=t,k.p=n}}(Za&3)!==0&&ri(),Kt(e),n=e.pendingLanes,(l&4194090)!==0&&(n&42)!==0?e===gr?Vn++:(Vn=0,gr=e):Vn=0,Qn(0)}}function Rd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,_n(t)))}function ri(e){return _d(),Td(),Ad(),zd()}function zd(){if(Fe!==5)return!1;var e=Cl,t=vr;vr=0;var l=Yi(Za),a=N.T,n=k.p;try{k.p=32>l?32:l,N.T=null,l=pr,pr=null;var u=Cl,r=Za;if(Fe=0,Qa=Cl=null,Za=0,(je&6)!==0)throw Error(c(331));var h=je;if(je|=4,fd(u.current),rd(u,u.current,r,l),je=h,Qn(0,!1),bt&&typeof bt.onPostCommitFiberRoot=="function")try{bt.onPostCommitFiberRoot(un,u)}catch{}return!0}finally{k.p=n,N.T=a,Rd(e,t)}}function wd(e,t,l){t=Ot(l,t),t=Wc(e.stateNode,t,2),e=_l(e,t,2),e!==null&&(rn(e,2),Kt(e))}function ze(e,t,l){if(e.tag===3)wd(e,e,l);else for(;t!==null;){if(t.tag===3){wd(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Dl===null||!Dl.has(a))){e=Ot(l,e),l=Df(2),a=_l(t,l,2),a!==null&&(Cf(l,a,t,e),rn(a,2),Kt(a));break}}t=t.return}}function jr(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new Jy;var n=new Set;a.set(t,n)}else n=a.get(t),n===void 0&&(n=new Set,a.set(t,n));n.has(l)||(dr=!0,n.add(l),e=Iy.bind(null,e,t,l),t.then(e,e))}function Iy(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,we===e&&(pe&l)===l&&(Ue===4||Ue===3&&(pe&62914560)===pe&&300>Xt()-yr?(je&2)===0&&Ka(e,0):hr|=l,Va===pe&&(Va=0)),Kt(e)}function Od(e,t){t===0&&(t=_s()),e=wa(e,t),e!==null&&(rn(e,t),Kt(e))}function ev(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),Od(e,l)}function tv(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(c(314))}a!==null&&a.delete(t),Od(e,l)}function lv(e,t){return Ui(e,t)}var si=null,$a=null,Er=!1,oi=!1,_r=!1,fa=0;function Kt(e){e!==$a&&e.next===null&&($a===null?si=$a=e:$a=$a.next=e),oi=!0,Er||(Er=!0,nv())}function Qn(e,t){if(!_r&&oi){_r=!0;do for(var l=!1,a=si;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var r=a.suspendedLanes,h=a.pingedLanes;u=(1<<31-xt(42|e)+1)-1,u&=n&~(r&~h),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,Cd(a,u))}else u=pe,u=vu(a,a===we?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||cn(a,u)||(l=!0,Cd(a,u));a=a.next}while(l);_r=!1}}function av(){Nd()}function Nd(){oi=Er=!1;var e=0;fa!==0&&(dv()&&(e=fa),fa=0);for(var t=Xt(),l=null,a=si;a!==null;){var n=a.next,u=Md(a,t);u===0?(a.next=null,l===null?si=n:l.next=n,n===null&&($a=l)):(l=a,(e!==0||(u&3)!==0)&&(oi=!0)),a=n}Qn(e)}function Md(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var r=31-xt(u),h=1<<r,p=n[r];p===-1?((h&l)===0||(h&a)!==0)&&(n[r]=wm(h,t)):p<=t&&(e.expiredLanes|=h),u&=~h}if(t=we,l=pe,l=vu(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(Ee===2||Ee===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&Hi(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||cn(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&Hi(a),Yi(l)){case 2:case 8:l=Ss;break;case 32:l=hu;break;case 268435456:l=js;break;default:l=hu}return a=Dd.bind(null,e),l=Ui(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&Hi(a),e.callbackPriority=2,e.callbackNode=null,2}function Dd(e,t){if(Fe!==0&&Fe!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(ri()&&e.callbackNode!==l)return null;var a=pe;return a=vu(e,e===we?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(yd(e,a,t),Md(e,Xt()),e.callbackNode!=null&&e.callbackNode===l?Dd.bind(null,e):null)}function Cd(e,t){if(ri())return null;yd(e,t,!0)}function nv(){mv(function(){(je&6)!==0?Ui(xs,av):Nd()})}function Tr(){return fa===0&&(fa=Es()),fa}function Ud(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Su(""+e)}function Hd(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function uv(e,t,l,a,n){if(t==="submit"&&l&&l.stateNode===n){var u=Ud((n[rt]||null).action),r=a.submitter;r&&(t=(t=r[rt]||null)?Ud(t.formAction):r.getAttribute("formAction"),t!==null&&(u=t,r=null));var h=new Tu("action","action",null,a,n);e.push({event:h,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(fa!==0){var p=r?Hd(n,r):new FormData(n);Qc(l,{pending:!0,data:p,method:n.method,action:u},null,p)}}else typeof u=="function"&&(h.preventDefault(),p=r?Hd(n,r):new FormData(n),Qc(l,{pending:!0,data:p,method:n.method,action:u},u,p))},currentTarget:n}]})}}for(var Ar=0;Ar<oc.length;Ar++){var Rr=oc[Ar],iv=Rr.toLowerCase(),cv=Rr[0].toUpperCase()+Rr.slice(1);Bt(iv,"on"+cv)}Bt(mo,"onAnimationEnd"),Bt(yo,"onAnimationIteration"),Bt(vo,"onAnimationStart"),Bt("dblclick","onDoubleClick"),Bt("focusin","onFocus"),Bt("focusout","onBlur"),Bt(_y,"onTransitionRun"),Bt(Ty,"onTransitionStart"),Bt(Ay,"onTransitionCancel"),Bt(po,"onTransitionEnd"),ba("onMouseEnter",["mouseout","mouseover"]),ba("onMouseLeave",["mouseout","mouseover"]),ba("onPointerEnter",["pointerout","pointerover"]),ba("onPointerLeave",["pointerout","pointerover"]),$l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),$l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),$l("onBeforeInput",["compositionend","keypress","textInput","paste"]),$l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),$l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),$l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),rv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Zn));function qd(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],n=a.event;a=a.listeners;e:{var u=void 0;if(t)for(var r=a.length-1;0<=r;r--){var h=a[r],p=h.instance,R=h.currentTarget;if(h=h.listener,p!==u&&n.isPropagationStopped())break e;u=h,n.currentTarget=R;try{u(n)}catch(M){Fu(M)}n.currentTarget=null,u=p}else for(r=0;r<a.length;r++){if(h=a[r],p=h.instance,R=h.currentTarget,h=h.listener,p!==u&&n.isPropagationStopped())break e;u=h,n.currentTarget=R;try{u(n)}catch(M){Fu(M)}n.currentTarget=null,u=p}}}}function me(e,t){var l=t[Li];l===void 0&&(l=t[Li]=new Set);var a=e+"__bubble";l.has(a)||(Bd(t,e,2,!1),l.add(a))}function zr(e,t,l){var a=0;t&&(a|=4),Bd(l,e,a,t)}var fi="_reactListening"+Math.random().toString(36).slice(2);function wr(e){if(!e[fi]){e[fi]=!0,ws.forEach(function(l){l!=="selectionchange"&&(rv.has(l)||zr(l,!1,e),zr(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[fi]||(t[fi]=!0,zr("selectionchange",!1,t))}}function Bd(e,t,l,a){switch(rh(t)){case 2:var n=Uv;break;case 8:n=Hv;break;default:n=kr}l=n.bind(null,t,l,e),n=void 0,!Fi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(t,l,{capture:!0,passive:n}):e.addEventListener(t,l,!0):n!==void 0?e.addEventListener(t,l,{passive:n}):e.addEventListener(t,l,!1)}function Or(e,t,l,a,n){var u=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var r=a.tag;if(r===3||r===4){var h=a.stateNode.containerInfo;if(h===n)break;if(r===4)for(r=a.return;r!==null;){var p=r.tag;if((p===3||p===4)&&r.stateNode.containerInfo===n)return;r=r.return}for(;h!==null;){if(r=va(h),r===null)return;if(p=r.tag,p===5||p===6||p===26||p===27){a=u=r;continue e}h=h.parentNode}}a=a.return}ks(function(){var R=u,M=$i(l),H=[];e:{var z=go.get(e);if(z!==void 0){var w=Tu,ue=e;switch(e){case"keypress":if(Eu(l)===0)break e;case"keydown":case"keyup":w=ly;break;case"focusin":ue="focus",w=tc;break;case"focusout":ue="blur",w=tc;break;case"beforeblur":case"afterblur":w=tc;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Zs;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=Vm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=uy;break;case mo:case yo:case vo:w=Km;break;case po:w=cy;break;case"scroll":case"scrollend":w=Xm;break;case"wheel":w=sy;break;case"copy":case"cut":case"paste":w=$m;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Js;break;case"toggle":case"beforetoggle":w=fy}var te=(t&4)!==0,Ae=!te&&(e==="scroll"||e==="scrollend"),E=te?z!==null?z+"Capture":null:z;te=[];for(var x=R,_;x!==null;){var C=x;if(_=C.stateNode,C=C.tag,C!==5&&C!==26&&C!==27||_===null||E===null||(C=fn(x,E),C!=null&&te.push(Kn(x,C,_))),Ae)break;x=x.return}0<te.length&&(z=new w(z,ue,null,l,M),H.push({event:z,listeners:te}))}}if((t&7)===0){e:{if(z=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",z&&l!==Ji&&(ue=l.relatedTarget||l.fromElement)&&(va(ue)||ue[ya]))break e;if((w||z)&&(z=M.window===M?M:(z=M.ownerDocument)?z.defaultView||z.parentWindow:window,w?(ue=l.relatedTarget||l.toElement,w=R,ue=ue?va(ue):null,ue!==null&&(Ae=m(ue),te=ue.tag,ue!==Ae||te!==5&&te!==27&&te!==6)&&(ue=null)):(w=null,ue=R),w!==ue)){if(te=Zs,C="onMouseLeave",E="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(te=Js,C="onPointerLeave",E="onPointerEnter",x="pointer"),Ae=w==null?z:on(w),_=ue==null?z:on(ue),z=new te(C,x+"leave",w,l,M),z.target=Ae,z.relatedTarget=_,C=null,va(M)===R&&(te=new te(E,x+"enter",ue,l,M),te.target=_,te.relatedTarget=Ae,C=te),Ae=C,w&&ue)t:{for(te=w,E=ue,x=0,_=te;_;_=Wa(_))x++;for(_=0,C=E;C;C=Wa(C))_++;for(;0<x-_;)te=Wa(te),x--;for(;0<_-x;)E=Wa(E),_--;for(;x--;){if(te===E||E!==null&&te===E.alternate)break t;te=Wa(te),E=Wa(E)}te=null}else te=null;w!==null&&Yd(H,z,w,te,!1),ue!==null&&Ae!==null&&Yd(H,Ae,ue,te,!0)}}e:{if(z=R?on(R):window,w=z.nodeName&&z.nodeName.toLowerCase(),w==="select"||w==="input"&&z.type==="file")var W=lo;else if(eo(z))if(ao)W=Sy;else{W=by;var fe=gy}else w=z.nodeName,!w||w.toLowerCase()!=="input"||z.type!=="checkbox"&&z.type!=="radio"?R&&Ki(R.elementType)&&(W=lo):W=xy;if(W&&(W=W(e,R))){to(H,W,l,M);break e}fe&&fe(e,z,R),e==="focusout"&&R&&z.type==="number"&&R.memoizedProps.value!=null&&Zi(z,"number",z.value)}switch(fe=R?on(R):window,e){case"focusin":(eo(fe)||fe.contentEditable==="true")&&(Aa=fe,cc=R,bn=null);break;case"focusout":bn=cc=Aa=null;break;case"mousedown":rc=!0;break;case"contextmenu":case"mouseup":case"dragend":rc=!1,fo(H,l,M);break;case"selectionchange":if(Ey)break;case"keydown":case"keyup":fo(H,l,M)}var ee;if(ac)e:{switch(e){case"compositionstart":var le="onCompositionStart";break e;case"compositionend":le="onCompositionEnd";break e;case"compositionupdate":le="onCompositionUpdate";break e}le=void 0}else Ta?Ps(e,l)&&(le="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(le="onCompositionStart");le&&($s&&l.locale!=="ko"&&(Ta||le!=="onCompositionStart"?le==="onCompositionEnd"&&Ta&&(ee=Vs()):(xl=M,Pi="value"in xl?xl.value:xl.textContent,Ta=!0)),fe=di(R,le),0<fe.length&&(le=new Ks(le,e,null,l,M),H.push({event:le,listeners:fe}),ee?le.data=ee:(ee=Is(l),ee!==null&&(le.data=ee)))),(ee=hy?my(e,l):yy(e,l))&&(le=di(R,"onBeforeInput"),0<le.length&&(fe=new Ks("onBeforeInput","beforeinput",null,l,M),H.push({event:fe,listeners:le}),fe.data=ee)),uv(H,e,R,l,M)}qd(H,t)})}function Kn(e,t,l){return{instance:e,listener:t,currentTarget:l}}function di(e,t){for(var l=t+"Capture",a=[];e!==null;){var n=e,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=fn(e,l),n!=null&&a.unshift(Kn(e,n,u)),n=fn(e,t),n!=null&&a.push(Kn(e,n,u))),e.tag===3)return a;e=e.return}return[]}function Wa(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Yd(e,t,l,a,n){for(var u=t._reactName,r=[];l!==null&&l!==a;){var h=l,p=h.alternate,R=h.stateNode;if(h=h.tag,p!==null&&p===a)break;h!==5&&h!==26&&h!==27||R===null||(p=R,n?(R=fn(l,u),R!=null&&r.unshift(Kn(l,R,p))):n||(R=fn(l,u),R!=null&&r.push(Kn(l,R,p)))),l=l.return}r.length!==0&&e.push({event:t,listeners:r})}var sv=/\r\n?/g,ov=/\u0000|\uFFFD/g;function Ld(e){return(typeof e=="string"?e:""+e).replace(sv,`
`).replace(ov,"")}function Gd(e,t){return t=Ld(t),Ld(e)===t}function hi(){}function Te(e,t,l,a,n,u){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||ja(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&ja(e,""+a);break;case"className":gu(e,"class",a);break;case"tabIndex":gu(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":gu(e,l,a);break;case"style":Gs(e,a,u);break;case"data":if(t!=="object"){gu(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Su(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(t!=="input"&&Te(e,t,"name",n.name,n,null),Te(e,t,"formEncType",n.formEncType,n,null),Te(e,t,"formMethod",n.formMethod,n,null),Te(e,t,"formTarget",n.formTarget,n,null)):(Te(e,t,"encType",n.encType,n,null),Te(e,t,"method",n.method,n,null),Te(e,t,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Su(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=hi);break;case"onScroll":a!=null&&me("scroll",e);break;case"onScrollEnd":a!=null&&me("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=Su(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":me("beforetoggle",e),me("toggle",e),pu(e,"popover",a);break;case"xlinkActuate":Wt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Wt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Wt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Wt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Wt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Wt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Wt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Wt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Wt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":pu(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Lm.get(l)||l,pu(e,l,a))}}function Nr(e,t,l,a,n,u){switch(l){case"style":Gs(e,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"children":typeof a=="string"?ja(e,a):(typeof a=="number"||typeof a=="bigint")&&ja(e,""+a);break;case"onScroll":a!=null&&me("scroll",e);break;case"onScrollEnd":a!=null&&me("scrollend",e);break;case"onClick":a!=null&&(e.onclick=hi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Os.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),t=l.slice(2,n?l.length-7:void 0),u=e[rt]||null,u=u!=null?u[l]:null,typeof u=="function"&&e.removeEventListener(t,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,n);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):pu(e,l,a)}}}function Pe(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":me("error",e),me("load",e);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var r=l[u];if(r!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Te(e,t,u,r,l,null)}}n&&Te(e,t,"srcSet",l.srcSet,l,null),a&&Te(e,t,"src",l.src,l,null);return;case"input":me("invalid",e);var h=u=r=n=null,p=null,R=null;for(a in l)if(l.hasOwnProperty(a)){var M=l[a];if(M!=null)switch(a){case"name":n=M;break;case"type":r=M;break;case"checked":p=M;break;case"defaultChecked":R=M;break;case"value":u=M;break;case"defaultValue":h=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(c(137,t));break;default:Te(e,t,a,M,l,null)}}qs(e,u,h,p,R,r,n,!1),bu(e);return;case"select":me("invalid",e),a=r=u=null;for(n in l)if(l.hasOwnProperty(n)&&(h=l[n],h!=null))switch(n){case"value":u=h;break;case"defaultValue":r=h;break;case"multiple":a=h;default:Te(e,t,n,h,l,null)}t=u,l=r,e.multiple=!!a,t!=null?Sa(e,!!a,t,!1):l!=null&&Sa(e,!!a,l,!0);return;case"textarea":me("invalid",e),u=n=a=null;for(r in l)if(l.hasOwnProperty(r)&&(h=l[r],h!=null))switch(r){case"value":a=h;break;case"defaultValue":n=h;break;case"children":u=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(c(91));break;default:Te(e,t,r,h,l,null)}Ys(e,a,n,u),bu(e);return;case"option":for(p in l)if(l.hasOwnProperty(p)&&(a=l[p],a!=null))switch(p){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Te(e,t,p,a,l,null)}return;case"dialog":me("beforetoggle",e),me("toggle",e),me("cancel",e),me("close",e);break;case"iframe":case"object":me("load",e);break;case"video":case"audio":for(a=0;a<Zn.length;a++)me(Zn[a],e);break;case"image":me("error",e),me("load",e);break;case"details":me("toggle",e);break;case"embed":case"source":case"link":me("error",e),me("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(R in l)if(l.hasOwnProperty(R)&&(a=l[R],a!=null))switch(R){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Te(e,t,R,a,l,null)}return;default:if(Ki(t)){for(M in l)l.hasOwnProperty(M)&&(a=l[M],a!==void 0&&Nr(e,t,M,a,l,void 0));return}}for(h in l)l.hasOwnProperty(h)&&(a=l[h],a!=null&&Te(e,t,h,a,l,null))}function fv(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,r=null,h=null,p=null,R=null,M=null;for(w in l){var H=l[w];if(l.hasOwnProperty(w)&&H!=null)switch(w){case"checked":break;case"value":break;case"defaultValue":p=H;default:a.hasOwnProperty(w)||Te(e,t,w,null,a,H)}}for(var z in a){var w=a[z];if(H=l[z],a.hasOwnProperty(z)&&(w!=null||H!=null))switch(z){case"type":u=w;break;case"name":n=w;break;case"checked":R=w;break;case"defaultChecked":M=w;break;case"value":r=w;break;case"defaultValue":h=w;break;case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(c(137,t));break;default:w!==H&&Te(e,t,z,w,a,H)}}Qi(e,r,h,p,R,M,u,n);return;case"select":w=r=h=z=null;for(u in l)if(p=l[u],l.hasOwnProperty(u)&&p!=null)switch(u){case"value":break;case"multiple":w=p;default:a.hasOwnProperty(u)||Te(e,t,u,null,a,p)}for(n in a)if(u=a[n],p=l[n],a.hasOwnProperty(n)&&(u!=null||p!=null))switch(n){case"value":z=u;break;case"defaultValue":h=u;break;case"multiple":r=u;default:u!==p&&Te(e,t,n,u,a,p)}t=h,l=r,a=w,z!=null?Sa(e,!!l,z,!1):!!a!=!!l&&(t!=null?Sa(e,!!l,t,!0):Sa(e,!!l,l?[]:"",!1));return;case"textarea":w=z=null;for(h in l)if(n=l[h],l.hasOwnProperty(h)&&n!=null&&!a.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:Te(e,t,h,null,a,n)}for(r in a)if(n=a[r],u=l[r],a.hasOwnProperty(r)&&(n!=null||u!=null))switch(r){case"value":z=n;break;case"defaultValue":w=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(c(91));break;default:n!==u&&Te(e,t,r,n,a,u)}Bs(e,z,w);return;case"option":for(var ue in l)if(z=l[ue],l.hasOwnProperty(ue)&&z!=null&&!a.hasOwnProperty(ue))switch(ue){case"selected":e.selected=!1;break;default:Te(e,t,ue,null,a,z)}for(p in a)if(z=a[p],w=l[p],a.hasOwnProperty(p)&&z!==w&&(z!=null||w!=null))switch(p){case"selected":e.selected=z&&typeof z!="function"&&typeof z!="symbol";break;default:Te(e,t,p,z,a,w)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var te in l)z=l[te],l.hasOwnProperty(te)&&z!=null&&!a.hasOwnProperty(te)&&Te(e,t,te,null,a,z);for(R in a)if(z=a[R],w=l[R],a.hasOwnProperty(R)&&z!==w&&(z!=null||w!=null))switch(R){case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(c(137,t));break;default:Te(e,t,R,z,a,w)}return;default:if(Ki(t)){for(var Ae in l)z=l[Ae],l.hasOwnProperty(Ae)&&z!==void 0&&!a.hasOwnProperty(Ae)&&Nr(e,t,Ae,void 0,a,z);for(M in a)z=a[M],w=l[M],!a.hasOwnProperty(M)||z===w||z===void 0&&w===void 0||Nr(e,t,M,z,a,w);return}}for(var E in l)z=l[E],l.hasOwnProperty(E)&&z!=null&&!a.hasOwnProperty(E)&&Te(e,t,E,null,a,z);for(H in a)z=a[H],w=l[H],!a.hasOwnProperty(H)||z===w||z==null&&w==null||Te(e,t,H,z,a,w)}var Mr=null,Dr=null;function mi(e){return e.nodeType===9?e:e.ownerDocument}function Xd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function kd(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Cr(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ur=null;function dv(){var e=window.event;return e&&e.type==="popstate"?e===Ur?!1:(Ur=e,!0):(Ur=null,!1)}var Vd=typeof setTimeout=="function"?setTimeout:void 0,hv=typeof clearTimeout=="function"?clearTimeout:void 0,Qd=typeof Promise=="function"?Promise:void 0,mv=typeof queueMicrotask=="function"?queueMicrotask:typeof Qd<"u"?function(e){return Qd.resolve(null).then(e).catch(yv)}:Vd;function yv(e){setTimeout(function(){throw e})}function Hl(e){return e==="head"}function Zd(e,t){var l=t,a=0,n=0;do{var u=l.nextSibling;if(e.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var r=e.ownerDocument;if(l&1&&Jn(r.documentElement),l&2&&Jn(r.body),l&4)for(l=r.head,Jn(l),r=l.firstChild;r;){var h=r.nextSibling,p=r.nodeName;r[sn]||p==="SCRIPT"||p==="STYLE"||p==="LINK"&&r.rel.toLowerCase()==="stylesheet"||l.removeChild(r),r=h}}if(n===0){e.removeChild(u),lu(t);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);lu(t)}function Hr(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":Hr(l),Gi(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function vv(e,t,l,a){for(;e.nodeType===1;){var n=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[sn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=Lt(e.nextSibling),e===null)break}return null}function pv(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=Lt(e.nextSibling),e===null))return null;return e}function qr(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function gv(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Lt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Br=null;function Kd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function Jd(e,t,l){switch(t=mi(l),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function Jn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Gi(e)}var Ht=new Map,$d=new Set;function yi(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ol=k.d;k.d={f:bv,r:xv,D:Sv,C:jv,L:Ev,m:_v,X:Av,S:Tv,M:Rv};function bv(){var e=ol.f(),t=ii();return e||t}function xv(e){var t=pa(e);t!==null&&t.tag===5&&t.type==="form"?yf(t):ol.r(e)}var Fa=typeof document>"u"?null:document;function Wd(e,t,l){var a=Fa;if(a&&typeof t=="string"&&t){var n=wt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),$d.has(n)||($d.add(n),e={rel:e,crossOrigin:l,href:t},a.querySelector(n)===null&&(t=a.createElement("link"),Pe(t,"link",e),Ze(t),a.head.appendChild(t)))}}function Sv(e){ol.D(e),Wd("dns-prefetch",e,null)}function jv(e,t){ol.C(e,t),Wd("preconnect",e,t)}function Ev(e,t,l){ol.L(e,t,l);var a=Fa;if(a&&e&&t){var n='link[rel="preload"][as="'+wt(t)+'"]';t==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+wt(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+wt(l.imageSizes)+'"]')):n+='[href="'+wt(e)+'"]';var u=n;switch(t){case"style":u=Pa(e);break;case"script":u=Ia(e)}Ht.has(u)||(e=j({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),Ht.set(u,e),a.querySelector(n)!==null||t==="style"&&a.querySelector($n(u))||t==="script"&&a.querySelector(Wn(u))||(t=a.createElement("link"),Pe(t,"link",e),Ze(t),a.head.appendChild(t)))}}function _v(e,t){ol.m(e,t);var l=Fa;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+wt(a)+'"][href="'+wt(e)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Ia(e)}if(!Ht.has(u)&&(e=j({rel:"modulepreload",href:e},t),Ht.set(u,e),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Wn(u)))return}a=l.createElement("link"),Pe(a,"link",e),Ze(a),l.head.appendChild(a)}}}function Tv(e,t,l){ol.S(e,t,l);var a=Fa;if(a&&e){var n=ga(a).hoistableStyles,u=Pa(e);t=t||"default";var r=n.get(u);if(!r){var h={loading:0,preload:null};if(r=a.querySelector($n(u)))h.loading=5;else{e=j({rel:"stylesheet",href:e,"data-precedence":t},l),(l=Ht.get(u))&&Yr(e,l);var p=r=a.createElement("link");Ze(p),Pe(p,"link",e),p._p=new Promise(function(R,M){p.onload=R,p.onerror=M}),p.addEventListener("load",function(){h.loading|=1}),p.addEventListener("error",function(){h.loading|=2}),h.loading|=4,vi(r,t,a)}r={type:"stylesheet",instance:r,count:1,state:h},n.set(u,r)}}}function Av(e,t){ol.X(e,t);var l=Fa;if(l&&e){var a=ga(l).hoistableScripts,n=Ia(e),u=a.get(n);u||(u=l.querySelector(Wn(n)),u||(e=j({src:e,async:!0},t),(t=Ht.get(n))&&Lr(e,t),u=l.createElement("script"),Ze(u),Pe(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Rv(e,t){ol.M(e,t);var l=Fa;if(l&&e){var a=ga(l).hoistableScripts,n=Ia(e),u=a.get(n);u||(u=l.querySelector(Wn(n)),u||(e=j({src:e,async:!0,type:"module"},t),(t=Ht.get(n))&&Lr(e,t),u=l.createElement("script"),Ze(u),Pe(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Fd(e,t,l,a){var n=(n=ie.current)?yi(n):null;if(!n)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=Pa(l.href),l=ga(n).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=Pa(l.href);var u=ga(n).hoistableStyles,r=u.get(e);if(r||(n=n.ownerDocument||n,r={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,r),(u=n.querySelector($n(e)))&&!u._p&&(r.instance=u,r.state.loading=5),Ht.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Ht.set(e,l),u||zv(n,e,l,r.state))),t&&a===null)throw Error(c(528,""));return r}if(t&&a!==null)throw Error(c(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Ia(l),l=ga(n).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function Pa(e){return'href="'+wt(e)+'"'}function $n(e){return'link[rel="stylesheet"]['+e+"]"}function Pd(e){return j({},e,{"data-precedence":e.precedence,precedence:null})}function zv(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Pe(t,"link",l),Ze(t),e.head.appendChild(t))}function Ia(e){return'[src="'+wt(e)+'"]'}function Wn(e){return"script[async]"+e}function Id(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+wt(l.href)+'"]');if(a)return t.instance=a,Ze(a),a;var n=j({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Ze(a),Pe(a,"style",n),vi(a,l.precedence,e),t.instance=a;case"stylesheet":n=Pa(l.href);var u=e.querySelector($n(n));if(u)return t.state.loading|=4,t.instance=u,Ze(u),u;a=Pd(l),(n=Ht.get(n))&&Yr(a,n),u=(e.ownerDocument||e).createElement("link"),Ze(u);var r=u;return r._p=new Promise(function(h,p){r.onload=h,r.onerror=p}),Pe(u,"link",a),t.state.loading|=4,vi(u,l.precedence,e),t.instance=u;case"script":return u=Ia(l.src),(n=e.querySelector(Wn(u)))?(t.instance=n,Ze(n),n):(a=l,(n=Ht.get(u))&&(a=j({},l),Lr(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),Ze(n),Pe(n,"link",a),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,vi(a,l.precedence,e));return t.instance}function vi(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,r=0;r<a.length;r++){var h=a[r];if(h.dataset.precedence===t)u=h;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function Yr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Lr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var pi=null;function eh(e,t,l){if(pi===null){var a=new Map,n=pi=new Map;n.set(l,a)}else n=pi,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),n=0;n<l.length;n++){var u=l[n];if(!(u[sn]||u[et]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var r=u.getAttribute(t)||"";r=e+r;var h=a.get(r);h?h.push(u):a.set(r,[u])}}return a}function th(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function wv(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function lh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Fn=null;function Ov(){}function Nv(e,t,l){if(Fn===null)throw Error(c(475));var a=Fn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Pa(l.href),u=e.querySelector($n(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=gi.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=u,Ze(u);return}u=e.ownerDocument||e,l=Pd(l),(n=Ht.get(n))&&Yr(l,n),u=u.createElement("link"),Ze(u);var r=u;r._p=new Promise(function(h,p){r.onload=h,r.onerror=p}),Pe(u,"link",l),t.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=gi.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function Mv(){if(Fn===null)throw Error(c(475));var e=Fn;return e.stylesheets&&e.count===0&&Gr(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&Gr(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function gi(){if(this.count--,this.count===0){if(this.stylesheets)Gr(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var bi=null;function Gr(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,bi=new Map,t.forEach(Dv,e),bi=null,gi.call(e))}function Dv(e,t){if(!(t.state.loading&4)){var l=bi.get(e);if(l)var a=l.get(null);else{l=new Map,bi.set(e,l);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var r=n[u];(r.nodeName==="LINK"||r.getAttribute("media")!=="not all")&&(l.set(r.dataset.precedence,r),a=r)}a&&l.set(null,a)}n=t.instance,r=n.getAttribute("data-precedence"),u=l.get(r)||a,u===a&&l.set(null,n),l.set(r,n),this.count++,a=gi.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Pn={$$typeof:I,Provider:null,Consumer:null,_currentValue:B,_currentValue2:B,_threadCount:0};function Cv(e,t,l,a,n,u,r,h){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=qi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=qi(0),this.hiddenUpdates=qi(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=r,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function ah(e,t,l,a,n,u,r,h,p,R,M,H){return e=new Cv(e,t,l,r,h,p,R,H),t=1,u===!0&&(t|=24),u=jt(3,null,null,t),e.current=u,u.stateNode=e,t=jc(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:t},Ac(u),e}function nh(e){return e?(e=Oa,e):Oa}function uh(e,t,l,a,n,u){n=nh(n),a.context===null?a.context=n:a.pendingContext=n,a=El(t),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=_l(e,a,t),l!==null&&(Rt(l,e,t),zn(l,e,t))}function ih(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function Xr(e,t){ih(e,t),(e=e.alternate)&&ih(e,t)}function ch(e){if(e.tag===13){var t=wa(e,67108864);t!==null&&Rt(t,e,67108864),Xr(e,67108864)}}var xi=!0;function Uv(e,t,l,a){var n=N.T;N.T=null;var u=k.p;try{k.p=2,kr(e,t,l,a)}finally{k.p=u,N.T=n}}function Hv(e,t,l,a){var n=N.T;N.T=null;var u=k.p;try{k.p=8,kr(e,t,l,a)}finally{k.p=u,N.T=n}}function kr(e,t,l,a){if(xi){var n=Vr(a);if(n===null)Or(e,t,a,Si,l),sh(e,a);else if(Bv(n,e,t,l,a))a.stopPropagation();else if(sh(e,a),t&4&&-1<qv.indexOf(e)){for(;n!==null;){var u=pa(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var r=Jl(u.pendingLanes);if(r!==0){var h=u;for(h.pendingLanes|=2,h.entangledLanes|=2;r;){var p=1<<31-xt(r);h.entanglements[1]|=p,r&=~p}Kt(u),(je&6)===0&&(ni=Xt()+500,Qn(0))}}break;case 13:h=wa(u,2),h!==null&&Rt(h,u,2),ii(),Xr(u,2)}if(u=Vr(a),u===null&&Or(e,t,a,Si,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else Or(e,t,a,null,l)}}function Vr(e){return e=$i(e),Qr(e)}var Si=null;function Qr(e){if(Si=null,e=va(e),e!==null){var t=m(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=g(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Si=e,null}function rh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(jm()){case xs:return 2;case Ss:return 8;case hu:case Em:return 32;case js:return 268435456;default:return 32}default:return 32}}var Zr=!1,ql=null,Bl=null,Yl=null,In=new Map,eu=new Map,Ll=[],qv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function sh(e,t){switch(e){case"focusin":case"focusout":ql=null;break;case"dragenter":case"dragleave":Bl=null;break;case"mouseover":case"mouseout":Yl=null;break;case"pointerover":case"pointerout":In.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":eu.delete(t.pointerId)}}function tu(e,t,l,a,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},t!==null&&(t=pa(t),t!==null&&ch(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function Bv(e,t,l,a,n){switch(t){case"focusin":return ql=tu(ql,e,t,l,a,n),!0;case"dragenter":return Bl=tu(Bl,e,t,l,a,n),!0;case"mouseover":return Yl=tu(Yl,e,t,l,a,n),!0;case"pointerover":var u=n.pointerId;return In.set(u,tu(In.get(u)||null,e,t,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,eu.set(u,tu(eu.get(u)||null,e,t,l,a,n)),!0}return!1}function oh(e){var t=va(e.target);if(t!==null){var l=m(t);if(l!==null){if(t=l.tag,t===13){if(t=g(l),t!==null){e.blockedOn=t,Nm(e.priority,function(){if(l.tag===13){var a=At();a=Bi(a);var n=wa(l,a);n!==null&&Rt(n,l,a),Xr(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ji(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=Vr(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);Ji=a,l.target.dispatchEvent(a),Ji=null}else return t=pa(l),t!==null&&ch(t),e.blockedOn=l,!1;t.shift()}return!0}function fh(e,t,l){ji(e)&&l.delete(t)}function Yv(){Zr=!1,ql!==null&&ji(ql)&&(ql=null),Bl!==null&&ji(Bl)&&(Bl=null),Yl!==null&&ji(Yl)&&(Yl=null),In.forEach(fh),eu.forEach(fh)}function Ei(e,t){e.blockedOn===t&&(e.blockedOn=null,Zr||(Zr=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Yv)))}var _i=null;function dh(e){_i!==e&&(_i=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){_i===e&&(_i=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],n=e[t+2];if(typeof a!="function"){if(Qr(a||l)===null)continue;break}var u=pa(l);u!==null&&(e.splice(t,3),t-=3,Qc(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function lu(e){function t(p){return Ei(p,e)}ql!==null&&Ei(ql,e),Bl!==null&&Ei(Bl,e),Yl!==null&&Ei(Yl,e),In.forEach(t),eu.forEach(t);for(var l=0;l<Ll.length;l++){var a=Ll[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<Ll.length&&(l=Ll[0],l.blockedOn===null);)oh(l),l.blockedOn===null&&Ll.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],r=n[rt]||null;if(typeof u=="function")r||dh(l);else if(r){var h=null;if(u&&u.hasAttribute("formAction")){if(n=u,r=u[rt]||null)h=r.formAction;else if(Qr(n)!==null)continue}else h=r.action;typeof h=="function"?l[a+1]=h:(l.splice(a,3),a-=3),dh(l)}}}function Kr(e){this._internalRoot=e}Ti.prototype.render=Kr.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var l=t.current,a=At();uh(l,a,e,t,null,null)},Ti.prototype.unmount=Kr.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;uh(e.current,2,null,e,null,null),ii(),t[ya]=null}};function Ti(e){this._internalRoot=e}Ti.prototype.unstable_scheduleHydration=function(e){if(e){var t=Rs();e={blockedOn:null,target:e,priority:t};for(var l=0;l<Ll.length&&t!==0&&t<Ll[l].priority;l++);Ll.splice(l,0,e),l===0&&oh(e)}};var hh=o.version;if(hh!=="19.1.0")throw Error(c(527,hh,"19.1.0"));k.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=v(t),e=e!==null?y(e):null,e=e===null?null:e.stateNode,e};var Lv={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ai=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ai.isDisabled&&Ai.supportsFiber)try{un=Ai.inject(Lv),bt=Ai}catch{}}return nu.createRoot=function(e,t){if(!d(e))throw Error(c(299));var l=!1,a="",n=wf,u=Of,r=Nf,h=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(r=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(h=t.unstable_transitionCallbacks)),t=ah(e,1,!1,null,null,l,a,n,u,r,h,null),e[ya]=t.current,wr(e),new Kr(t)},nu.hydrateRoot=function(e,t,l){if(!d(e))throw Error(c(299));var a=!1,n="",u=wf,r=Of,h=Nf,p=null,R=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(r=l.onCaughtError),l.onRecoverableError!==void 0&&(h=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(p=l.unstable_transitionCallbacks),l.formState!==void 0&&(R=l.formState)),t=ah(e,1,!0,t,l??null,a,n,u,r,h,p,R),t.context=nh(null),l=t.current,a=At(),a=Bi(a),n=El(a),n.callback=null,_l(l,n,a),l=a,t.current.lanes=l,rn(t,l),Kt(t),e[ya]=t.current,wr(e),new Ti(t)},nu.version="19.1.0",nu}var Eh;function $v(){if(Eh)return Wr.exports;Eh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(o){console.error(o)}}return i(),Wr.exports=Jv(),Wr.exports}var Wv=$v(),uu={},_h;function Fv(){if(_h)return uu;_h=1,Object.defineProperty(uu,"__esModule",{value:!0}),uu.parse=g,uu.serialize=y;const i=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,o=/^[\u0021-\u003A\u003C-\u007E]*$/,f=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,c=/^[\u0020-\u003A\u003D-\u007E]*$/,d=Object.prototype.toString,m=(()=>{const O=function(){};return O.prototype=Object.create(null),O})();function g(O,X){const D=new m,G=O.length;if(G<2)return D;const Q=(X==null?void 0:X.decode)||j;let Y=0;do{const Z=O.indexOf("=",Y);if(Z===-1)break;const I=O.indexOf(";",Y),ce=I===-1?G:I;if(Z>ce){Y=O.lastIndexOf(";",Z-1)+1;continue}const S=A(O,Y,Z),V=v(O,Z,S),ye=O.slice(S,V);if(D[ye]===void 0){let J=A(O,Z+1,ce),ve=v(O,ce,J);const Qe=Q(O.slice(J,ve));D[ye]=Qe}Y=ce+1}while(Y<G);return D}function A(O,X,D){do{const G=O.charCodeAt(X);if(G!==32&&G!==9)return X}while(++X<D);return D}function v(O,X,D){for(;X>D;){const G=O.charCodeAt(--X);if(G!==32&&G!==9)return X+1}return D}function y(O,X,D){const G=(D==null?void 0:D.encode)||encodeURIComponent;if(!i.test(O))throw new TypeError(`argument name is invalid: ${O}`);const Q=G(X);if(!o.test(Q))throw new TypeError(`argument val is invalid: ${X}`);let Y=O+"="+Q;if(!D)return Y;if(D.maxAge!==void 0){if(!Number.isInteger(D.maxAge))throw new TypeError(`option maxAge is invalid: ${D.maxAge}`);Y+="; Max-Age="+D.maxAge}if(D.domain){if(!f.test(D.domain))throw new TypeError(`option domain is invalid: ${D.domain}`);Y+="; Domain="+D.domain}if(D.path){if(!c.test(D.path))throw new TypeError(`option path is invalid: ${D.path}`);Y+="; Path="+D.path}if(D.expires){if(!U(D.expires)||!Number.isFinite(D.expires.valueOf()))throw new TypeError(`option expires is invalid: ${D.expires}`);Y+="; Expires="+D.expires.toUTCString()}if(D.httpOnly&&(Y+="; HttpOnly"),D.secure&&(Y+="; Secure"),D.partitioned&&(Y+="; Partitioned"),D.priority)switch(typeof D.priority=="string"?D.priority.toLowerCase():void 0){case"low":Y+="; Priority=Low";break;case"medium":Y+="; Priority=Medium";break;case"high":Y+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${D.priority}`)}if(D.sameSite)switch(typeof D.sameSite=="string"?D.sameSite.toLowerCase():D.sameSite){case!0:case"strict":Y+="; SameSite=Strict";break;case"lax":Y+="; SameSite=Lax";break;case"none":Y+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${D.sameSite}`)}return Y}function j(O){if(O.indexOf("%")===-1)return O;try{return decodeURIComponent(O)}catch{return O}}function U(O){return d.call(O)==="[object Date]"}return uu}Fv();var Th="popstate";function Pv(i={}){function o(c,d){let{pathname:m,search:g,hash:A}=c.location;return ns("",{pathname:m,search:g,hash:A},d.state&&d.state.usr||null,d.state&&d.state.key||"default")}function f(c,d){return typeof d=="string"?d:ru(d)}return ep(o,f,null,i)}function De(i,o){if(i===!1||i===null||typeof i>"u")throw new Error(o)}function Jt(i,o){if(!i){typeof console<"u"&&console.warn(o);try{throw new Error(o)}catch{}}}function Iv(){return Math.random().toString(36).substring(2,10)}function Ah(i,o){return{usr:i.state,key:i.key,idx:o}}function ns(i,o,f=null,c){return{pathname:typeof i=="string"?i:i.pathname,search:"",hash:"",...typeof o=="string"?tn(o):o,state:f,key:o&&o.key||c||Iv()}}function ru({pathname:i="/",search:o="",hash:f=""}){return o&&o!=="?"&&(i+=o.charAt(0)==="?"?o:"?"+o),f&&f!=="#"&&(i+=f.charAt(0)==="#"?f:"#"+f),i}function tn(i){let o={};if(i){let f=i.indexOf("#");f>=0&&(o.hash=i.substring(f),i=i.substring(0,f));let c=i.indexOf("?");c>=0&&(o.search=i.substring(c),i=i.substring(0,c)),i&&(o.pathname=i)}return o}function ep(i,o,f,c={}){let{window:d=document.defaultView,v5Compat:m=!1}=c,g=d.history,A="POP",v=null,y=j();y==null&&(y=0,g.replaceState({...g.state,idx:y},""));function j(){return(g.state||{idx:null}).idx}function U(){A="POP";let Q=j(),Y=Q==null?null:Q-y;y=Q,v&&v({action:A,location:G.location,delta:Y})}function O(Q,Y){A="PUSH";let Z=ns(G.location,Q,Y);y=j()+1;let I=Ah(Z,y),ce=G.createHref(Z);try{g.pushState(I,"",ce)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;d.location.assign(ce)}m&&v&&v({action:A,location:G.location,delta:1})}function X(Q,Y){A="REPLACE";let Z=ns(G.location,Q,Y);y=j();let I=Ah(Z,y),ce=G.createHref(Z);g.replaceState(I,"",ce),m&&v&&v({action:A,location:G.location,delta:0})}function D(Q){return tp(Q)}let G={get action(){return A},get location(){return i(d,g)},listen(Q){if(v)throw new Error("A history only accepts one active listener");return d.addEventListener(Th,U),v=Q,()=>{d.removeEventListener(Th,U),v=null}},createHref(Q){return o(d,Q)},createURL:D,encodeLocation(Q){let Y=D(Q);return{pathname:Y.pathname,search:Y.search,hash:Y.hash}},push:O,replace:X,go(Q){return g.go(Q)}};return G}function tp(i,o=!1){let f="http://localhost";typeof window<"u"&&(f=window.location.origin!=="null"?window.location.origin:window.location.href),De(f,"No window.location.(origin|href) available to create URL");let c=typeof i=="string"?i:ru(i);return c=c.replace(/ $/,"%20"),!o&&c.startsWith("//")&&(c=f+c),new URL(c,f)}function Yh(i,o,f="/"){return lp(i,o,f,!1)}function lp(i,o,f,c){let d=typeof o=="string"?tn(o):o,m=hl(d.pathname||"/",f);if(m==null)return null;let g=Lh(i);ap(g);let A=null;for(let v=0;A==null&&v<g.length;++v){let y=mp(m);A=dp(g[v],y,c)}return A}function Lh(i,o=[],f=[],c=""){let d=(m,g,A)=>{let v={relativePath:A===void 0?m.path||"":A,caseSensitive:m.caseSensitive===!0,childrenIndex:g,route:m};v.relativePath.startsWith("/")&&(De(v.relativePath.startsWith(c),`Absolute route path "${v.relativePath}" nested under path "${c}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),v.relativePath=v.relativePath.slice(c.length));let y=dl([c,v.relativePath]),j=f.concat(v);m.children&&m.children.length>0&&(De(m.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${y}".`),Lh(m.children,o,j,y)),!(m.path==null&&!m.index)&&o.push({path:y,score:op(y,m.index),routesMeta:j})};return i.forEach((m,g)=>{var A;if(m.path===""||!((A=m.path)!=null&&A.includes("?")))d(m,g);else for(let v of Gh(m.path))d(m,g,v)}),o}function Gh(i){let o=i.split("/");if(o.length===0)return[];let[f,...c]=o,d=f.endsWith("?"),m=f.replace(/\?$/,"");if(c.length===0)return d?[m,""]:[m];let g=Gh(c.join("/")),A=[];return A.push(...g.map(v=>v===""?m:[m,v].join("/"))),d&&A.push(...g),A.map(v=>i.startsWith("/")&&v===""?"/":v)}function ap(i){i.sort((o,f)=>o.score!==f.score?f.score-o.score:fp(o.routesMeta.map(c=>c.childrenIndex),f.routesMeta.map(c=>c.childrenIndex)))}var np=/^:[\w-]+$/,up=3,ip=2,cp=1,rp=10,sp=-2,Rh=i=>i==="*";function op(i,o){let f=i.split("/"),c=f.length;return f.some(Rh)&&(c+=sp),o&&(c+=ip),f.filter(d=>!Rh(d)).reduce((d,m)=>d+(np.test(m)?up:m===""?cp:rp),c)}function fp(i,o){return i.length===o.length&&i.slice(0,-1).every((c,d)=>c===o[d])?i[i.length-1]-o[o.length-1]:0}function dp(i,o,f=!1){let{routesMeta:c}=i,d={},m="/",g=[];for(let A=0;A<c.length;++A){let v=c[A],y=A===c.length-1,j=m==="/"?o:o.slice(m.length)||"/",U=Mi({path:v.relativePath,caseSensitive:v.caseSensitive,end:y},j),O=v.route;if(!U&&y&&f&&!c[c.length-1].route.index&&(U=Mi({path:v.relativePath,caseSensitive:v.caseSensitive,end:!1},j)),!U)return null;Object.assign(d,U.params),g.push({params:d,pathname:dl([m,U.pathname]),pathnameBase:gp(dl([m,U.pathnameBase])),route:O}),U.pathnameBase!=="/"&&(m=dl([m,U.pathnameBase]))}return g}function Mi(i,o){typeof i=="string"&&(i={path:i,caseSensitive:!1,end:!0});let[f,c]=hp(i.path,i.caseSensitive,i.end),d=o.match(f);if(!d)return null;let m=d[0],g=m.replace(/(.)\/+$/,"$1"),A=d.slice(1);return{params:c.reduce((y,{paramName:j,isOptional:U},O)=>{if(j==="*"){let D=A[O]||"";g=m.slice(0,m.length-D.length).replace(/(.)\/+$/,"$1")}const X=A[O];return U&&!X?y[j]=void 0:y[j]=(X||"").replace(/%2F/g,"/"),y},{}),pathname:m,pathnameBase:g,pattern:i}}function hp(i,o=!1,f=!0){Jt(i==="*"||!i.endsWith("*")||i.endsWith("/*"),`Route path "${i}" will be treated as if it were "${i.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${i.replace(/\*$/,"/*")}".`);let c=[],d="^"+i.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(g,A,v)=>(c.push({paramName:A,isOptional:v!=null}),v?"/?([^\\/]+)?":"/([^\\/]+)"));return i.endsWith("*")?(c.push({paramName:"*"}),d+=i==="*"||i==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):f?d+="\\/*$":i!==""&&i!=="/"&&(d+="(?:(?=\\/|$))"),[new RegExp(d,o?void 0:"i"),c]}function mp(i){try{return i.split("/").map(o=>decodeURIComponent(o).replace(/\//g,"%2F")).join("/")}catch(o){return Jt(!1,`The URL path "${i}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${o}).`),i}}function hl(i,o){if(o==="/")return i;if(!i.toLowerCase().startsWith(o.toLowerCase()))return null;let f=o.endsWith("/")?o.length-1:o.length,c=i.charAt(f);return c&&c!=="/"?null:i.slice(f)||"/"}function yp(i,o="/"){let{pathname:f,search:c="",hash:d=""}=typeof i=="string"?tn(i):i;return{pathname:f?f.startsWith("/")?f:vp(f,o):o,search:bp(c),hash:xp(d)}}function vp(i,o){let f=o.replace(/\/+$/,"").split("/");return i.split("/").forEach(d=>{d===".."?f.length>1&&f.pop():d!=="."&&f.push(d)}),f.length>1?f.join("/"):"/"}function es(i,o,f,c){return`Cannot include a '${i}' character in a manually specified \`to.${o}\` field [${JSON.stringify(c)}].  Please separate it out to the \`to.${f}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function pp(i){return i.filter((o,f)=>f===0||o.route.path&&o.route.path.length>0)}function Xh(i){let o=pp(i);return o.map((f,c)=>c===o.length-1?f.pathname:f.pathnameBase)}function kh(i,o,f,c=!1){let d;typeof i=="string"?d=tn(i):(d={...i},De(!d.pathname||!d.pathname.includes("?"),es("?","pathname","search",d)),De(!d.pathname||!d.pathname.includes("#"),es("#","pathname","hash",d)),De(!d.search||!d.search.includes("#"),es("#","search","hash",d)));let m=i===""||d.pathname==="",g=m?"/":d.pathname,A;if(g==null)A=f;else{let U=o.length-1;if(!c&&g.startsWith("..")){let O=g.split("/");for(;O[0]==="..";)O.shift(),U-=1;d.pathname=O.join("/")}A=U>=0?o[U]:"/"}let v=yp(d,A),y=g&&g!=="/"&&g.endsWith("/"),j=(m||g===".")&&f.endsWith("/");return!v.pathname.endsWith("/")&&(y||j)&&(v.pathname+="/"),v}var dl=i=>i.join("/").replace(/\/\/+/g,"/"),gp=i=>i.replace(/\/+$/,"").replace(/^\/*/,"/"),bp=i=>!i||i==="?"?"":i.startsWith("?")?i:"?"+i,xp=i=>!i||i==="#"?"":i.startsWith("#")?i:"#"+i;function Sp(i){return i!=null&&typeof i.status=="number"&&typeof i.statusText=="string"&&typeof i.internal=="boolean"&&"data"in i}var Vh=["POST","PUT","PATCH","DELETE"];new Set(Vh);var jp=["GET",...Vh];new Set(jp);var ln=T.createContext(null);ln.displayName="DataRouter";var Di=T.createContext(null);Di.displayName="DataRouterState";var Qh=T.createContext({isTransitioning:!1});Qh.displayName="ViewTransition";var Ep=T.createContext(new Map);Ep.displayName="Fetchers";var _p=T.createContext(null);_p.displayName="Await";var $t=T.createContext(null);$t.displayName="Navigation";var su=T.createContext(null);su.displayName="Location";var ml=T.createContext({outlet:null,matches:[],isDataRoute:!1});ml.displayName="Route";var fs=T.createContext(null);fs.displayName="RouteError";function Tp(i,{relative:o}={}){De(ou(),"useHref() may be used only in the context of a <Router> component.");let{basename:f,navigator:c}=T.useContext($t),{hash:d,pathname:m,search:g}=fu(i,{relative:o}),A=m;return f!=="/"&&(A=m==="/"?f:dl([f,m])),c.createHref({pathname:A,search:g,hash:d})}function ou(){return T.useContext(su)!=null}function Kl(){return De(ou(),"useLocation() may be used only in the context of a <Router> component."),T.useContext(su).location}var Zh="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Kh(i){T.useContext($t).static||T.useLayoutEffect(i)}function Ap(){let{isDataRoute:i}=T.useContext(ml);return i?Yp():Rp()}function Rp(){De(ou(),"useNavigate() may be used only in the context of a <Router> component.");let i=T.useContext(ln),{basename:o,navigator:f}=T.useContext($t),{matches:c}=T.useContext(ml),{pathname:d}=Kl(),m=JSON.stringify(Xh(c)),g=T.useRef(!1);return Kh(()=>{g.current=!0}),T.useCallback((v,y={})=>{if(Jt(g.current,Zh),!g.current)return;if(typeof v=="number"){f.go(v);return}let j=kh(v,JSON.parse(m),d,y.relative==="path");i==null&&o!=="/"&&(j.pathname=j.pathname==="/"?o:dl([o,j.pathname])),(y.replace?f.replace:f.push)(j,y.state,y)},[o,f,m,d,i])}T.createContext(null);function fu(i,{relative:o}={}){let{matches:f}=T.useContext(ml),{pathname:c}=Kl(),d=JSON.stringify(Xh(f));return T.useMemo(()=>kh(i,JSON.parse(d),c,o==="path"),[i,d,c,o])}function zp(i,o){return Jh(i,o)}function Jh(i,o,f,c){var Y;De(ou(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:d}=T.useContext($t),{matches:m}=T.useContext(ml),g=m[m.length-1],A=g?g.params:{},v=g?g.pathname:"/",y=g?g.pathnameBase:"/",j=g&&g.route;{let Z=j&&j.path||"";$h(v,!j||Z.endsWith("*")||Z.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${v}" (under <Route path="${Z}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${Z}"> to <Route path="${Z==="/"?"*":`${Z}/*`}">.`)}let U=Kl(),O;if(o){let Z=typeof o=="string"?tn(o):o;De(y==="/"||((Y=Z.pathname)==null?void 0:Y.startsWith(y)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${y}" but pathname "${Z.pathname}" was given in the \`location\` prop.`),O=Z}else O=U;let X=O.pathname||"/",D=X;if(y!=="/"){let Z=y.replace(/^\//,"").split("/");D="/"+X.replace(/^\//,"").split("/").slice(Z.length).join("/")}let G=Yh(i,{pathname:D});Jt(j||G!=null,`No routes matched location "${O.pathname}${O.search}${O.hash}" `),Jt(G==null||G[G.length-1].route.element!==void 0||G[G.length-1].route.Component!==void 0||G[G.length-1].route.lazy!==void 0,`Matched leaf route at location "${O.pathname}${O.search}${O.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let Q=Dp(G&&G.map(Z=>Object.assign({},Z,{params:Object.assign({},A,Z.params),pathname:dl([y,d.encodeLocation?d.encodeLocation(Z.pathname).pathname:Z.pathname]),pathnameBase:Z.pathnameBase==="/"?y:dl([y,d.encodeLocation?d.encodeLocation(Z.pathnameBase).pathname:Z.pathnameBase])})),m,f,c);return o&&Q?T.createElement(su.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...O},navigationType:"POP"}},Q):Q}function wp(){let i=Bp(),o=Sp(i)?`${i.status} ${i.statusText}`:i instanceof Error?i.message:JSON.stringify(i),f=i instanceof Error?i.stack:null,c="rgba(200,200,200, 0.5)",d={padding:"0.5rem",backgroundColor:c},m={padding:"2px 4px",backgroundColor:c},g=null;return console.error("Error handled by React Router default ErrorBoundary:",i),g=T.createElement(T.Fragment,null,T.createElement("p",null,"💿 Hey developer 👋"),T.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",T.createElement("code",{style:m},"ErrorBoundary")," or"," ",T.createElement("code",{style:m},"errorElement")," prop on your route.")),T.createElement(T.Fragment,null,T.createElement("h2",null,"Unexpected Application Error!"),T.createElement("h3",{style:{fontStyle:"italic"}},o),f?T.createElement("pre",{style:d},f):null,g)}var Op=T.createElement(wp,null),Np=class extends T.Component{constructor(i){super(i),this.state={location:i.location,revalidation:i.revalidation,error:i.error}}static getDerivedStateFromError(i){return{error:i}}static getDerivedStateFromProps(i,o){return o.location!==i.location||o.revalidation!=="idle"&&i.revalidation==="idle"?{error:i.error,location:i.location,revalidation:i.revalidation}:{error:i.error!==void 0?i.error:o.error,location:o.location,revalidation:i.revalidation||o.revalidation}}componentDidCatch(i,o){console.error("React Router caught the following error during render",i,o)}render(){return this.state.error!==void 0?T.createElement(ml.Provider,{value:this.props.routeContext},T.createElement(fs.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Mp({routeContext:i,match:o,children:f}){let c=T.useContext(ln);return c&&c.static&&c.staticContext&&(o.route.errorElement||o.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=o.route.id),T.createElement(ml.Provider,{value:i},f)}function Dp(i,o=[],f=null,c=null){if(i==null){if(!f)return null;if(f.errors)i=f.matches;else if(o.length===0&&!f.initialized&&f.matches.length>0)i=f.matches;else return null}let d=i,m=f==null?void 0:f.errors;if(m!=null){let v=d.findIndex(y=>y.route.id&&(m==null?void 0:m[y.route.id])!==void 0);De(v>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(m).join(",")}`),d=d.slice(0,Math.min(d.length,v+1))}let g=!1,A=-1;if(f)for(let v=0;v<d.length;v++){let y=d[v];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(A=v),y.route.id){let{loaderData:j,errors:U}=f,O=y.route.loader&&!j.hasOwnProperty(y.route.id)&&(!U||U[y.route.id]===void 0);if(y.route.lazy||O){g=!0,A>=0?d=d.slice(0,A+1):d=[d[0]];break}}}return d.reduceRight((v,y,j)=>{let U,O=!1,X=null,D=null;f&&(U=m&&y.route.id?m[y.route.id]:void 0,X=y.route.errorElement||Op,g&&(A<0&&j===0?($h("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),O=!0,D=null):A===j&&(O=!0,D=y.route.hydrateFallbackElement||null)));let G=o.concat(d.slice(0,j+1)),Q=()=>{let Y;return U?Y=X:O?Y=D:y.route.Component?Y=T.createElement(y.route.Component,null):y.route.element?Y=y.route.element:Y=v,T.createElement(Mp,{match:y,routeContext:{outlet:v,matches:G,isDataRoute:f!=null},children:Y})};return f&&(y.route.ErrorBoundary||y.route.errorElement||j===0)?T.createElement(Np,{location:f.location,revalidation:f.revalidation,component:X,error:U,children:Q(),routeContext:{outlet:null,matches:G,isDataRoute:!0}}):Q()},null)}function ds(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Cp(i){let o=T.useContext(ln);return De(o,ds(i)),o}function Up(i){let o=T.useContext(Di);return De(o,ds(i)),o}function Hp(i){let o=T.useContext(ml);return De(o,ds(i)),o}function hs(i){let o=Hp(i),f=o.matches[o.matches.length-1];return De(f.route.id,`${i} can only be used on routes that contain a unique "id"`),f.route.id}function qp(){return hs("useRouteId")}function Bp(){var c;let i=T.useContext(fs),o=Up("useRouteError"),f=hs("useRouteError");return i!==void 0?i:(c=o.errors)==null?void 0:c[f]}function Yp(){let{router:i}=Cp("useNavigate"),o=hs("useNavigate"),f=T.useRef(!1);return Kh(()=>{f.current=!0}),T.useCallback(async(d,m={})=>{Jt(f.current,Zh),f.current&&(typeof d=="number"?i.navigate(d):await i.navigate(d,{fromRouteId:o,...m}))},[i,o])}var zh={};function $h(i,o,f){!o&&!zh[i]&&(zh[i]=!0,Jt(!1,f))}T.memo(Lp);function Lp({routes:i,future:o,state:f}){return Jh(i,void 0,f,o)}function kl(i){De(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Gp({basename:i="/",children:o=null,location:f,navigationType:c="POP",navigator:d,static:m=!1}){De(!ou(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let g=i.replace(/^\/*/,"/"),A=T.useMemo(()=>({basename:g,navigator:d,static:m,future:{}}),[g,d,m]);typeof f=="string"&&(f=tn(f));let{pathname:v="/",search:y="",hash:j="",state:U=null,key:O="default"}=f,X=T.useMemo(()=>{let D=hl(v,g);return D==null?null:{location:{pathname:D,search:y,hash:j,state:U,key:O},navigationType:c}},[g,v,y,j,U,O,c]);return Jt(X!=null,`<Router basename="${g}"> is not able to match the URL "${v}${y}${j}" because it does not start with the basename, so the <Router> won't render anything.`),X==null?null:T.createElement($t.Provider,{value:A},T.createElement(su.Provider,{children:o,value:X}))}function Xp({children:i,location:o}){return zp(us(i),o)}function us(i,o=[]){let f=[];return T.Children.forEach(i,(c,d)=>{if(!T.isValidElement(c))return;let m=[...o,d];if(c.type===T.Fragment){f.push.apply(f,us(c.props.children,m));return}De(c.type===kl,`[${typeof c.type=="string"?c.type:c.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),De(!c.props.index||!c.props.children,"An index route cannot have child routes.");let g={id:c.props.id||m.join("-"),caseSensitive:c.props.caseSensitive,element:c.props.element,Component:c.props.Component,index:c.props.index,path:c.props.path,loader:c.props.loader,action:c.props.action,hydrateFallbackElement:c.props.hydrateFallbackElement,HydrateFallback:c.props.HydrateFallback,errorElement:c.props.errorElement,ErrorBoundary:c.props.ErrorBoundary,hasErrorBoundary:c.props.hasErrorBoundary===!0||c.props.ErrorBoundary!=null||c.props.errorElement!=null,shouldRevalidate:c.props.shouldRevalidate,handle:c.props.handle,lazy:c.props.lazy};c.props.children&&(g.children=us(c.props.children,m)),f.push(g)}),f}var Oi="get",Ni="application/x-www-form-urlencoded";function Ci(i){return i!=null&&typeof i.tagName=="string"}function kp(i){return Ci(i)&&i.tagName.toLowerCase()==="button"}function Vp(i){return Ci(i)&&i.tagName.toLowerCase()==="form"}function Qp(i){return Ci(i)&&i.tagName.toLowerCase()==="input"}function Zp(i){return!!(i.metaKey||i.altKey||i.ctrlKey||i.shiftKey)}function Kp(i,o){return i.button===0&&(!o||o==="_self")&&!Zp(i)}var Ri=null;function Jp(){if(Ri===null)try{new FormData(document.createElement("form"),0),Ri=!1}catch{Ri=!0}return Ri}var $p=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function ts(i){return i!=null&&!$p.has(i)?(Jt(!1,`"${i}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ni}"`),null):i}function Wp(i,o){let f,c,d,m,g;if(Vp(i)){let A=i.getAttribute("action");c=A?hl(A,o):null,f=i.getAttribute("method")||Oi,d=ts(i.getAttribute("enctype"))||Ni,m=new FormData(i)}else if(kp(i)||Qp(i)&&(i.type==="submit"||i.type==="image")){let A=i.form;if(A==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let v=i.getAttribute("formaction")||A.getAttribute("action");if(c=v?hl(v,o):null,f=i.getAttribute("formmethod")||A.getAttribute("method")||Oi,d=ts(i.getAttribute("formenctype"))||ts(A.getAttribute("enctype"))||Ni,m=new FormData(A,i),!Jp()){let{name:y,type:j,value:U}=i;if(j==="image"){let O=y?`${y}.`:"";m.append(`${O}x`,"0"),m.append(`${O}y`,"0")}else y&&m.append(y,U)}}else{if(Ci(i))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');f=Oi,c=null,d=Ni,g=i}return m&&d==="text/plain"&&(g=m,m=void 0),{action:c,method:f.toLowerCase(),encType:d,formData:m,body:g}}function ms(i,o){if(i===!1||i===null||typeof i>"u")throw new Error(o)}async function Fp(i,o){if(i.id in o)return o[i.id];try{let f=await import(i.module);return o[i.id]=f,f}catch(f){return console.error(`Error loading route module \`${i.module}\`, reloading page...`),console.error(f),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Pp(i){return i==null?!1:i.href==null?i.rel==="preload"&&typeof i.imageSrcSet=="string"&&typeof i.imageSizes=="string":typeof i.rel=="string"&&typeof i.href=="string"}async function Ip(i,o,f){let c=await Promise.all(i.map(async d=>{let m=o.routes[d.route.id];if(m){let g=await Fp(m,f);return g.links?g.links():[]}return[]}));return ag(c.flat(1).filter(Pp).filter(d=>d.rel==="stylesheet"||d.rel==="preload").map(d=>d.rel==="stylesheet"?{...d,rel:"prefetch",as:"style"}:{...d,rel:"prefetch"}))}function wh(i,o,f,c,d,m){let g=(v,y)=>f[y]?v.route.id!==f[y].route.id:!0,A=(v,y)=>{var j;return f[y].pathname!==v.pathname||((j=f[y].route.path)==null?void 0:j.endsWith("*"))&&f[y].params["*"]!==v.params["*"]};return m==="assets"?o.filter((v,y)=>g(v,y)||A(v,y)):m==="data"?o.filter((v,y)=>{var U;let j=c.routes[v.route.id];if(!j||!j.hasLoader)return!1;if(g(v,y)||A(v,y))return!0;if(v.route.shouldRevalidate){let O=v.route.shouldRevalidate({currentUrl:new URL(d.pathname+d.search+d.hash,window.origin),currentParams:((U=f[0])==null?void 0:U.params)||{},nextUrl:new URL(i,window.origin),nextParams:v.params,defaultShouldRevalidate:!0});if(typeof O=="boolean")return O}return!0}):[]}function eg(i,o,{includeHydrateFallback:f}={}){return tg(i.map(c=>{let d=o.routes[c.route.id];if(!d)return[];let m=[d.module];return d.clientActionModule&&(m=m.concat(d.clientActionModule)),d.clientLoaderModule&&(m=m.concat(d.clientLoaderModule)),f&&d.hydrateFallbackModule&&(m=m.concat(d.hydrateFallbackModule)),d.imports&&(m=m.concat(d.imports)),m}).flat(1))}function tg(i){return[...new Set(i)]}function lg(i){let o={},f=Object.keys(i).sort();for(let c of f)o[c]=i[c];return o}function ag(i,o){let f=new Set;return new Set(o),i.reduce((c,d)=>{let m=JSON.stringify(lg(d));return f.has(m)||(f.add(m),c.push({key:m,link:d})),c},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var ng=new Set([100,101,204,205]);function ug(i,o){let f=typeof i=="string"?new URL(i,typeof window>"u"?"server://singlefetch/":window.location.origin):i;return f.pathname==="/"?f.pathname="_root.data":o&&hl(f.pathname,o)==="/"?f.pathname=`${o.replace(/\/$/,"")}/_root.data`:f.pathname=`${f.pathname.replace(/\/$/,"")}.data`,f}function Wh(){let i=T.useContext(ln);return ms(i,"You must render this element inside a <DataRouterContext.Provider> element"),i}function ig(){let i=T.useContext(Di);return ms(i,"You must render this element inside a <DataRouterStateContext.Provider> element"),i}var ys=T.createContext(void 0);ys.displayName="FrameworkContext";function Fh(){let i=T.useContext(ys);return ms(i,"You must render this element inside a <HydratedRouter> element"),i}function cg(i,o){let f=T.useContext(ys),[c,d]=T.useState(!1),[m,g]=T.useState(!1),{onFocus:A,onBlur:v,onMouseEnter:y,onMouseLeave:j,onTouchStart:U}=o,O=T.useRef(null);T.useEffect(()=>{if(i==="render"&&g(!0),i==="viewport"){let G=Y=>{Y.forEach(Z=>{g(Z.isIntersecting)})},Q=new IntersectionObserver(G,{threshold:.5});return O.current&&Q.observe(O.current),()=>{Q.disconnect()}}},[i]),T.useEffect(()=>{if(c){let G=setTimeout(()=>{g(!0)},100);return()=>{clearTimeout(G)}}},[c]);let X=()=>{d(!0)},D=()=>{d(!1),g(!1)};return f?i!=="intent"?[m,O,{}]:[m,O,{onFocus:iu(A,X),onBlur:iu(v,D),onMouseEnter:iu(y,X),onMouseLeave:iu(j,D),onTouchStart:iu(U,X)}]:[!1,O,{}]}function iu(i,o){return f=>{i&&i(f),f.defaultPrevented||o(f)}}function rg({page:i,...o}){let{router:f}=Wh(),c=T.useMemo(()=>Yh(f.routes,i,f.basename),[f.routes,i,f.basename]);return c?T.createElement(og,{page:i,matches:c,...o}):null}function sg(i){let{manifest:o,routeModules:f}=Fh(),[c,d]=T.useState([]);return T.useEffect(()=>{let m=!1;return Ip(i,o,f).then(g=>{m||d(g)}),()=>{m=!0}},[i,o,f]),c}function og({page:i,matches:o,...f}){let c=Kl(),{manifest:d,routeModules:m}=Fh(),{basename:g}=Wh(),{loaderData:A,matches:v}=ig(),y=T.useMemo(()=>wh(i,o,v,d,c,"data"),[i,o,v,d,c]),j=T.useMemo(()=>wh(i,o,v,d,c,"assets"),[i,o,v,d,c]),U=T.useMemo(()=>{if(i===c.pathname+c.search+c.hash)return[];let D=new Set,G=!1;if(o.forEach(Y=>{var I;let Z=d.routes[Y.route.id];!Z||!Z.hasLoader||(!y.some(ce=>ce.route.id===Y.route.id)&&Y.route.id in A&&((I=m[Y.route.id])!=null&&I.shouldRevalidate)||Z.hasClientLoader?G=!0:D.add(Y.route.id))}),D.size===0)return[];let Q=ug(i,g);return G&&D.size>0&&Q.searchParams.set("_routes",o.filter(Y=>D.has(Y.route.id)).map(Y=>Y.route.id).join(",")),[Q.pathname+Q.search]},[g,A,c,d,y,o,i,m]),O=T.useMemo(()=>eg(j,d),[j,d]),X=sg(j);return T.createElement(T.Fragment,null,U.map(D=>T.createElement("link",{key:D,rel:"prefetch",as:"fetch",href:D,...f})),O.map(D=>T.createElement("link",{key:D,rel:"modulepreload",href:D,...f})),X.map(({key:D,link:G})=>T.createElement("link",{key:D,...G})))}function fg(...i){return o=>{i.forEach(f=>{typeof f=="function"?f(o):f!=null&&(f.current=o)})}}var Ph=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Ph&&(window.__reactRouterVersion="7.6.2")}catch{}function dg({basename:i,children:o,window:f}){let c=T.useRef();c.current==null&&(c.current=Pv({window:f,v5Compat:!0}));let d=c.current,[m,g]=T.useState({action:d.action,location:d.location}),A=T.useCallback(v=>{T.startTransition(()=>g(v))},[g]);return T.useLayoutEffect(()=>d.listen(A),[d,A]),T.createElement(Gp,{basename:i,children:o,location:m.location,navigationType:m.action,navigator:d})}var Ih=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,vs=T.forwardRef(function({onClick:o,discover:f="render",prefetch:c="none",relative:d,reloadDocument:m,replace:g,state:A,target:v,to:y,preventScrollReset:j,viewTransition:U,...O},X){let{basename:D}=T.useContext($t),G=typeof y=="string"&&Ih.test(y),Q,Y=!1;if(typeof y=="string"&&G&&(Q=y,Ph))try{let ve=new URL(window.location.href),Qe=y.startsWith("//")?new URL(ve.protocol+y):new URL(y),it=hl(Qe.pathname,D);Qe.origin===ve.origin&&it!=null?y=it+Qe.search+Qe.hash:Y=!0}catch{Jt(!1,`<Link to="${y}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let Z=Tp(y,{relative:d}),[I,ce,S]=cg(c,O),V=vg(y,{replace:g,state:A,target:v,preventScrollReset:j,relative:d,viewTransition:U});function ye(ve){o&&o(ve),ve.defaultPrevented||V(ve)}let J=T.createElement("a",{...O,...S,href:Q||Z,onClick:Y||m?o:ye,ref:fg(X,ce),target:v,"data-discover":!G&&f==="render"?"true":void 0});return I&&!G?T.createElement(T.Fragment,null,J,T.createElement(rg,{page:Z})):J});vs.displayName="Link";var hg=T.forwardRef(function({"aria-current":o="page",caseSensitive:f=!1,className:c="",end:d=!1,style:m,to:g,viewTransition:A,children:v,...y},j){let U=fu(g,{relative:y.relative}),O=Kl(),X=T.useContext(Di),{navigator:D,basename:G}=T.useContext($t),Q=X!=null&&Sg(U)&&A===!0,Y=D.encodeLocation?D.encodeLocation(U).pathname:U.pathname,Z=O.pathname,I=X&&X.navigation&&X.navigation.location?X.navigation.location.pathname:null;f||(Z=Z.toLowerCase(),I=I?I.toLowerCase():null,Y=Y.toLowerCase()),I&&G&&(I=hl(I,G)||I);const ce=Y!=="/"&&Y.endsWith("/")?Y.length-1:Y.length;let S=Z===Y||!d&&Z.startsWith(Y)&&Z.charAt(ce)==="/",V=I!=null&&(I===Y||!d&&I.startsWith(Y)&&I.charAt(Y.length)==="/"),ye={isActive:S,isPending:V,isTransitioning:Q},J=S?o:void 0,ve;typeof c=="function"?ve=c(ye):ve=[c,S?"active":null,V?"pending":null,Q?"transitioning":null].filter(Boolean).join(" ");let Qe=typeof m=="function"?m(ye):m;return T.createElement(vs,{...y,"aria-current":J,className:ve,ref:j,style:Qe,to:g,viewTransition:A},typeof v=="function"?v(ye):v)});hg.displayName="NavLink";var mg=T.forwardRef(({discover:i="render",fetcherKey:o,navigate:f,reloadDocument:c,replace:d,state:m,method:g=Oi,action:A,onSubmit:v,relative:y,preventScrollReset:j,viewTransition:U,...O},X)=>{let D=bg(),G=xg(A,{relative:y}),Q=g.toLowerCase()==="get"?"get":"post",Y=typeof A=="string"&&Ih.test(A),Z=I=>{if(v&&v(I),I.defaultPrevented)return;I.preventDefault();let ce=I.nativeEvent.submitter,S=(ce==null?void 0:ce.getAttribute("formmethod"))||g;D(ce||I.currentTarget,{fetcherKey:o,method:S,navigate:f,replace:d,state:m,relative:y,preventScrollReset:j,viewTransition:U})};return T.createElement("form",{ref:X,method:Q,action:G,onSubmit:c?v:Z,...O,"data-discover":!Y&&i==="render"?"true":void 0})});mg.displayName="Form";function yg(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function em(i){let o=T.useContext(ln);return De(o,yg(i)),o}function vg(i,{target:o,replace:f,state:c,preventScrollReset:d,relative:m,viewTransition:g}={}){let A=Ap(),v=Kl(),y=fu(i,{relative:m});return T.useCallback(j=>{if(Kp(j,o)){j.preventDefault();let U=f!==void 0?f:ru(v)===ru(y);A(i,{replace:U,state:c,preventScrollReset:d,relative:m,viewTransition:g})}},[v,A,y,f,c,o,i,d,m,g])}var pg=0,gg=()=>`__${String(++pg)}__`;function bg(){let{router:i}=em("useSubmit"),{basename:o}=T.useContext($t),f=qp();return T.useCallback(async(c,d={})=>{let{action:m,method:g,encType:A,formData:v,body:y}=Wp(c,o);if(d.navigate===!1){let j=d.fetcherKey||gg();await i.fetch(j,f,d.action||m,{preventScrollReset:d.preventScrollReset,formData:v,body:y,formMethod:d.method||g,formEncType:d.encType||A,flushSync:d.flushSync})}else await i.navigate(d.action||m,{preventScrollReset:d.preventScrollReset,formData:v,body:y,formMethod:d.method||g,formEncType:d.encType||A,replace:d.replace,state:d.state,fromRouteId:f,flushSync:d.flushSync,viewTransition:d.viewTransition})},[i,o,f])}function xg(i,{relative:o}={}){let{basename:f}=T.useContext($t),c=T.useContext(ml);De(c,"useFormAction must be used inside a RouteContext");let[d]=c.matches.slice(-1),m={...fu(i||".",{relative:o})},g=Kl();if(i==null){m.search=g.search;let A=new URLSearchParams(m.search),v=A.getAll("index");if(v.some(j=>j==="")){A.delete("index"),v.filter(U=>U).forEach(U=>A.append("index",U));let j=A.toString();m.search=j?`?${j}`:""}}return(!i||i===".")&&d.route.index&&(m.search=m.search?m.search.replace(/^\?/,"?index&"):"?index"),f!=="/"&&(m.pathname=m.pathname==="/"?f:dl([f,m.pathname])),ru(m)}function Sg(i,o={}){let f=T.useContext(Qh);De(f!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:c}=em("useViewTransitionState"),d=fu(i,{relative:o.relative});if(!f.isTransitioning)return!1;let m=hl(f.currentLocation.pathname,c)||f.currentLocation.pathname,g=hl(f.nextLocation.pathname,c)||f.nextLocation.pathname;return Mi(d.pathname,g)!=null||Mi(d.pathname,m)!=null}[...ng];Bh();function Oh(i,o){if(typeof i=="function")return i(o);i!=null&&(i.current=o)}function jg(...i){return o=>{let f=!1;const c=i.map(d=>{const m=Oh(d,o);return!f&&typeof m=="function"&&(f=!0),m});if(f)return()=>{for(let d=0;d<c.length;d++){const m=c[d];typeof m=="function"?m():Oh(i[d],null)}}}}function tm(i){const o=Eg(i),f=T.forwardRef((c,d)=>{const{children:m,...g}=c,A=T.Children.toArray(m),v=A.find(Tg);if(v){const y=v.props.children,j=A.map(U=>U===v?T.Children.count(y)>1?T.Children.only(null):T.isValidElement(y)?y.props.children:null:U);return s.jsx(o,{...g,ref:d,children:T.isValidElement(y)?T.cloneElement(y,void 0,j):null})}return s.jsx(o,{...g,ref:d,children:m})});return f.displayName=`${i}.Slot`,f}var lm=tm("Slot");function Eg(i){const o=T.forwardRef((f,c)=>{const{children:d,...m}=f;if(T.isValidElement(d)){const g=Rg(d),A=Ag(m,d.props);return d.type!==T.Fragment&&(A.ref=c?jg(c,g):g),T.cloneElement(d,A)}return T.Children.count(d)>1?T.Children.only(null):null});return o.displayName=`${i}.SlotClone`,o}var _g=Symbol("radix.slottable");function Tg(i){return T.isValidElement(i)&&typeof i.type=="function"&&"__radixId"in i.type&&i.type.__radixId===_g}function Ag(i,o){const f={...o};for(const c in o){const d=i[c],m=o[c];/^on[A-Z]/.test(c)?d&&m?f[c]=(...A)=>{const v=m(...A);return d(...A),v}:d&&(f[c]=d):c==="style"?f[c]={...d,...m}:c==="className"&&(f[c]=[d,m].filter(Boolean).join(" "))}return{...i,...f}}function Rg(i){var c,d;let o=(c=Object.getOwnPropertyDescriptor(i.props,"ref"))==null?void 0:c.get,f=o&&"isReactWarning"in o&&o.isReactWarning;return f?i.ref:(o=(d=Object.getOwnPropertyDescriptor(i,"ref"))==null?void 0:d.get,f=o&&"isReactWarning"in o&&o.isReactWarning,f?i.props.ref:i.props.ref||i.ref)}function am(i){var o,f,c="";if(typeof i=="string"||typeof i=="number")c+=i;else if(typeof i=="object")if(Array.isArray(i)){var d=i.length;for(o=0;o<d;o++)i[o]&&(f=am(i[o]))&&(c&&(c+=" "),c+=f)}else for(f in i)i[f]&&(c&&(c+=" "),c+=f);return c}function nm(){for(var i,o,f=0,c="",d=arguments.length;f<d;f++)(i=arguments[f])&&(o=am(i))&&(c&&(c+=" "),c+=o);return c}const Nh=i=>typeof i=="boolean"?`${i}`:i===0?"0":i,Mh=nm,ps=(i,o)=>f=>{var c;if((o==null?void 0:o.variants)==null)return Mh(i,f==null?void 0:f.class,f==null?void 0:f.className);const{variants:d,defaultVariants:m}=o,g=Object.keys(d).map(y=>{const j=f==null?void 0:f[y],U=m==null?void 0:m[y];if(j===null)return null;const O=Nh(j)||Nh(U);return d[y][O]}),A=f&&Object.entries(f).reduce((y,j)=>{let[U,O]=j;return O===void 0||(y[U]=O),y},{}),v=o==null||(c=o.compoundVariants)===null||c===void 0?void 0:c.reduce((y,j)=>{let{class:U,className:O,...X}=j;return Object.entries(X).every(D=>{let[G,Q]=D;return Array.isArray(Q)?Q.includes({...m,...A}[G]):{...m,...A}[G]===Q})?[...y,U,O]:y},[]);return Mh(i,g,v,f==null?void 0:f.class,f==null?void 0:f.className)},gs="-",zg=i=>{const o=Og(i),{conflictingClassGroups:f,conflictingClassGroupModifiers:c}=i;return{getClassGroupId:g=>{const A=g.split(gs);return A[0]===""&&A.length!==1&&A.shift(),um(A,o)||wg(g)},getConflictingClassGroupIds:(g,A)=>{const v=f[g]||[];return A&&c[g]?[...v,...c[g]]:v}}},um=(i,o)=>{var g;if(i.length===0)return o.classGroupId;const f=i[0],c=o.nextPart.get(f),d=c?um(i.slice(1),c):void 0;if(d)return d;if(o.validators.length===0)return;const m=i.join(gs);return(g=o.validators.find(({validator:A})=>A(m)))==null?void 0:g.classGroupId},Dh=/^\[(.+)\]$/,wg=i=>{if(Dh.test(i)){const o=Dh.exec(i)[1],f=o==null?void 0:o.substring(0,o.indexOf(":"));if(f)return"arbitrary.."+f}},Og=i=>{const{theme:o,classGroups:f}=i,c={nextPart:new Map,validators:[]};for(const d in f)is(f[d],c,d,o);return c},is=(i,o,f,c)=>{i.forEach(d=>{if(typeof d=="string"){const m=d===""?o:Ch(o,d);m.classGroupId=f;return}if(typeof d=="function"){if(Ng(d)){is(d(c),o,f,c);return}o.validators.push({validator:d,classGroupId:f});return}Object.entries(d).forEach(([m,g])=>{is(g,Ch(o,m),f,c)})})},Ch=(i,o)=>{let f=i;return o.split(gs).forEach(c=>{f.nextPart.has(c)||f.nextPart.set(c,{nextPart:new Map,validators:[]}),f=f.nextPart.get(c)}),f},Ng=i=>i.isThemeGetter,Mg=i=>{if(i<1)return{get:()=>{},set:()=>{}};let o=0,f=new Map,c=new Map;const d=(m,g)=>{f.set(m,g),o++,o>i&&(o=0,c=f,f=new Map)};return{get(m){let g=f.get(m);if(g!==void 0)return g;if((g=c.get(m))!==void 0)return d(m,g),g},set(m,g){f.has(m)?f.set(m,g):d(m,g)}}},cs="!",rs=":",Dg=rs.length,Cg=i=>{const{prefix:o,experimentalParseClassName:f}=i;let c=d=>{const m=[];let g=0,A=0,v=0,y;for(let D=0;D<d.length;D++){let G=d[D];if(g===0&&A===0){if(G===rs){m.push(d.slice(v,D)),v=D+Dg;continue}if(G==="/"){y=D;continue}}G==="["?g++:G==="]"?g--:G==="("?A++:G===")"&&A--}const j=m.length===0?d:d.substring(v),U=Ug(j),O=U!==j,X=y&&y>v?y-v:void 0;return{modifiers:m,hasImportantModifier:O,baseClassName:U,maybePostfixModifierPosition:X}};if(o){const d=o+rs,m=c;c=g=>g.startsWith(d)?m(g.substring(d.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:g,maybePostfixModifierPosition:void 0}}if(f){const d=c;c=m=>f({className:m,parseClassName:d})}return c},Ug=i=>i.endsWith(cs)?i.substring(0,i.length-1):i.startsWith(cs)?i.substring(1):i,Hg=i=>{const o=Object.fromEntries(i.orderSensitiveModifiers.map(c=>[c,!0]));return c=>{if(c.length<=1)return c;const d=[];let m=[];return c.forEach(g=>{g[0]==="["||o[g]?(d.push(...m.sort(),g),m=[]):m.push(g)}),d.push(...m.sort()),d}},qg=i=>({cache:Mg(i.cacheSize),parseClassName:Cg(i),sortModifiers:Hg(i),...zg(i)}),Bg=/\s+/,Yg=(i,o)=>{const{parseClassName:f,getClassGroupId:c,getConflictingClassGroupIds:d,sortModifiers:m}=o,g=[],A=i.trim().split(Bg);let v="";for(let y=A.length-1;y>=0;y-=1){const j=A[y],{isExternal:U,modifiers:O,hasImportantModifier:X,baseClassName:D,maybePostfixModifierPosition:G}=f(j);if(U){v=j+(v.length>0?" "+v:v);continue}let Q=!!G,Y=c(Q?D.substring(0,G):D);if(!Y){if(!Q){v=j+(v.length>0?" "+v:v);continue}if(Y=c(D),!Y){v=j+(v.length>0?" "+v:v);continue}Q=!1}const Z=m(O).join(":"),I=X?Z+cs:Z,ce=I+Y;if(g.includes(ce))continue;g.push(ce);const S=d(Y,Q);for(let V=0;V<S.length;++V){const ye=S[V];g.push(I+ye)}v=j+(v.length>0?" "+v:v)}return v};function Lg(){let i=0,o,f,c="";for(;i<arguments.length;)(o=arguments[i++])&&(f=im(o))&&(c&&(c+=" "),c+=f);return c}const im=i=>{if(typeof i=="string")return i;let o,f="";for(let c=0;c<i.length;c++)i[c]&&(o=im(i[c]))&&(f&&(f+=" "),f+=o);return f};function Gg(i,...o){let f,c,d,m=g;function g(v){const y=o.reduce((j,U)=>U(j),i());return f=qg(y),c=f.cache.get,d=f.cache.set,m=A,A(v)}function A(v){const y=c(v);if(y)return y;const j=Yg(v,f);return d(v,j),j}return function(){return m(Lg.apply(null,arguments))}}const Ve=i=>{const o=f=>f[i]||[];return o.isThemeGetter=!0,o},cm=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,rm=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Xg=/^\d+\/\d+$/,kg=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Vg=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Qg=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Zg=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Kg=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,en=i=>Xg.test(i),oe=i=>!!i&&!Number.isNaN(Number(i)),Xl=i=>!!i&&Number.isInteger(Number(i)),ls=i=>i.endsWith("%")&&oe(i.slice(0,-1)),fl=i=>kg.test(i),Jg=()=>!0,$g=i=>Vg.test(i)&&!Qg.test(i),sm=()=>!1,Wg=i=>Zg.test(i),Fg=i=>Kg.test(i),Pg=i=>!F(i)&&!P(i),Ig=i=>an(i,dm,sm),F=i=>cm.test(i),da=i=>an(i,hm,$g),as=i=>an(i,n0,oe),Uh=i=>an(i,om,sm),e0=i=>an(i,fm,Fg),zi=i=>an(i,mm,Wg),P=i=>rm.test(i),cu=i=>nn(i,hm),t0=i=>nn(i,u0),Hh=i=>nn(i,om),l0=i=>nn(i,dm),a0=i=>nn(i,fm),wi=i=>nn(i,mm,!0),an=(i,o,f)=>{const c=cm.exec(i);return c?c[1]?o(c[1]):f(c[2]):!1},nn=(i,o,f=!1)=>{const c=rm.exec(i);return c?c[1]?o(c[1]):f:!1},om=i=>i==="position"||i==="percentage",fm=i=>i==="image"||i==="url",dm=i=>i==="length"||i==="size"||i==="bg-size",hm=i=>i==="length",n0=i=>i==="number",u0=i=>i==="family-name",mm=i=>i==="shadow",i0=()=>{const i=Ve("color"),o=Ve("font"),f=Ve("text"),c=Ve("font-weight"),d=Ve("tracking"),m=Ve("leading"),g=Ve("breakpoint"),A=Ve("container"),v=Ve("spacing"),y=Ve("radius"),j=Ve("shadow"),U=Ve("inset-shadow"),O=Ve("text-shadow"),X=Ve("drop-shadow"),D=Ve("blur"),G=Ve("perspective"),Q=Ve("aspect"),Y=Ve("ease"),Z=Ve("animate"),I=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ce=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],S=()=>[...ce(),P,F],V=()=>["auto","hidden","clip","visible","scroll"],ye=()=>["auto","contain","none"],J=()=>[P,F,v],ve=()=>[en,"full","auto",...J()],Qe=()=>[Xl,"none","subgrid",P,F],it=()=>["auto",{span:["full",Xl,P,F]},Xl,P,F],qe=()=>[Xl,"auto",P,F],Gt=()=>["auto","min","max","fr",P,F],qt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Ne=()=>["start","end","center","stretch","center-safe","end-safe"],N=()=>["auto",...J()],k=()=>[en,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...J()],B=()=>[i,P,F],Se=()=>[...ce(),Hh,Uh,{position:[P,F]}],b=()=>["no-repeat",{repeat:["","x","y","space","round"]}],q=()=>["auto","cover","contain",l0,Ig,{size:[P,F]}],K=()=>[ls,cu,da],L=()=>["","none","full",y,P,F],$=()=>["",oe,cu,da],de=()=>["solid","dashed","dotted","double"],ie=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ge=()=>[oe,ls,Hh,Uh],Re=()=>["","none",D,P,F],gt=()=>["none",oe,P,F],yl=()=>["none",oe,P,F],vl=()=>[oe,P,F],pl=()=>[en,"full",...J()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[fl],breakpoint:[fl],color:[Jg],container:[fl],"drop-shadow":[fl],ease:["in","out","in-out"],font:[Pg],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[fl],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[fl],shadow:[fl],spacing:["px",oe],text:[fl],"text-shadow":[fl],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",en,F,P,Q]}],container:["container"],columns:[{columns:[oe,F,P,A]}],"break-after":[{"break-after":I()}],"break-before":[{"break-before":I()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:S()}],overflow:[{overflow:V()}],"overflow-x":[{"overflow-x":V()}],"overflow-y":[{"overflow-y":V()}],overscroll:[{overscroll:ye()}],"overscroll-x":[{"overscroll-x":ye()}],"overscroll-y":[{"overscroll-y":ye()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ve()}],"inset-x":[{"inset-x":ve()}],"inset-y":[{"inset-y":ve()}],start:[{start:ve()}],end:[{end:ve()}],top:[{top:ve()}],right:[{right:ve()}],bottom:[{bottom:ve()}],left:[{left:ve()}],visibility:["visible","invisible","collapse"],z:[{z:[Xl,"auto",P,F]}],basis:[{basis:[en,"full","auto",A,...J()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[oe,en,"auto","initial","none",F]}],grow:[{grow:["",oe,P,F]}],shrink:[{shrink:["",oe,P,F]}],order:[{order:[Xl,"first","last","none",P,F]}],"grid-cols":[{"grid-cols":Qe()}],"col-start-end":[{col:it()}],"col-start":[{"col-start":qe()}],"col-end":[{"col-end":qe()}],"grid-rows":[{"grid-rows":Qe()}],"row-start-end":[{row:it()}],"row-start":[{"row-start":qe()}],"row-end":[{"row-end":qe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Gt()}],"auto-rows":[{"auto-rows":Gt()}],gap:[{gap:J()}],"gap-x":[{"gap-x":J()}],"gap-y":[{"gap-y":J()}],"justify-content":[{justify:[...qt(),"normal"]}],"justify-items":[{"justify-items":[...Ne(),"normal"]}],"justify-self":[{"justify-self":["auto",...Ne()]}],"align-content":[{content:["normal",...qt()]}],"align-items":[{items:[...Ne(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Ne(),{baseline:["","last"]}]}],"place-content":[{"place-content":qt()}],"place-items":[{"place-items":[...Ne(),"baseline"]}],"place-self":[{"place-self":["auto",...Ne()]}],p:[{p:J()}],px:[{px:J()}],py:[{py:J()}],ps:[{ps:J()}],pe:[{pe:J()}],pt:[{pt:J()}],pr:[{pr:J()}],pb:[{pb:J()}],pl:[{pl:J()}],m:[{m:N()}],mx:[{mx:N()}],my:[{my:N()}],ms:[{ms:N()}],me:[{me:N()}],mt:[{mt:N()}],mr:[{mr:N()}],mb:[{mb:N()}],ml:[{ml:N()}],"space-x":[{"space-x":J()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":J()}],"space-y-reverse":["space-y-reverse"],size:[{size:k()}],w:[{w:[A,"screen",...k()]}],"min-w":[{"min-w":[A,"screen","none",...k()]}],"max-w":[{"max-w":[A,"screen","none","prose",{screen:[g]},...k()]}],h:[{h:["screen","lh",...k()]}],"min-h":[{"min-h":["screen","lh","none",...k()]}],"max-h":[{"max-h":["screen","lh",...k()]}],"font-size":[{text:["base",f,cu,da]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[c,P,as]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ls,F]}],"font-family":[{font:[t0,F,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[d,P,F]}],"line-clamp":[{"line-clamp":[oe,"none",P,as]}],leading:[{leading:[m,...J()]}],"list-image":[{"list-image":["none",P,F]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",P,F]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:B()}],"text-color":[{text:B()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...de(),"wavy"]}],"text-decoration-thickness":[{decoration:[oe,"from-font","auto",P,da]}],"text-decoration-color":[{decoration:B()}],"underline-offset":[{"underline-offset":[oe,"auto",P,F]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:J()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",P,F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",P,F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Se()}],"bg-repeat":[{bg:b()}],"bg-size":[{bg:q()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Xl,P,F],radial:["",P,F],conic:[Xl,P,F]},a0,e0]}],"bg-color":[{bg:B()}],"gradient-from-pos":[{from:K()}],"gradient-via-pos":[{via:K()}],"gradient-to-pos":[{to:K()}],"gradient-from":[{from:B()}],"gradient-via":[{via:B()}],"gradient-to":[{to:B()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:$()}],"border-w-x":[{"border-x":$()}],"border-w-y":[{"border-y":$()}],"border-w-s":[{"border-s":$()}],"border-w-e":[{"border-e":$()}],"border-w-t":[{"border-t":$()}],"border-w-r":[{"border-r":$()}],"border-w-b":[{"border-b":$()}],"border-w-l":[{"border-l":$()}],"divide-x":[{"divide-x":$()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":$()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...de(),"hidden","none"]}],"divide-style":[{divide:[...de(),"hidden","none"]}],"border-color":[{border:B()}],"border-color-x":[{"border-x":B()}],"border-color-y":[{"border-y":B()}],"border-color-s":[{"border-s":B()}],"border-color-e":[{"border-e":B()}],"border-color-t":[{"border-t":B()}],"border-color-r":[{"border-r":B()}],"border-color-b":[{"border-b":B()}],"border-color-l":[{"border-l":B()}],"divide-color":[{divide:B()}],"outline-style":[{outline:[...de(),"none","hidden"]}],"outline-offset":[{"outline-offset":[oe,P,F]}],"outline-w":[{outline:["",oe,cu,da]}],"outline-color":[{outline:B()}],shadow:[{shadow:["","none",j,wi,zi]}],"shadow-color":[{shadow:B()}],"inset-shadow":[{"inset-shadow":["none",U,wi,zi]}],"inset-shadow-color":[{"inset-shadow":B()}],"ring-w":[{ring:$()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:B()}],"ring-offset-w":[{"ring-offset":[oe,da]}],"ring-offset-color":[{"ring-offset":B()}],"inset-ring-w":[{"inset-ring":$()}],"inset-ring-color":[{"inset-ring":B()}],"text-shadow":[{"text-shadow":["none",O,wi,zi]}],"text-shadow-color":[{"text-shadow":B()}],opacity:[{opacity:[oe,P,F]}],"mix-blend":[{"mix-blend":[...ie(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ie()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[oe]}],"mask-image-linear-from-pos":[{"mask-linear-from":ge()}],"mask-image-linear-to-pos":[{"mask-linear-to":ge()}],"mask-image-linear-from-color":[{"mask-linear-from":B()}],"mask-image-linear-to-color":[{"mask-linear-to":B()}],"mask-image-t-from-pos":[{"mask-t-from":ge()}],"mask-image-t-to-pos":[{"mask-t-to":ge()}],"mask-image-t-from-color":[{"mask-t-from":B()}],"mask-image-t-to-color":[{"mask-t-to":B()}],"mask-image-r-from-pos":[{"mask-r-from":ge()}],"mask-image-r-to-pos":[{"mask-r-to":ge()}],"mask-image-r-from-color":[{"mask-r-from":B()}],"mask-image-r-to-color":[{"mask-r-to":B()}],"mask-image-b-from-pos":[{"mask-b-from":ge()}],"mask-image-b-to-pos":[{"mask-b-to":ge()}],"mask-image-b-from-color":[{"mask-b-from":B()}],"mask-image-b-to-color":[{"mask-b-to":B()}],"mask-image-l-from-pos":[{"mask-l-from":ge()}],"mask-image-l-to-pos":[{"mask-l-to":ge()}],"mask-image-l-from-color":[{"mask-l-from":B()}],"mask-image-l-to-color":[{"mask-l-to":B()}],"mask-image-x-from-pos":[{"mask-x-from":ge()}],"mask-image-x-to-pos":[{"mask-x-to":ge()}],"mask-image-x-from-color":[{"mask-x-from":B()}],"mask-image-x-to-color":[{"mask-x-to":B()}],"mask-image-y-from-pos":[{"mask-y-from":ge()}],"mask-image-y-to-pos":[{"mask-y-to":ge()}],"mask-image-y-from-color":[{"mask-y-from":B()}],"mask-image-y-to-color":[{"mask-y-to":B()}],"mask-image-radial":[{"mask-radial":[P,F]}],"mask-image-radial-from-pos":[{"mask-radial-from":ge()}],"mask-image-radial-to-pos":[{"mask-radial-to":ge()}],"mask-image-radial-from-color":[{"mask-radial-from":B()}],"mask-image-radial-to-color":[{"mask-radial-to":B()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":ce()}],"mask-image-conic-pos":[{"mask-conic":[oe]}],"mask-image-conic-from-pos":[{"mask-conic-from":ge()}],"mask-image-conic-to-pos":[{"mask-conic-to":ge()}],"mask-image-conic-from-color":[{"mask-conic-from":B()}],"mask-image-conic-to-color":[{"mask-conic-to":B()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Se()}],"mask-repeat":[{mask:b()}],"mask-size":[{mask:q()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",P,F]}],filter:[{filter:["","none",P,F]}],blur:[{blur:Re()}],brightness:[{brightness:[oe,P,F]}],contrast:[{contrast:[oe,P,F]}],"drop-shadow":[{"drop-shadow":["","none",X,wi,zi]}],"drop-shadow-color":[{"drop-shadow":B()}],grayscale:[{grayscale:["",oe,P,F]}],"hue-rotate":[{"hue-rotate":[oe,P,F]}],invert:[{invert:["",oe,P,F]}],saturate:[{saturate:[oe,P,F]}],sepia:[{sepia:["",oe,P,F]}],"backdrop-filter":[{"backdrop-filter":["","none",P,F]}],"backdrop-blur":[{"backdrop-blur":Re()}],"backdrop-brightness":[{"backdrop-brightness":[oe,P,F]}],"backdrop-contrast":[{"backdrop-contrast":[oe,P,F]}],"backdrop-grayscale":[{"backdrop-grayscale":["",oe,P,F]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[oe,P,F]}],"backdrop-invert":[{"backdrop-invert":["",oe,P,F]}],"backdrop-opacity":[{"backdrop-opacity":[oe,P,F]}],"backdrop-saturate":[{"backdrop-saturate":[oe,P,F]}],"backdrop-sepia":[{"backdrop-sepia":["",oe,P,F]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":J()}],"border-spacing-x":[{"border-spacing-x":J()}],"border-spacing-y":[{"border-spacing-y":J()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",P,F]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[oe,"initial",P,F]}],ease:[{ease:["linear","initial",Y,P,F]}],delay:[{delay:[oe,P,F]}],animate:[{animate:["none",Z,P,F]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[G,P,F]}],"perspective-origin":[{"perspective-origin":S()}],rotate:[{rotate:gt()}],"rotate-x":[{"rotate-x":gt()}],"rotate-y":[{"rotate-y":gt()}],"rotate-z":[{"rotate-z":gt()}],scale:[{scale:yl()}],"scale-x":[{"scale-x":yl()}],"scale-y":[{"scale-y":yl()}],"scale-z":[{"scale-z":yl()}],"scale-3d":["scale-3d"],skew:[{skew:vl()}],"skew-x":[{"skew-x":vl()}],"skew-y":[{"skew-y":vl()}],transform:[{transform:[P,F,"","none","gpu","cpu"]}],"transform-origin":[{origin:S()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:pl()}],"translate-x":[{"translate-x":pl()}],"translate-y":[{"translate-y":pl()}],"translate-z":[{"translate-z":pl()}],"translate-none":["translate-none"],accent:[{accent:B()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:B()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",P,F]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":J()}],"scroll-mx":[{"scroll-mx":J()}],"scroll-my":[{"scroll-my":J()}],"scroll-ms":[{"scroll-ms":J()}],"scroll-me":[{"scroll-me":J()}],"scroll-mt":[{"scroll-mt":J()}],"scroll-mr":[{"scroll-mr":J()}],"scroll-mb":[{"scroll-mb":J()}],"scroll-ml":[{"scroll-ml":J()}],"scroll-p":[{"scroll-p":J()}],"scroll-px":[{"scroll-px":J()}],"scroll-py":[{"scroll-py":J()}],"scroll-ps":[{"scroll-ps":J()}],"scroll-pe":[{"scroll-pe":J()}],"scroll-pt":[{"scroll-pt":J()}],"scroll-pr":[{"scroll-pr":J()}],"scroll-pb":[{"scroll-pb":J()}],"scroll-pl":[{"scroll-pl":J()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",P,F]}],fill:[{fill:["none",...B()]}],"stroke-w":[{stroke:[oe,cu,da,as]}],stroke:[{stroke:["none",...B()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},c0=Gg(i0);function Ie(...i){return c0(nm(i))}const r0=ps("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function at({className:i,variant:o,size:f,asChild:c=!1,...d}){const m=c?lm:"button";return s.jsx(m,{"data-slot":"button",className:Ie(r0({variant:o,size:f,className:i})),...d})}function mt({className:i,...o}){return s.jsx("div",{"data-slot":"card",className:Ie("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",i),...o})}function yt({className:i,...o}){return s.jsx("div",{"data-slot":"card-header",className:Ie("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",i),...o})}function vt({className:i,...o}){return s.jsx("div",{"data-slot":"card-title",className:Ie("leading-none font-semibold",i),...o})}function ym({className:i,...o}){return s.jsx("div",{"data-slot":"card-description",className:Ie("text-muted-foreground text-sm",i),...o})}function pt({className:i,...o}){return s.jsx("div",{"data-slot":"card-content",className:Ie("px-6",i),...o})}var s0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],o0=s0.reduce((i,o)=>{const f=tm(`Primitive.${o}`),c=T.forwardRef((d,m)=>{const{asChild:g,...A}=d,v=g?f:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),s.jsx(v,{...A,ref:m})});return c.displayName=`Primitive.${o}`,{...i,[o]:c}},{});function ht({className:i,type:o,...f}){return s.jsx("input",{type:o,"data-slot":"input",className:Ie("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",i),...f})}var f0="Label",vm=T.forwardRef((i,o)=>s.jsx(o0.label,{...i,ref:o,onMouseDown:f=>{var d;f.target.closest("button, input, select, textarea")||((d=i.onMouseDown)==null||d.call(i,f),!f.defaultPrevented&&f.detail>1&&f.preventDefault())}}));vm.displayName=f0;var d0=vm;function He({className:i,...o}){return s.jsx(d0,{"data-slot":"label",className:Ie("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",i),...o})}function Vl({className:i,...o}){return s.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:s.jsx("table",{"data-slot":"table",className:Ie("w-full caption-bottom text-sm",i),...o})})}function Ql({className:i,...o}){return s.jsx("thead",{"data-slot":"table-header",className:Ie("[&_tr]:border-b",i),...o})}function Zl({className:i,...o}){return s.jsx("tbody",{"data-slot":"table-body",className:Ie("[&_tr:last-child]:border-0",i),...o})}function ut({className:i,...o}){return s.jsx("tr",{"data-slot":"table-row",className:Ie("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",i),...o})}function ae({className:i,...o}){return s.jsx("th",{"data-slot":"table-head",className:Ie("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",i),...o})}function ne({className:i,...o}){return s.jsx("td",{"data-slot":"table-cell",className:Ie("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",i),...o})}const h0=ps("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function ha({className:i,variant:o,asChild:f=!1,...c}){const d=f?lm:"span";return s.jsx(d,{"data-slot":"badge",className:Ie(h0({variant:o}),i),...c})}const m0=ps("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function y0({className:i,variant:o,...f}){return s.jsx("div",{"data-slot":"alert",role:"alert",className:Ie(m0({variant:o}),i),...f})}function v0({className:i,...o}){return s.jsx("div",{"data-slot":"alert-description",className:Ie("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",i),...o})}/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p0=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),g0=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(o,f,c)=>c?c.toUpperCase():f.toLowerCase()),qh=i=>{const o=g0(i);return o.charAt(0).toUpperCase()+o.slice(1)},pm=(...i)=>i.filter((o,f,c)=>!!o&&o.trim()!==""&&c.indexOf(o)===f).join(" ").trim(),b0=i=>{for(const o in i)if(o.startsWith("aria-")||o==="role"||o==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var x0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S0=T.forwardRef(({color:i="currentColor",size:o=24,strokeWidth:f=2,absoluteStrokeWidth:c,className:d="",children:m,iconNode:g,...A},v)=>T.createElement("svg",{ref:v,...x0,width:o,height:o,stroke:i,strokeWidth:c?Number(f)*24/Number(o):f,className:pm("lucide",d),...!m&&!b0(A)&&{"aria-hidden":"true"},...A},[...g.map(([y,j])=>T.createElement(y,j)),...Array.isArray(m)?m:[m]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ma=(i,o)=>{const f=T.forwardRef(({className:c,...d},m)=>T.createElement(S0,{ref:m,iconNode:o,className:pm(`lucide-${p0(qh(i))}`,`lucide-${i}`,c),...d}));return f.displayName=qh(i),f};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j0=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],E0=ma("eye",j0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _0=[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]],ss=ma("package",_0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T0=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],du=ma("plus",T0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A0=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],R0=ma("search",A0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z0=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]],gm=ma("trending-up",z0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w0=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],bs=ma("triangle-alert",w0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O0=[["path",{d:"M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z",key:"gksnxg"}],["path",{d:"M6 18h12",key:"9pbo8z"}],["path",{d:"M6 14h12",key:"4cwo0f"}],["rect",{width:"12",height:"12",x:"6",y:"10",key:"apd30q"}]],bm=ma("warehouse",O0),nt="http://localhost:5000/api";function N0(){const i=Kl(),o=[{path:"/",label:"仪表板",icon:gm},{path:"/products",label:"商品管理",icon:ss},{path:"/warehouses",label:"仓库管理",icon:bm},{path:"/inventory",label:"库存查询",icon:R0},{path:"/inbound",label:"入库管理",icon:du},{path:"/outbound",label:"出库管理",icon:E0},{path:"/warnings",label:"库存预警",icon:bs}];return s.jsx("nav",{className:"bg-white shadow-sm border-b",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"flex justify-between h-16",children:s.jsxs("div",{className:"flex",children:[s.jsxs("div",{className:"flex-shrink-0 flex items-center",children:[s.jsx(ss,{className:"h-8 w-8 text-blue-600"}),s.jsx("span",{className:"ml-2 text-xl font-bold text-gray-900",children:"库存管理系统"})]}),s.jsx("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:o.map(f=>{const c=f.icon;return s.jsxs(vs,{to:f.path,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${i.pathname===f.path?"border-blue-500 text-gray-900":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"}`,children:[s.jsx(c,{className:"h-4 w-4 mr-1"}),f.label]},f.path)})})]})})})})}function M0(){const[i,o]=T.useState({totalProducts:0,totalWarehouses:0,lowStockItems:0,totalInventoryValue:0});return T.useEffect(()=>{o({totalProducts:156,totalWarehouses:3,lowStockItems:12,totalInventoryValue:258e4})},[]),s.jsxs("div",{className:"space-y-6",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"仪表板"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[s.jsxs(mt,{children:[s.jsxs(yt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(vt,{className:"text-sm font-medium",children:"商品总数"}),s.jsx(ss,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(pt,{children:[s.jsx("div",{className:"text-2xl font-bold",children:i.totalProducts}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"+2.1% 较上月"})]})]}),s.jsxs(mt,{children:[s.jsxs(yt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(vt,{className:"text-sm font-medium",children:"仓库数量"}),s.jsx(bm,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(pt,{children:[s.jsx("div",{className:"text-2xl font-bold",children:i.totalWarehouses}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"无变化"})]})]}),s.jsxs(mt,{children:[s.jsxs(yt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(vt,{className:"text-sm font-medium",children:"低库存商品"}),s.jsx(bs,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(pt,{children:[s.jsx("div",{className:"text-2xl font-bold text-red-600",children:i.lowStockItems}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"需要补货"})]})]}),s.jsxs(mt,{children:[s.jsxs(yt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(vt,{className:"text-sm font-medium",children:"库存总价值"}),s.jsx(gm,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(pt,{children:[s.jsxs("div",{className:"text-2xl font-bold",children:["¥",i.totalInventoryValue.toLocaleString()]}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"+5.2% 较上月"})]})]})]})]})}function D0(){const[i,o]=T.useState([]),[f,c]=T.useState(!1),[d,m]=T.useState({product_name:"",sku:"",category:"",brand:"",unit:"",purchase_price:"",sales_price:""});T.useEffect(()=>{g()},[]);const g=async()=>{try{const y=await(await fetch(`${nt}/products`)).json();y.code===200&&o(y.data)}catch(v){console.error("获取商品列表失败:",v)}},A=async v=>{v.preventDefault();try{const j=await(await fetch(`${nt}/products`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)})).json();j.code===200?(c(!1),m({product_name:"",sku:"",category:"",brand:"",unit:"",purchase_price:"",sales_price:""}),g()):alert(j.message)}catch(y){console.error("添加商品失败:",y)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"商品管理"}),s.jsxs(at,{onClick:()=>c(!0),children:[s.jsx(du,{className:"h-4 w-4 mr-2"}),"添加商品"]})]}),f&&s.jsxs(mt,{children:[s.jsx(yt,{children:s.jsx(vt,{children:"添加新商品"})}),s.jsx(pt,{children:s.jsxs("form",{onSubmit:A,className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx(He,{htmlFor:"product_name",children:"商品名称"}),s.jsx(ht,{id:"product_name",value:d.product_name,onChange:v=>m({...d,product_name:v.target.value}),required:!0})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"sku",children:"SKU"}),s.jsx(ht,{id:"sku",value:d.sku,onChange:v=>m({...d,sku:v.target.value}),required:!0})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"category",children:"分类"}),s.jsx(ht,{id:"category",value:d.category,onChange:v=>m({...d,category:v.target.value})})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"brand",children:"品牌"}),s.jsx(ht,{id:"brand",value:d.brand,onChange:v=>m({...d,brand:v.target.value})})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"unit",children:"单位"}),s.jsx(ht,{id:"unit",value:d.unit,onChange:v=>m({...d,unit:v.target.value})})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"purchase_price",children:"采购价格"}),s.jsx(ht,{id:"purchase_price",type:"number",step:"0.01",value:d.purchase_price,onChange:v=>m({...d,purchase_price:v.target.value})})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"sales_price",children:"销售价格"}),s.jsx(ht,{id:"sales_price",type:"number",step:"0.01",value:d.sales_price,onChange:v=>m({...d,sales_price:v.target.value})})]}),s.jsxs("div",{className:"col-span-2 flex gap-2",children:[s.jsx(at,{type:"submit",children:"保存"}),s.jsx(at,{type:"button",variant:"outline",onClick:()=>c(!1),children:"取消"})]})]})})]}),s.jsxs(mt,{children:[s.jsx(yt,{children:s.jsx(vt,{children:"商品列表"})}),s.jsx(pt,{children:s.jsxs(Vl,{children:[s.jsx(Ql,{children:s.jsxs(ut,{children:[s.jsx(ae,{children:"商品名称"}),s.jsx(ae,{children:"SKU"}),s.jsx(ae,{children:"分类"}),s.jsx(ae,{children:"品牌"}),s.jsx(ae,{children:"单位"}),s.jsx(ae,{children:"采购价格"}),s.jsx(ae,{children:"销售价格"})]})}),s.jsx(Zl,{children:i.map(v=>s.jsxs(ut,{children:[s.jsx(ne,{children:v.product_name}),s.jsx(ne,{children:v.sku}),s.jsx(ne,{children:v.category}),s.jsx(ne,{children:v.brand}),s.jsx(ne,{children:v.unit}),s.jsxs(ne,{children:["¥",v.purchase_price]}),s.jsxs(ne,{children:["¥",v.sales_price]})]},v.product_id))})]})})]})]})}function C0(){const[i,o]=T.useState([]),[f,c]=T.useState(!1),[d,m]=T.useState({warehouse_name:"",location:"",capacity:""});T.useEffect(()=>{g()},[]);const g=async()=>{try{const y=await(await fetch(`${nt}/warehouses`)).json();y.code===200&&o(y.data)}catch(v){console.error("获取仓库列表失败:",v)}},A=async v=>{v.preventDefault();try{const j=await(await fetch(`${nt}/warehouses`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)})).json();j.code===200?(c(!1),m({warehouse_name:"",location:"",capacity:""}),g()):alert(j.message)}catch(y){console.error("添加仓库失败:",y)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"仓库管理"}),s.jsxs(at,{onClick:()=>c(!0),children:[s.jsx(du,{className:"h-4 w-4 mr-2"}),"添加仓库"]})]}),f&&s.jsxs(mt,{children:[s.jsx(yt,{children:s.jsx(vt,{children:"添加新仓库"})}),s.jsx(pt,{children:s.jsxs("form",{onSubmit:A,className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx(He,{htmlFor:"warehouse_name",children:"仓库名称"}),s.jsx(ht,{id:"warehouse_name",value:d.warehouse_name,onChange:v=>m({...d,warehouse_name:v.target.value}),required:!0})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"location",children:"位置"}),s.jsx(ht,{id:"location",value:d.location,onChange:v=>m({...d,location:v.target.value})})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"capacity",children:"容量 (立方米)"}),s.jsx(ht,{id:"capacity",type:"number",step:"0.01",value:d.capacity,onChange:v=>m({...d,capacity:v.target.value})})]}),s.jsxs("div",{className:"col-span-2 flex gap-2",children:[s.jsx(at,{type:"submit",children:"保存"}),s.jsx(at,{type:"button",variant:"outline",onClick:()=>c(!1),children:"取消"})]})]})})]}),s.jsxs(mt,{children:[s.jsx(yt,{children:s.jsx(vt,{children:"仓库列表"})}),s.jsx(pt,{children:s.jsxs(Vl,{children:[s.jsx(Ql,{children:s.jsxs(ut,{children:[s.jsx(ae,{children:"仓库名称"}),s.jsx(ae,{children:"位置"}),s.jsx(ae,{children:"容量 (立方米)"})]})}),s.jsx(Zl,{children:i.map(v=>s.jsxs(ut,{children:[s.jsx(ne,{children:v.warehouse_name}),s.jsx(ne,{children:v.location}),s.jsx(ne,{children:v.capacity})]},v.warehouse_id))})]})})]})]})}function U0(){const[i,o]=T.useState([]),[f,c]=T.useState(!1),[d,m]=T.useState([]),[g,A]=T.useState([]),[v,y]=T.useState([]),[j,U]=T.useState({supplier_id:"",warehouse_id:"",order_type:"purchase",items:[]}),[O,X]=T.useState({product_id:"",quantity:"",unit_price:""});T.useEffect(()=>{D(),G(),Q(),Y()},[]);const D=async()=>{try{const V=await(await fetch(`${nt}/inbound`)).json();V.code===200&&o(V.data)}catch(S){console.error("获取入库单列表失败:",S)}},G=async()=>{try{const V=await(await fetch(`${nt}/products`)).json();V.code===200&&m(V.data)}catch(S){console.error("获取商品列表失败:",S)}},Q=async()=>{try{const V=await(await fetch(`${nt}/warehouses`)).json();V.code===200&&A(V.data)}catch(S){console.error("获取仓库列表失败:",S)}},Y=async()=>{try{const V=await(await fetch(`${nt}/suppliers`)).json();V.code===200&&y(V.data)}catch(S){console.error("获取供应商列表失败:",S)}},Z=()=>{if(O.product_id&&O.quantity&&O.unit_price){const S=d.find(V=>V.product_id===parseInt(O.product_id));U({...j,items:[...j.items,{...O,product_name:(S==null?void 0:S.product_name)||"",sku:(S==null?void 0:S.sku)||""}]}),X({product_id:"",quantity:"",unit_price:""})}},I=S=>{const V=j.items.filter((ye,J)=>J!==S);U({...j,items:V})},ce=async S=>{if(S.preventDefault(),j.items.length===0){alert("请至少添加一个商品");return}try{const ye=await(await fetch(`${nt}/inbound`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(j)})).json();ye.code===200?(c(!1),U({supplier_id:"",warehouse_id:"",order_type:"purchase",items:[]}),D()):alert(ye.message)}catch(V){console.error("创建入库单失败:",V)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"入库管理"}),s.jsxs(at,{onClick:()=>c(!0),children:[s.jsx(du,{className:"h-4 w-4 mr-2"}),"创建入库单"]})]}),f&&s.jsxs(mt,{children:[s.jsx(yt,{children:s.jsx(vt,{children:"创建入库单"})}),s.jsx(pt,{children:s.jsxs("form",{onSubmit:ce,className:"space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsx(He,{htmlFor:"order_type",children:"入库类型"}),s.jsxs("select",{id:"order_type",className:"w-full p-2 border rounded",value:j.order_type,onChange:S=>U({...j,order_type:S.target.value}),children:[s.jsx("option",{value:"purchase",children:"采购入库"}),s.jsx("option",{value:"return",children:"退货入库"}),s.jsx("option",{value:"transfer",children:"调拨入库"})]})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"supplier_id",children:"供应商"}),s.jsxs("select",{id:"supplier_id",className:"w-full p-2 border rounded",value:j.supplier_id,onChange:S=>U({...j,supplier_id:S.target.value}),required:!0,children:[s.jsx("option",{value:"",children:"请选择供应商"}),v.map(S=>s.jsx("option",{value:S.supplier_id,children:S.supplier_name},S.supplier_id))]})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"warehouse_id",children:"目标仓库"}),s.jsxs("select",{id:"warehouse_id",className:"w-full p-2 border rounded",value:j.warehouse_id,onChange:S=>U({...j,warehouse_id:S.target.value}),required:!0,children:[s.jsx("option",{value:"",children:"请选择仓库"}),g.map(S=>s.jsx("option",{value:S.warehouse_id,children:S.warehouse_name},S.warehouse_id))]})]})]}),s.jsxs("div",{className:"border-t pt-4",children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"添加商品"}),s.jsxs("div",{className:"grid grid-cols-4 gap-4 items-end",children:[s.jsxs("div",{children:[s.jsx(He,{htmlFor:"product_id",children:"商品"}),s.jsxs("select",{id:"product_id",className:"w-full p-2 border rounded",value:O.product_id,onChange:S=>X({...O,product_id:S.target.value}),children:[s.jsx("option",{value:"",children:"请选择商品"}),d.map(S=>s.jsxs("option",{value:S.product_id,children:[S.product_name," (",S.sku,")"]},S.product_id))]})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"quantity",children:"数量"}),s.jsx(ht,{id:"quantity",type:"number",value:O.quantity,onChange:S=>X({...O,quantity:S.target.value})})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"unit_price",children:"单价"}),s.jsx(ht,{id:"unit_price",type:"number",step:"0.01",value:O.unit_price,onChange:S=>X({...O,unit_price:S.target.value})})]}),s.jsx("div",{children:s.jsx(at,{type:"button",onClick:Z,children:"添加"})})]})]}),j.items.length>0&&s.jsxs("div",{className:"border-t pt-4",children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"商品明细"}),s.jsxs(Vl,{children:[s.jsx(Ql,{children:s.jsxs(ut,{children:[s.jsx(ae,{children:"商品名称"}),s.jsx(ae,{children:"SKU"}),s.jsx(ae,{children:"数量"}),s.jsx(ae,{children:"单价"}),s.jsx(ae,{children:"小计"}),s.jsx(ae,{children:"操作"})]})}),s.jsx(Zl,{children:j.items.map((S,V)=>s.jsxs(ut,{children:[s.jsx(ne,{children:S.product_name}),s.jsx(ne,{children:S.sku}),s.jsx(ne,{children:S.quantity}),s.jsxs(ne,{children:["¥",S.unit_price]}),s.jsxs(ne,{children:["¥",(S.quantity*S.unit_price).toFixed(2)]}),s.jsx(ne,{children:s.jsx(at,{type:"button",variant:"destructive",size:"sm",onClick:()=>I(V),children:"删除"})})]},V))})]}),s.jsx("div",{className:"text-right mt-4",children:s.jsxs("strong",{children:["总金额: ¥",j.items.reduce((S,V)=>S+V.quantity*V.unit_price,0).toFixed(2)]})})]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx(at,{type:"submit",children:"创建入库单"}),s.jsx(at,{type:"button",variant:"outline",onClick:()=>c(!1),children:"取消"})]})]})})]}),s.jsxs(mt,{children:[s.jsx(yt,{children:s.jsx(vt,{children:"入库单列表"})}),s.jsx(pt,{children:s.jsxs(Vl,{children:[s.jsx(Ql,{children:s.jsxs(ut,{children:[s.jsx(ae,{children:"入库单号"}),s.jsx(ae,{children:"入库类型"}),s.jsx(ae,{children:"供应商"}),s.jsx(ae,{children:"目标仓库"}),s.jsx(ae,{children:"总金额"}),s.jsx(ae,{children:"状态"}),s.jsx(ae,{children:"创建时间"})]})}),s.jsx(Zl,{children:i.map(S=>s.jsxs(ut,{children:[s.jsx(ne,{children:S.order_number}),s.jsx(ne,{children:s.jsx(ha,{variant:"outline",children:S.order_type==="purchase"?"采购入库":S.order_type==="return"?"退货入库":"调拨入库"})}),s.jsx(ne,{children:S.supplier_name}),s.jsx(ne,{children:S.warehouse_name}),s.jsxs(ne,{children:["¥",S.total_amount]}),s.jsx(ne,{children:s.jsx(ha,{variant:S.status==="confirmed"?"default":"secondary",children:S.status==="pending"?"待确认":"已确认"})}),s.jsx(ne,{children:new Date(S.created_at).toLocaleDateString()})]},S.inbound_id))})]})})]})]})}function H0(){const[i,o]=T.useState([]),[f,c]=T.useState(!1),[d,m]=T.useState([]),[g,A]=T.useState([]),[v,y]=T.useState([]),[j,U]=T.useState({customer_id:"",warehouse_id:"",order_type:"sales",items:[]}),[O,X]=T.useState({product_id:"",quantity:"",unit_price:""});T.useEffect(()=>{D(),G(),Q(),Y()},[]);const D=async()=>{try{const V=await(await fetch(`${nt}/outbound`)).json();V.code===200&&o(V.data)}catch(S){console.error("获取出库单列表失败:",S)}},G=async()=>{try{const V=await(await fetch(`${nt}/products`)).json();V.code===200&&m(V.data)}catch(S){console.error("获取商品列表失败:",S)}},Q=async()=>{try{const V=await(await fetch(`${nt}/warehouses`)).json();V.code===200&&A(V.data)}catch(S){console.error("获取仓库列表失败:",S)}},Y=async()=>{try{const V=await(await fetch(`${nt}/customers`)).json();V.code===200&&y(V.data)}catch(S){console.error("获取客户列表失败:",S)}},Z=()=>{if(O.product_id&&O.quantity&&O.unit_price){const S=d.find(V=>V.product_id===parseInt(O.product_id));U({...j,items:[...j.items,{...O,product_name:(S==null?void 0:S.product_name)||"",sku:(S==null?void 0:S.sku)||""}]}),X({product_id:"",quantity:"",unit_price:""})}},I=S=>{const V=j.items.filter((ye,J)=>J!==S);U({...j,items:V})},ce=async S=>{if(S.preventDefault(),j.items.length===0){alert("请至少添加一个商品");return}try{const ye=await(await fetch(`${nt}/outbound`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(j)})).json();ye.code===200?(c(!1),U({customer_id:"",warehouse_id:"",order_type:"sales",items:[]}),D()):alert(ye.message)}catch(V){console.error("创建出库单失败:",V)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"出库管理"}),s.jsxs(at,{onClick:()=>c(!0),children:[s.jsx(du,{className:"h-4 w-4 mr-2"}),"创建出库单"]})]}),f&&s.jsxs(mt,{children:[s.jsx(yt,{children:s.jsx(vt,{children:"创建出库单"})}),s.jsx(pt,{children:s.jsxs("form",{onSubmit:ce,className:"space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsx(He,{htmlFor:"order_type",children:"出库类型"}),s.jsxs("select",{id:"order_type",className:"w-full p-2 border rounded",value:j.order_type,onChange:S=>U({...j,order_type:S.target.value}),children:[s.jsx("option",{value:"sales",children:"销售出库"}),s.jsx("option",{value:"transfer",children:"调拨出库"}),s.jsx("option",{value:"damage",children:"报损出库"})]})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"customer_id",children:"客户"}),s.jsxs("select",{id:"customer_id",className:"w-full p-2 border rounded",value:j.customer_id,onChange:S=>U({...j,customer_id:S.target.value}),required:!0,children:[s.jsx("option",{value:"",children:"请选择客户"}),v.map(S=>s.jsx("option",{value:S.customer_id,children:S.customer_name},S.customer_id))]})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"warehouse_id",children:"出库仓库"}),s.jsxs("select",{id:"warehouse_id",className:"w-full p-2 border rounded",value:j.warehouse_id,onChange:S=>U({...j,warehouse_id:S.target.value}),required:!0,children:[s.jsx("option",{value:"",children:"请选择仓库"}),g.map(S=>s.jsx("option",{value:S.warehouse_id,children:S.warehouse_name},S.warehouse_id))]})]})]}),s.jsxs("div",{className:"border-t pt-4",children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"添加商品"}),s.jsxs("div",{className:"grid grid-cols-4 gap-4 items-end",children:[s.jsxs("div",{children:[s.jsx(He,{htmlFor:"product_id",children:"商品"}),s.jsxs("select",{id:"product_id",className:"w-full p-2 border rounded",value:O.product_id,onChange:S=>X({...O,product_id:S.target.value}),children:[s.jsx("option",{value:"",children:"请选择商品"}),d.map(S=>s.jsxs("option",{value:S.product_id,children:[S.product_name," (",S.sku,")"]},S.product_id))]})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"quantity",children:"数量"}),s.jsx(ht,{id:"quantity",type:"number",value:O.quantity,onChange:S=>X({...O,quantity:S.target.value})})]}),s.jsxs("div",{children:[s.jsx(He,{htmlFor:"unit_price",children:"单价"}),s.jsx(ht,{id:"unit_price",type:"number",step:"0.01",value:O.unit_price,onChange:S=>X({...O,unit_price:S.target.value})})]}),s.jsx("div",{children:s.jsx(at,{type:"button",onClick:Z,children:"添加"})})]})]}),j.items.length>0&&s.jsxs("div",{className:"border-t pt-4",children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"商品明细"}),s.jsxs(Vl,{children:[s.jsx(Ql,{children:s.jsxs(ut,{children:[s.jsx(ae,{children:"商品名称"}),s.jsx(ae,{children:"SKU"}),s.jsx(ae,{children:"数量"}),s.jsx(ae,{children:"单价"}),s.jsx(ae,{children:"小计"}),s.jsx(ae,{children:"操作"})]})}),s.jsx(Zl,{children:j.items.map((S,V)=>s.jsxs(ut,{children:[s.jsx(ne,{children:S.product_name}),s.jsx(ne,{children:S.sku}),s.jsx(ne,{children:S.quantity}),s.jsxs(ne,{children:["¥",S.unit_price]}),s.jsxs(ne,{children:["¥",(S.quantity*S.unit_price).toFixed(2)]}),s.jsx(ne,{children:s.jsx(at,{type:"button",variant:"destructive",size:"sm",onClick:()=>I(V),children:"删除"})})]},V))})]}),s.jsx("div",{className:"text-right mt-4",children:s.jsxs("strong",{children:["总金额: ¥",j.items.reduce((S,V)=>S+V.quantity*V.unit_price,0).toFixed(2)]})})]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx(at,{type:"submit",children:"创建出库单"}),s.jsx(at,{type:"button",variant:"outline",onClick:()=>c(!1),children:"取消"})]})]})})]}),s.jsxs(mt,{children:[s.jsx(yt,{children:s.jsx(vt,{children:"出库单列表"})}),s.jsx(pt,{children:s.jsxs(Vl,{children:[s.jsx(Ql,{children:s.jsxs(ut,{children:[s.jsx(ae,{children:"出库单号"}),s.jsx(ae,{children:"出库类型"}),s.jsx(ae,{children:"客户"}),s.jsx(ae,{children:"出库仓库"}),s.jsx(ae,{children:"总金额"}),s.jsx(ae,{children:"状态"}),s.jsx(ae,{children:"创建时间"})]})}),s.jsx(Zl,{children:i.map(S=>s.jsxs(ut,{children:[s.jsx(ne,{children:S.order_number}),s.jsx(ne,{children:s.jsx(ha,{variant:"outline",children:S.order_type==="sales"?"销售出库":S.order_type==="transfer"?"调拨出库":"报损出库"})}),s.jsx(ne,{children:S.customer_name}),s.jsx(ne,{children:S.warehouse_name}),s.jsxs(ne,{children:["¥",S.total_amount]}),s.jsx(ne,{children:s.jsx(ha,{variant:S.status==="confirmed"?"default":"secondary",children:S.status==="pending"?"待确认":"已确认"})}),s.jsx(ne,{children:new Date(S.created_at).toLocaleDateString()})]},S.outbound_id))})]})})]})]})}function q0(){const[i,o]=T.useState([]),[f,c]=T.useState(!1);T.useEffect(()=>{d()},[]);const d=async()=>{c(!0);try{const g=await(await fetch(`${nt}/inventory`)).json();g.code===200&&o(g.data)}catch(m){console.error("获取库存失败:",m)}finally{c(!1)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"库存查询"}),s.jsxs(mt,{children:[s.jsxs(yt,{children:[s.jsx(vt,{children:"实时库存"}),s.jsx(ym,{children:"查看所有商品的实时库存情况"})]}),s.jsx(pt,{children:f?s.jsx("div",{className:"text-center py-4",children:"加载中..."}):s.jsxs(Vl,{children:[s.jsx(Ql,{children:s.jsxs(ut,{children:[s.jsx(ae,{children:"商品名称"}),s.jsx(ae,{children:"SKU"}),s.jsx(ae,{children:"仓库"}),s.jsx(ae,{children:"总库存"}),s.jsx(ae,{children:"可用库存"}),s.jsx(ae,{children:"锁定库存"}),s.jsx(ae,{children:"预警阈值"}),s.jsx(ae,{children:"状态"})]})}),s.jsx(Zl,{children:i.map(m=>s.jsxs(ut,{children:[s.jsx(ne,{children:m.product_name}),s.jsx(ne,{children:m.sku}),s.jsx(ne,{children:m.warehouse_name}),s.jsx(ne,{children:m.quantity}),s.jsx(ne,{children:m.available_quantity}),s.jsx(ne,{children:m.locked_quantity}),s.jsx(ne,{children:m.warning_threshold}),s.jsx(ne,{children:m.available_quantity<=m.warning_threshold?s.jsx(ha,{variant:"destructive",children:"库存不足"}):s.jsx(ha,{variant:"default",children:"正常"})})]},m.inventory_id))})]})})]})]})}function B0(){const[i,o]=T.useState([]),[f,c]=T.useState(!1);T.useEffect(()=>{d()},[]);const d=async()=>{c(!0);try{const g=await(await fetch(`${nt}/inventory/warnings`)).json();g.code===200&&o(g.data)}catch(m){console.error("获取预警信息失败:",m)}finally{c(!1)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"库存预警"}),i.length>0&&s.jsxs(y0,{children:[s.jsx(bs,{className:"h-4 w-4"}),s.jsxs(v0,{children:["发现 ",i.length," 个商品库存不足，请及时补货！"]})]}),s.jsxs(mt,{children:[s.jsxs(yt,{children:[s.jsx(vt,{children:"预警列表"}),s.jsx(ym,{children:"库存低于预警阈值的商品"})]}),s.jsx(pt,{children:f?s.jsx("div",{className:"text-center py-4",children:"加载中..."}):i.length===0?s.jsx("div",{className:"text-center py-8 text-gray-500",children:"暂无库存预警"}):s.jsxs(Vl,{children:[s.jsx(Ql,{children:s.jsxs(ut,{children:[s.jsx(ae,{children:"商品名称"}),s.jsx(ae,{children:"SKU"}),s.jsx(ae,{children:"仓库"}),s.jsx(ae,{children:"当前库存"}),s.jsx(ae,{children:"预警阈值"}),s.jsx(ae,{children:"预警信息"})]})}),s.jsx(Zl,{children:i.map((m,g)=>s.jsxs(ut,{children:[s.jsx(ne,{children:m.product_name}),s.jsx(ne,{children:m.sku}),s.jsx(ne,{children:m.warehouse_name}),s.jsx(ne,{className:"text-red-600 font-medium",children:m.current_quantity}),s.jsx(ne,{children:m.warning_threshold}),s.jsx(ne,{children:s.jsx(ha,{variant:"destructive",children:m.message})})]},g))})]})})]})]})}function Y0(){return s.jsx(dg,{children:s.jsxs("div",{className:"min-h-screen bg-gray-100",children:[s.jsx(N0,{}),s.jsx("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:s.jsxs(Xp,{children:[s.jsx(kl,{path:"/",element:s.jsx(M0,{})}),s.jsx(kl,{path:"/products",element:s.jsx(D0,{})}),s.jsx(kl,{path:"/warehouses",element:s.jsx(C0,{})}),s.jsx(kl,{path:"/inventory",element:s.jsx(q0,{})}),s.jsx(kl,{path:"/inbound",element:s.jsx(U0,{})}),s.jsx(kl,{path:"/outbound",element:s.jsx(H0,{})}),s.jsx(kl,{path:"/warnings",element:s.jsx(B0,{})})]})})]})})}Wv.createRoot(document.getElementById("root")).render(s.jsx(T.StrictMode,{children:s.jsx(Y0,{})}));
