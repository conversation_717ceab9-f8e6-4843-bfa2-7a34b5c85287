export type Config = import("eslint").Linter.Config;
export type LegacyConfig = import("eslint").Linter.LegacyConfig;
export type Plugin = import("eslint").ESLint.Plugin;
export type RuleEntry = import("eslint").Linter.RuleEntry;
export type ExtendsElement = import("./types.cts").ExtendsElement;
export type SimpleExtendsElement = import("./types.cts").SimpleExtendsElement;
export type ConfigWithExtends = import("./types.cts").ConfigWithExtends;
export type InfiniteConfigArray = import("./types.cts").InfiniteArray<Config>;
export type ConfigWithExtendsArray = import("./types.cts").ConfigWithExtendsArray;
/**
 * Helper function to define a config array.
 * @param {ConfigWithExtendsArray} args The arguments to the function.
 * @returns {Config[]} The config array.
 */
export function defineConfig(...args: ConfigWithExtendsArray): Config[];
/**
 * Creates a global ignores config with the given patterns.
 * @param {string[]} ignorePatterns The ignore patterns.
 * @param {string} [name] The name of the global ignores config.
 * @returns {Config} The global ignores config.
 */
export function globalIgnores(ignorePatterns: string[], name?: string): Config;
