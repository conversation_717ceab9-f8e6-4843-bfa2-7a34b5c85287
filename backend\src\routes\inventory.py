from flask import Blueprint, request, jsonify
from src.models.inventory import db, Product, Warehouse, Inventory, Supplier, Customer, InboundOrder, InboundOrderItem, OutboundOrder, OutboundOrderItem
from datetime import datetime
import uuid

inventory_bp = Blueprint('inventory', __name__)

def generate_unique_id(prefix):
    """生成唯一ID"""
    return f"{prefix}{datetime.now().strftime('%Y%m%d%H%M%S')}{str(uuid.uuid4())[:8]}"

# 商品管理API
@inventory_bp.route('/products', methods=['GET'])
def get_products():
    """获取商品列表"""
    products = Product.query.all()
    result = []
    for product in products:
        result.append({
            'product_id': product.product_id,
            'product_name': product.product_name,
            'sku': product.sku,
            'category': product.category,
            'brand': product.brand,
            'unit': product.unit,
            'purchase_price': float(product.purchase_price) if product.purchase_price else None,
            'sales_price': float(product.sales_price) if product.sales_price else None
        })
    return jsonify({'code': 200, 'data': result})

@inventory_bp.route('/products', methods=['POST'])
def create_product():
    """创建商品"""
    data = request.json
    
    # 检查SKU是否已存在
    existing_product = Product.query.filter_by(sku=data.get('sku')).first()
    if existing_product:
        return jsonify({'code': 400, 'message': 'SKU已存在'})
    
    product = Product(
        product_id=generate_unique_id('P'),
        product_name=data.get('product_name'),
        sku=data.get('sku'),
        category=data.get('category'),
        brand=data.get('brand'),
        unit=data.get('unit'),
        purchase_price=data.get('purchase_price'),
        sales_price=data.get('sales_price')
    )
    
    db.session.add(product)
    db.session.commit()
    
    return jsonify({'code': 200, 'message': '商品创建成功', 'product_id': product.product_id})

# 仓库管理API
@inventory_bp.route('/warehouses', methods=['GET'])
def get_warehouses():
    """获取仓库列表"""
    warehouses = Warehouse.query.all()
    result = []
    for warehouse in warehouses:
        result.append({
            'warehouse_id': warehouse.warehouse_id,
            'warehouse_name': warehouse.warehouse_name,
            'location': warehouse.location,
            'capacity': float(warehouse.capacity) if warehouse.capacity else None
        })
    return jsonify({'code': 200, 'data': result})

@inventory_bp.route('/warehouses', methods=['POST'])
def create_warehouse():
    """创建仓库"""
    data = request.json
    
    warehouse = Warehouse(
        warehouse_id=generate_unique_id('W'),
        warehouse_name=data.get('warehouse_name'),
        location=data.get('location'),
        capacity=data.get('capacity')
    )
    
    db.session.add(warehouse)
    db.session.commit()
    
    return jsonify({'code': 200, 'message': '仓库创建成功', 'warehouse_id': warehouse.warehouse_id})

# 库存查询API
@inventory_bp.route('/inventory', methods=['GET'])
def get_inventory():
    """查询库存"""
    product_id = request.args.get('product_id')
    warehouse_id = request.args.get('warehouse_id')
    
    query = db.session.query(Inventory, Product, Warehouse).join(Product).join(Warehouse)
    
    if product_id:
        query = query.filter(Inventory.product_id == product_id)
    if warehouse_id:
        query = query.filter(Inventory.warehouse_id == warehouse_id)
    
    results = query.all()
    
    data = []
    for inventory, product, warehouse in results:
        data.append({
            'inventory_id': inventory.inventory_id,
            'product_name': product.product_name,
            'sku': product.sku,
            'warehouse_name': warehouse.warehouse_name,
            'quantity': inventory.quantity,
            'available_quantity': inventory.available_quantity,
            'locked_quantity': inventory.locked_quantity,
            'warning_threshold': inventory.warning_threshold,
            'last_updated': inventory.last_updated.isoformat() if inventory.last_updated else None
        })
    
    return jsonify({'code': 200, 'data': data})

# 入库管理API
@inventory_bp.route('/inbound', methods=['GET'])
def get_inbound_orders():
    """获取入库单列表"""
    inbound_orders = db.session.query(InboundOrder, Supplier, Warehouse).outerjoin(Supplier).join(Warehouse).all()
    result = []
    for inbound_order, supplier, warehouse in inbound_orders:
        # 计算总金额
        total_amount = 0
        for item in inbound_order.items:
            if item.unit_price and item.quantity:
                total_amount += float(item.unit_price) * item.quantity

        result.append({
            'inbound_order_id': inbound_order.inbound_order_id,
            'order_number': inbound_order.inbound_order_id,
            'order_type': inbound_order.order_type,
            'supplier_name': supplier.supplier_name if supplier else '无',
            'warehouse_name': warehouse.warehouse_name,
            'total_amount': total_amount,
            'status': inbound_order.status,
            'created_at': inbound_order.created_at.isoformat() if inbound_order.created_at else None
        })
    return jsonify({'code': 200, 'data': result})

@inventory_bp.route('/inbound', methods=['POST'])
def create_inbound_order():
    """创建入库单"""
    data = request.json
    
    # 创建入库单
    inbound_order = InboundOrder(
        inbound_order_id=generate_unique_id('INB'),
        order_type=data.get('order_type', '采购入库'),
        supplier_id=data.get('supplier_id'),
        warehouse_id=data.get('warehouse_id'),
        remarks=data.get('remarks')
    )
    
    db.session.add(inbound_order)
    
    # 创建入库明细
    for item_data in data.get('items', []):
        item = InboundOrderItem(
            inbound_order_item_id=generate_unique_id('INI'),
            inbound_order_id=inbound_order.inbound_order_id,
            product_id=item_data.get('product_id'),
            quantity=item_data.get('quantity'),
            unit_price=item_data.get('unit_price'),
            batch_number=item_data.get('batch_number')
        )
        db.session.add(item)
    
    db.session.commit()
    
    return jsonify({'code': 200, 'message': '入库单创建成功', 'inbound_order_id': inbound_order.inbound_order_id})

@inventory_bp.route('/inbound/<inbound_order_id>/confirm', methods=['POST'])
def confirm_inbound_order(inbound_order_id):
    """确认入库"""
    inbound_order = InboundOrder.query.get(inbound_order_id)
    if not inbound_order:
        return jsonify({'code': 404, 'message': '入库单不存在'})
    
    if inbound_order.status != '待入库':
        return jsonify({'code': 400, 'message': '入库单状态不正确'})
    
    # 更新库存
    for item in inbound_order.items:
        inventory = Inventory.query.filter_by(
            product_id=item.product_id,
            warehouse_id=inbound_order.warehouse_id
        ).first()
        
        if inventory:
            inventory.quantity += item.quantity
            inventory.available_quantity += item.quantity
            inventory.last_updated = datetime.utcnow()
        else:
            # 创建新的库存记录
            inventory = Inventory(
                inventory_id=generate_unique_id('INV'),
                product_id=item.product_id,
                warehouse_id=inbound_order.warehouse_id,
                quantity=item.quantity,
                available_quantity=item.quantity,
                locked_quantity=0
            )
            db.session.add(inventory)
    
    # 更新入库单状态
    inbound_order.status = '已入库'
    db.session.commit()
    
    return jsonify({'code': 200, 'message': '入库确认成功'})

# 出库管理API
@inventory_bp.route('/outbound', methods=['GET'])
def get_outbound_orders():
    """获取出库单列表"""
    outbound_orders = db.session.query(OutboundOrder, Customer, Warehouse).outerjoin(Customer).join(Warehouse).all()
    result = []
    for outbound_order, customer, warehouse in outbound_orders:
        # 计算总金额
        total_amount = 0
        for item in outbound_order.items:
            if item.unit_price and item.quantity:
                total_amount += float(item.unit_price) * item.quantity

        result.append({
            'outbound_order_id': outbound_order.outbound_order_id,
            'order_number': outbound_order.outbound_order_id,
            'order_type': outbound_order.order_type,
            'customer_name': customer.customer_name if customer else '无',
            'warehouse_name': warehouse.warehouse_name,
            'total_amount': total_amount,
            'status': outbound_order.status,
            'created_at': outbound_order.created_at.isoformat() if outbound_order.created_at else None
        })
    return jsonify({'code': 200, 'data': result})

@inventory_bp.route('/outbound', methods=['POST'])
def create_outbound_order():
    """创建出库单"""
    data = request.json
    
    # 检查库存
    for item_data in data.get('items', []):
        inventory = Inventory.query.filter_by(
            product_id=item_data.get('product_id'),
            warehouse_id=data.get('warehouse_id')
        ).first()
        
        if not inventory or inventory.available_quantity < item_data.get('quantity'):
            product = Product.query.get(item_data.get('product_id'))
            product_name = product.product_name if product else '未知商品'
            return jsonify({'code': 400, 'message': f'商品 {product_name} 库存不足'})
    
    # 创建出库单
    outbound_order = OutboundOrder(
        outbound_order_id=generate_unique_id('OUT'),
        order_type=data.get('order_type', '销售出库'),
        customer_id=data.get('customer_id'),
        warehouse_id=data.get('warehouse_id'),
        remarks=data.get('remarks')
    )
    
    db.session.add(outbound_order)
    
    # 创建出库明细并锁定库存
    for item_data in data.get('items', []):
        item = OutboundOrderItem(
            outbound_order_item_id=generate_unique_id('OUTI'),
            outbound_order_id=outbound_order.outbound_order_id,
            product_id=item_data.get('product_id'),
            quantity=item_data.get('quantity'),
            unit_price=item_data.get('unit_price')
        )
        db.session.add(item)
        
        # 锁定库存
        inventory = Inventory.query.filter_by(
            product_id=item_data.get('product_id'),
            warehouse_id=data.get('warehouse_id')
        ).first()
        
        inventory.available_quantity -= item_data.get('quantity')
        inventory.locked_quantity += item_data.get('quantity')
        inventory.last_updated = datetime.utcnow()
    
    db.session.commit()
    
    return jsonify({'code': 200, 'message': '出库单创建成功', 'outbound_order_id': outbound_order.outbound_order_id})

@inventory_bp.route('/outbound/<outbound_order_id>/confirm', methods=['POST'])
def confirm_outbound_order(outbound_order_id):
    """确认出库"""
    outbound_order = OutboundOrder.query.get(outbound_order_id)
    if not outbound_order:
        return jsonify({'code': 404, 'message': '出库单不存在'})
    
    if outbound_order.status != '待出库':
        return jsonify({'code': 400, 'message': '出库单状态不正确'})
    
    # 更新库存
    for item in outbound_order.items:
        inventory = Inventory.query.filter_by(
            product_id=item.product_id,
            warehouse_id=outbound_order.warehouse_id
        ).first()
        
        if inventory:
            inventory.quantity -= item.quantity
            inventory.locked_quantity -= item.quantity
            inventory.last_updated = datetime.utcnow()
    
    # 更新出库单状态
    outbound_order.status = '已出库'
    db.session.commit()
    
    return jsonify({'code': 200, 'message': '出库确认成功'})

# 供应商管理API
@inventory_bp.route('/suppliers', methods=['GET'])
def get_suppliers():
    """获取供应商列表"""
    suppliers = Supplier.query.all()
    result = []
    for supplier in suppliers:
        result.append({
            'supplier_id': supplier.supplier_id,
            'supplier_name': supplier.supplier_name,
            'contact_person': supplier.contact_person,
            'contact_phone': supplier.contact_phone,
            'address': supplier.address
        })
    return jsonify({'code': 200, 'data': result})

@inventory_bp.route('/suppliers', methods=['POST'])
def create_supplier():
    """创建供应商"""
    data = request.json
    
    supplier = Supplier(
        supplier_id=generate_unique_id('S'),
        supplier_name=data.get('supplier_name'),
        contact_person=data.get('contact_person'),
        contact_phone=data.get('contact_phone'),
        address=data.get('address')
    )
    
    db.session.add(supplier)
    db.session.commit()
    
    return jsonify({'code': 200, 'message': '供应商创建成功', 'supplier_id': supplier.supplier_id})

# 客户管理API
@inventory_bp.route('/customers', methods=['GET'])
def get_customers():
    """获取客户列表"""
    customers = Customer.query.all()
    result = []
    for customer in customers:
        result.append({
            'customer_id': customer.customer_id,
            'customer_name': customer.customer_name,
            'contact_phone': customer.contact_phone,
            'shipping_address': customer.shipping_address
        })
    return jsonify({'code': 200, 'data': result})

@inventory_bp.route('/customers', methods=['POST'])
def create_customer():
    """创建客户"""
    data = request.json
    
    customer = Customer(
        customer_id=generate_unique_id('C'),
        customer_name=data.get('customer_name'),
        contact_phone=data.get('contact_phone'),
        shipping_address=data.get('shipping_address')
    )
    
    db.session.add(customer)
    db.session.commit()
    
    return jsonify({'code': 200, 'message': '客户创建成功', 'customer_id': customer.customer_id})

# 库存预警API
@inventory_bp.route('/inventory/warnings', methods=['GET'])
def get_inventory_warnings():
    """获取库存预警"""
    warnings = db.session.query(Inventory, Product, Warehouse).join(Product).join(Warehouse).filter(
        Inventory.available_quantity <= Inventory.warning_threshold
    ).all()
    
    result = []
    for inventory, product, warehouse in warnings:
        result.append({
            'product_name': product.product_name,
            'sku': product.sku,
            'warehouse_name': warehouse.warehouse_name,
            'current_quantity': inventory.available_quantity,
            'warning_threshold': inventory.warning_threshold,
            'message': '库存低于预警值，请及时补货！'
        })
    
    return jsonify({'code': 200, 'data': result})

