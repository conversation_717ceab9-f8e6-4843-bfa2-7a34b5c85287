(()=>{var M;function V(G,J){var X=Object.keys(G);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(G);J&&(Z=Z.filter(function(B){return Object.getOwnPropertyDescriptor(G,B).enumerable})),X.push.apply(X,Z)}return X}function I(G){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?V(Object(X),!0).forEach(function(Z){w(G,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(X)):V(Object(X)).forEach(function(Z){Object.defineProperty(G,Z,Object.getOwnPropertyDescriptor(X,Z))})}return G}function w(G,J,X){if(J=D(J),J in G)Object.defineProperty(G,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else G[J]=X;return G}function D(G){var J=v(G,"string");return E(J)=="symbol"?J:String(J)}function v(G,J){if(E(G)!="object"||!G)return G;var X=G[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(G,J||"default");if(E(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(G)}function L(G,J){return b(G)||h(G,J)||f(G,J)||F()}function F(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(G,J){if(!G)return;if(typeof G==="string")return W(G,J);var X=Object.prototype.toString.call(G).slice(8,-1);if(X==="Object"&&G.constructor)X=G.constructor.name;if(X==="Map"||X==="Set")return Array.from(G);if(X==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(X))return W(G,J)}function W(G,J){if(J==null||J>G.length)J=G.length;for(var X=0,Z=new Array(J);X<J;X++)Z[X]=G[X];return Z}function h(G,J){var X=G==null?null:typeof Symbol!="undefined"&&G[Symbol.iterator]||G["@@iterator"];if(X!=null){var Z,B,C,U,H=[],Q=!0,q=!1;try{if(C=(X=X.call(G)).next,J===0){if(Object(X)!==X)return;Q=!1}else for(;!(Q=(Z=C.call(X)).done)&&(H.push(Z.value),H.length!==J);Q=!0);}catch(K){q=!0,B=K}finally{try{if(!Q&&X.return!=null&&(U=X.return(),Object(U)!==U))return}finally{if(q)throw B}}return H}}function b(G){if(Array.isArray(G))return G}function E(G){return E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},E(G)}var k=Object.defineProperty,wG=function G(J,X){for(var Z in X)k(J,Z,{get:X[Z],enumerable:!0,configurable:!0,set:function B(C){return X[Z]=function(){return C}}})};function Y(G){return function(J,X){if(J===1)if(X!==null&&X!==void 0&&X.addSuffix)return G.one[0].replace("{{time}}",G.one[2]);else return G.one[0].replace("{{time}}",G.one[1]);else{var Z=J%10===1&&J%100!==11;if(X!==null&&X!==void 0&&X.addSuffix)return G.other[0].replace("{{time}}",Z?G.other[3]:G.other[4]).replace("{{count}}",String(J));else return G.other[0].replace("{{time}}",Z?G.other[1]:G.other[2]).replace("{{count}}",String(J))}}}var _={lessThanXSeconds:Y({one:["maz\u0101k par {{time}}","sekundi","sekundi"],other:["maz\u0101k nek\u0101 {{count}} {{time}}","sekunde","sekundes","sekundes","sekund\u0113m"]}),xSeconds:Y({one:["1 {{time}}","sekunde","sekundes"],other:["{{count}} {{time}}","sekunde","sekundes","sekundes","sekund\u0113m"]}),halfAMinute:function G(J,X){if(X!==null&&X!==void 0&&X.addSuffix)return"pusmin\u016Btes";else return"pusmin\u016Bte"},lessThanXMinutes:Y({one:["maz\u0101k par {{time}}","min\u016Bti","min\u016Bti"],other:["maz\u0101k nek\u0101 {{count}} {{time}}","min\u016Bte","min\u016Btes","min\u016Btes","min\u016Bt\u0113m"]}),xMinutes:Y({one:["1 {{time}}","min\u016Bte","min\u016Btes"],other:["{{count}} {{time}}","min\u016Bte","min\u016Btes","min\u016Btes","min\u016Bt\u0113m"]}),aboutXHours:Y({one:["apm\u0113ram 1 {{time}}","stunda","stundas"],other:["apm\u0113ram {{count}} {{time}}","stunda","stundas","stundas","stund\u0101m"]}),xHours:Y({one:["1 {{time}}","stunda","stundas"],other:["{{count}} {{time}}","stunda","stundas","stundas","stund\u0101m"]}),xDays:Y({one:["1 {{time}}","diena","dienas"],other:["{{count}} {{time}}","diena","dienas","dienas","dien\u0101m"]}),aboutXWeeks:Y({one:["apm\u0113ram 1 {{time}}","ned\u0113\u013Ca","ned\u0113\u013Cas"],other:["apm\u0113ram {{count}} {{time}}","ned\u0113\u013Ca","ned\u0113\u013Cu","ned\u0113\u013Cas","ned\u0113\u013C\u0101m"]}),xWeeks:Y({one:["1 {{time}}","ned\u0113\u013Ca","ned\u0113\u013Cas"],other:["{{count}} {{time}}","ned\u0113\u013Ca","ned\u0113\u013Cu","ned\u0113\u013Cas","ned\u0113\u013C\u0101m"]}),aboutXMonths:Y({one:["apm\u0113ram 1 {{time}}","m\u0113nesis","m\u0113ne\u0161a"],other:["apm\u0113ram {{count}} {{time}}","m\u0113nesis","m\u0113ne\u0161i","m\u0113ne\u0161a","m\u0113ne\u0161iem"]}),xMonths:Y({one:["1 {{time}}","m\u0113nesis","m\u0113ne\u0161a"],other:["{{count}} {{time}}","m\u0113nesis","m\u0113ne\u0161i","m\u0113ne\u0161a","m\u0113ne\u0161iem"]}),aboutXYears:Y({one:["apm\u0113ram 1 {{time}}","gads","gada"],other:["apm\u0113ram {{count}} {{time}}","gads","gadi","gada","gadiem"]}),xYears:Y({one:["1 {{time}}","gads","gada"],other:["{{count}} {{time}}","gads","gadi","gada","gadiem"]}),overXYears:Y({one:["ilg\u0101k par 1 {{time}}","gadu","gadu"],other:["vair\u0101k nek\u0101 {{count}} {{time}}","gads","gadi","gada","gadiem"]}),almostXYears:Y({one:["gandr\u012Bz 1 {{time}}","gads","gada"],other:["vair\u0101k nek\u0101 {{count}} {{time}}","gads","gadi","gada","gadiem"]})},y=function G(J,X,Z){var B=_[J](X,Z);if(Z!==null&&Z!==void 0&&Z.addSuffix)if(Z.comparison&&Z.comparison>0)return"p\u0113c "+B;else return"pirms "+B;return B};function R(G){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=J.width?String(J.width):G.defaultWidth,Z=G.formats[X]||G.formats[G.defaultWidth];return Z}}var g={full:"EEEE, y. 'gada' d. MMMM",long:"y. 'gada' d. MMMM",medium:"dd.MM.y.",short:"dd.MM.y."},m={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},c={full:"{{date}} 'plkst.' {{time}}",long:"{{date}} 'plkst.' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},u={date:R({formats:g,defaultWidth:"full"}),time:R({formats:m,defaultWidth:"full"}),dateTime:R({formats:c,defaultWidth:"full"})},DG=7,p=365.2425,d=Math.pow(10,8)*24*60*60*1000,vG=-d,LG=604800000,FG=86400000,fG=60000,hG=3600000,bG=1000,kG=525600,_G=43200,yG=1440,gG=60,mG=3,cG=12,uG=4,l=3600,pG=60,S=l*24,dG=S*7,i=S*p,s=i/12,lG=s*3,$=Symbol.for("constructDateFrom");function j(G,J){if(typeof G==="function")return G(J);if(G&&E(G)==="object"&&$ in G)return G[$](J);if(G instanceof Date)return new G.constructor(J);return new Date(J)}function n(G){for(var J=arguments.length,X=new Array(J>1?J-1:0),Z=1;Z<J;Z++)X[Z-1]=arguments[Z];var B=j.bind(null,G||X.find(function(C){return E(C)==="object"}));return X.map(B)}function r(){return O}function iG(G){O=G}var O={};function o(G,J){return j(J||G,G)}function T(G,J){var X,Z,B,C,U,H,Q=r(),q=(X=(Z=(B=(C=J===null||J===void 0?void 0:J.weekStartsOn)!==null&&C!==void 0?C:J===null||J===void 0||(U=J.locale)===null||U===void 0||(U=U.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&B!==void 0?B:Q.weekStartsOn)!==null&&Z!==void 0?Z:(H=Q.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&X!==void 0?X:0,K=o(G,J===null||J===void 0?void 0:J.in),N=K.getDay(),zG=(N<q?7:0)+N-q;return K.setDate(K.getDate()-zG),K.setHours(0,0,0,0),K}function P(G,J,X){var Z=n(X===null||X===void 0?void 0:X.in,G,J),B=L(Z,2),C=B[0],U=B[1];return+T(C,X)===+T(U,X)}var z=["sv\u0113tdien\u0101","pirmdien\u0101","otrdien\u0101","tre\u0161dien\u0101","ceturtdien\u0101","piektdien\u0101","sestdien\u0101"],a={lastWeek:function G(J,X,Z){if(P(J,X,Z))return"eeee 'plkst.' p";var B=z[J.getDay()];return"'Pag\u0101ju\u0161\u0101 "+B+" plkst.' p"},yesterday:"'Vakar plkst.' p",today:"'\u0160odien plkst.' p",tomorrow:"'R\u012Bt plkst.' p",nextWeek:function G(J,X,Z){if(P(J,X,Z))return"eeee 'plkst.' p";var B=z[J.getDay()];return"'N\u0101kamaj\u0101 "+B+" plkst.' p"},other:"P"},t=function G(J,X,Z,B){var C=a[J];if(typeof C==="function")return C(X,Z,B);return C};function A(G){return function(J,X){var Z=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",B;if(Z==="formatting"&&G.formattingValues){var C=G.defaultFormattingWidth||G.defaultWidth,U=X!==null&&X!==void 0&&X.width?String(X.width):C;B=G.formattingValues[U]||G.formattingValues[C]}else{var H=G.defaultWidth,Q=X!==null&&X!==void 0&&X.width?String(X.width):G.defaultWidth;B=G.values[Q]||G.values[H]}var q=G.argumentCallback?G.argumentCallback(J):J;return B[q]}}var e={narrow:["p.m.\u0113","m.\u0113"],abbreviated:["p. m. \u0113.","m. \u0113."],wide:["pirms m\u016Bsu \u0113ras","m\u016Bsu \u0113r\u0101"]},GG={narrow:["1","2","3","4"],abbreviated:["1. cet.","2. cet.","3. cet.","4. cet."],wide:["pirmais ceturksnis","otrais ceturksnis","tre\u0161ais ceturksnis","ceturtais ceturksnis"]},JG={narrow:["1","2","3","4"],abbreviated:["1. cet.","2. cet.","3. cet.","4. cet."],wide:["pirmaj\u0101 ceturksn\u012B","otraj\u0101 ceturksn\u012B","tre\u0161aj\u0101 ceturksn\u012B","ceturtaj\u0101 ceturksn\u012B"]},XG={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","febr.","marts","apr.","maijs","j\u016Bn.","j\u016Bl.","aug.","sept.","okt.","nov.","dec."],wide:["janv\u0101ris","febru\u0101ris","marts","apr\u012Blis","maijs","j\u016Bnijs","j\u016Blijs","augusts","septembris","oktobris","novembris","decembris"]},ZG={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","febr.","mart\u0101","apr.","maijs","j\u016Bn.","j\u016Bl.","aug.","sept.","okt.","nov.","dec."],wide:["janv\u0101r\u012B","febru\u0101r\u012B","mart\u0101","apr\u012Bl\u012B","maij\u0101","j\u016Bnij\u0101","j\u016Blij\u0101","august\u0101","septembr\u012B","oktobr\u012B","novembr\u012B","decembr\u012B"]},BG={narrow:["S","P","O","T","C","P","S"],short:["Sv","P","O","T","C","Pk","S"],abbreviated:["sv\u0113td.","pirmd.","otrd.","tre\u0161d.","ceturtd.","piektd.","sestd."],wide:["sv\u0113tdiena","pirmdiena","otrdiena","tre\u0161diena","ceturtdiena","piektdiena","sestdiena"]},CG={narrow:["S","P","O","T","C","P","S"],short:["Sv","P","O","T","C","Pk","S"],abbreviated:["sv\u0113td.","pirmd.","otrd.","tre\u0161d.","ceturtd.","piektd.","sestd."],wide:["sv\u0113tdien\u0101","pirmdien\u0101","otrdien\u0101","tre\u0161dien\u0101","ceturtdien\u0101","piektdien\u0101","sestdien\u0101"]},UG={narrow:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"r\u012Bts",afternoon:"diena",evening:"vakars",night:"nakts"},abbreviated:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"r\u012Bts",afternoon:"p\u0113cpusd.",evening:"vakars",night:"nakts"},wide:{am:"am",pm:"pm",midnight:"pusnakts",noon:"pusdienlaiks",morning:"r\u012Bts",afternoon:"p\u0113cpusdiena",evening:"vakars",night:"nakts"}},HG={narrow:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"r\u012Bt\u0101",afternoon:"dien\u0101",evening:"vakar\u0101",night:"nakt\u012B"},abbreviated:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"r\u012Bt\u0101",afternoon:"p\u0113cpusd.",evening:"vakar\u0101",night:"nakt\u012B"},wide:{am:"am",pm:"pm",midnight:"pusnakt\u012B",noon:"pusdienlaik\u0101",morning:"r\u012Bt\u0101",afternoon:"p\u0113cpusdien\u0101",evening:"vakar\u0101",night:"nakt\u012B"}},YG=function G(J,X){var Z=Number(J);return Z+"."},qG={ordinalNumber:YG,era:A({values:e,defaultWidth:"wide"}),quarter:A({values:GG,defaultWidth:"wide",formattingValues:JG,defaultFormattingWidth:"wide",argumentCallback:function G(J){return J-1}}),month:A({values:XG,defaultWidth:"wide",formattingValues:ZG,defaultFormattingWidth:"wide"}),day:A({values:BG,defaultWidth:"wide",formattingValues:CG,defaultFormattingWidth:"wide"}),dayPeriod:A({values:UG,defaultWidth:"wide",formattingValues:HG,defaultFormattingWidth:"wide"})};function x(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=X.width,B=Z&&G.matchPatterns[Z]||G.matchPatterns[G.defaultMatchWidth],C=J.match(B);if(!C)return null;var U=C[0],H=Z&&G.parsePatterns[Z]||G.parsePatterns[G.defaultParseWidth],Q=Array.isArray(H)?KG(H,function(N){return N.test(U)}):QG(H,function(N){return N.test(U)}),q;q=G.valueCallback?G.valueCallback(Q):Q,q=X.valueCallback?X.valueCallback(q):q;var K=J.slice(U.length);return{value:q,rest:K}}}function QG(G,J){for(var X in G)if(Object.prototype.hasOwnProperty.call(G,X)&&J(G[X]))return X;return}function KG(G,J){for(var X=0;X<G.length;X++)if(J(G[X]))return X;return}function EG(G){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=J.match(G.matchPattern);if(!Z)return null;var B=Z[0],C=J.match(G.parsePattern);if(!C)return null;var U=G.valueCallback?G.valueCallback(C[0]):C[0];U=X.valueCallback?X.valueCallback(U):U;var H=J.slice(B.length);return{value:U,rest:H}}}var NG=/^(\d+)\./i,AG=/\d+/i,xG={narrow:/^(p\.m\.ē|m\.ē)/i,abbreviated:/^(p\. m\. ē\.|m\. ē\.)/i,wide:/^(pirms mūsu ēras|mūsu ērā)/i},IG={any:[/^p/i,/^m/i]},MG={narrow:/^[1234]/i,abbreviated:/^[1234](\. cet\.)/i,wide:/^(pirma(is|jā)|otra(is|jā)|treša(is|jā)|ceturta(is|jā)) ceturksn(is|ī)/i},RG={narrow:[/^1/i,/^2/i,/^3/i,/^4/i],abbreviated:[/^1/i,/^2/i,/^3/i,/^4/i],wide:[/^p/i,/^o/i,/^t/i,/^c/i]},VG={narrow:/^[jfmasond]/i,abbreviated:/^(janv\.|febr\.|marts|apr\.|maijs|jūn\.|jūl\.|aug\.|sept\.|okt\.|nov\.|dec\.)/i,wide:/^(janvār(is|ī)|februār(is|ī)|mart[sā]|aprīl(is|ī)|maij[sā]|jūnij[sā]|jūlij[sā]|august[sā]|septembr(is|ī)|oktobr(is|ī)|novembr(is|ī)|decembr(is|ī))/i},WG={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^jūn/i,/^jūl/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},SG={narrow:/^[spotc]/i,short:/^(sv|pi|o|t|c|pk|s)/i,abbreviated:/^(svētd\.|pirmd\.|otrd.\|trešd\.|ceturtd\.|piektd\.|sestd\.)/i,wide:/^(svētdien(a|ā)|pirmdien(a|ā)|otrdien(a|ā)|trešdien(a|ā)|ceturtdien(a|ā)|piektdien(a|ā)|sestdien(a|ā))/i},$G={narrow:[/^s/i,/^p/i,/^o/i,/^t/i,/^c/i,/^p/i,/^s/i],any:[/^sv/i,/^pi/i,/^o/i,/^t/i,/^c/i,/^p/i,/^se/i]},jG={narrow:/^(am|pm|pusn\.|pusd\.|rīt(s|ā)|dien(a|ā)|vakar(s|ā)|nakt(s|ī))/,abbreviated:/^(am|pm|pusn\.|pusd\.|rīt(s|ā)|pēcpusd\.|vakar(s|ā)|nakt(s|ī))/,wide:/^(am|pm|pusnakt(s|ī)|pusdienlaik(s|ā)|rīt(s|ā)|pēcpusdien(a|ā)|vakar(s|ā)|nakt(s|ī))/i},OG={any:{am:/^am/i,pm:/^pm/i,midnight:/^pusn/i,noon:/^pusd/i,morning:/^r/i,afternoon:/^(d|pēc)/i,evening:/^v/i,night:/^n/i}},TG={ordinalNumber:EG({matchPattern:NG,parsePattern:AG,valueCallback:function G(J){return parseInt(J,10)}}),era:x({matchPatterns:xG,defaultMatchWidth:"wide",parsePatterns:IG,defaultParseWidth:"any"}),quarter:x({matchPatterns:MG,defaultMatchWidth:"wide",parsePatterns:RG,defaultParseWidth:"wide",valueCallback:function G(J){return J+1}}),month:x({matchPatterns:VG,defaultMatchWidth:"wide",parsePatterns:WG,defaultParseWidth:"any"}),day:x({matchPatterns:SG,defaultMatchWidth:"wide",parsePatterns:$G,defaultParseWidth:"any"}),dayPeriod:x({matchPatterns:jG,defaultMatchWidth:"wide",parsePatterns:OG,defaultParseWidth:"any"})},PG={code:"lv",formatDistance:y,formatLong:u,formatRelative:t,localize:qG,match:TG,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=I(I({},window.dateFns),{},{locale:I(I({},(M=window.dateFns)===null||M===void 0?void 0:M.locale),{},{lv:PG})})})();

//# debugId=738C85F93BE122E664756E2164756E21
