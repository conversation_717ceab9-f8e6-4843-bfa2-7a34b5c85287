# 电商库存管理系统 API 文档

## 概述

本文档描述了电商库存管理系统的后端API接口，所有接口均返回中文响应信息。

### 基础信息

- **基础URL**: `http://localhost:5000/api`
- **响应格式**: JSON
- **字符编码**: UTF-8

### 统一响应格式

所有API接口都遵循统一的响应格式：

#### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "success": true,
  "data": {...}
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "success": false
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## API 接口列表

### 1. 商品管理

#### 1.1 获取商品列表
- **接口**: `GET /products`
- **描述**: 获取所有商品信息
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取商品列表成功",
  "success": true,
  "data": [
    {
      "product_id": "P20250624215951b264f8ba",
      "product_name": "华为Mate60 Pro",
      "sku": "HW-MATE60-PRO",
      "category": "智能手机",
      "brand": "华为",
      "unit": "台",
      "purchase_price": 5999.0,
      "sales_price": 6999.0
    }
  ]
}
```

#### 1.2 创建商品
- **接口**: `POST /products`
- **描述**: 创建新商品
- **请求参数**:
```json
{
  "product_name": "商品名称",
  "sku": "商品SKU",
  "category": "商品分类",
  "brand": "品牌",
  "unit": "单位",
  "purchase_price": 100.00,
  "sales_price": 150.00
}
```
- **成功响应**:
```json
{
  "code": 200,
  "message": "商品创建成功",
  "success": true,
  "data": {
    "product_id": "P20250624221401687eb7a1"
  }
}
```

### 2. 仓库管理

#### 2.1 获取仓库列表
- **接口**: `GET /warehouses`
- **描述**: 获取所有仓库信息
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取仓库列表成功",
  "success": true,
  "data": [
    {
      "warehouse_id": "W20250624215959a1b2c3d4",
      "warehouse_name": "北京总仓",
      "location": "北京市朝阳区",
      "capacity": 10000.0
    }
  ]
}
```

#### 2.2 创建仓库
- **接口**: `POST /warehouses`
- **描述**: 创建新仓库
- **请求参数**:
```json
{
  "warehouse_name": "仓库名称",
  "location": "仓库位置",
  "capacity": 10000.0
}
```

### 3. 库存管理

#### 3.1 查询库存
- **接口**: `GET /inventory`
- **描述**: 查询库存信息
- **查询参数**:
  - `product_id`: 商品ID（可选）
  - `warehouse_id`: 仓库ID（可选）

#### 3.2 获取库存预警
- **接口**: `GET /inventory/warnings`
- **描述**: 获取库存预警列表
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取库存预警成功",
  "success": true,
  "data": [
    {
      "product_name": "华为Mate60 Pro",
      "sku": "HW-MATE60-PRO",
      "warehouse_name": "北京总仓",
      "current_quantity": 5,
      "warning_threshold": 10,
      "message": "库存低于预警值，请及时补货！"
    }
  ]
}
```

### 4. 入库管理

#### 4.1 获取入库单列表
- **接口**: `GET /inbound`
- **描述**: 获取所有入库单

#### 4.2 创建入库单
- **接口**: `POST /inbound`
- **描述**: 创建新的入库单
- **请求参数**:
```json
{
  "order_type": "采购入库",
  "supplier_id": "S20250624215959a1b2c3d4",
  "warehouse_id": "W20250624215959a1b2c3d4",
  "remarks": "备注信息",
  "items": [
    {
      "product_id": "P20250624215951b264f8ba",
      "quantity": 100,
      "unit_price": 5999.0,
      "batch_number": "BATCH001"
    }
  ]
}
```

#### 4.3 确认入库
- **接口**: `POST /inbound/{inbound_order_id}/confirm`
- **描述**: 确认入库单，更新库存

### 5. 出库管理

#### 5.1 获取出库单列表
- **接口**: `GET /outbound`
- **描述**: 获取所有出库单

#### 5.2 创建出库单
- **接口**: `POST /outbound`
- **描述**: 创建新的出库单

#### 5.3 确认出库
- **接口**: `POST /outbound/{outbound_order_id}/confirm`
- **描述**: 确认出库单，减少库存

### 6. 供应商管理

#### 6.1 获取供应商列表
- **接口**: `GET /suppliers`
- **描述**: 获取所有供应商信息

#### 6.2 创建供应商
- **接口**: `POST /suppliers`
- **描述**: 创建新供应商

### 7. 客户管理

#### 7.1 获取客户列表
- **接口**: `GET /customers`
- **描述**: 获取所有客户信息

#### 7.2 创建客户
- **接口**: `POST /customers`
- **描述**: 创建新客户

## 错误码说明

### 常见错误消息

| 错误码 | 错误消息 | 说明 |
|--------|----------|------|
| 400 | 商品名称不能为空 | 创建商品时未提供商品名称 |
| 400 | SKU不能为空 | 创建商品时未提供SKU |
| 400 | SKU已存在，请使用其他SKU | 提供的SKU已被其他商品使用 |
| 400 | 仓库名称不能为空 | 创建仓库时未提供仓库名称 |
| 400 | 目标仓库不能为空 | 创建入库单时未指定目标仓库 |
| 400 | 入库商品明细不能为空 | 创建入库单时未提供商品明细 |
| 400 | 商品和数量不能为空 | 入库/出库明细中缺少商品或数量信息 |
| 400 | 商品 XXX 库存不足 | 出库时指定商品的可用库存不足 |
| 404 | 入库单不存在 | 确认入库时找不到指定的入库单 |
| 404 | 出库单不存在 | 确认出库时找不到指定的出库单 |
| 400 | 入库单状态不正确 | 入库单不是"待入库"状态 |
| 400 | 出库单状态不正确 | 出库单不是"待出库"状态 |

## 使用示例

### 创建商品示例
```bash
curl -X POST http://localhost:5000/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "iPhone 15 Pro",
    "sku": "APPLE-IP15-PRO",
    "category": "智能手机",
    "brand": "苹果",
    "unit": "台",
    "purchase_price": 7999.0,
    "sales_price": 9999.0
  }'
```

### 查询库存示例
```bash
curl http://localhost:5000/api/inventory
```

### 获取库存预警示例
```bash
curl http://localhost:5000/api/inventory/warnings
```
