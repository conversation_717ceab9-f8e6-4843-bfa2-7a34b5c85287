#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端启动脚本
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

try:
    print("🚀 正在启动电商库存管理系统后端...")
    print("📁 当前工作目录:", os.getcwd())
    print("🐍 Python版本:", sys.version)
    
    # 导入Flask应用
    from src.main import app
    
    print("✅ Flask应用导入成功")
    print("🌐 启动Web服务器...")
    print("📡 后端API地址: http://localhost:5000/api")
    print("🔧 调试模式: 开启")
    print("⚠️  按 Ctrl+C 停止服务")
    print("-" * 50)
    
    # 启动Flask应用
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,  # 关闭调试模式避免重启问题
        use_reloader=False
    )
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("💡 请确保已安装所有依赖包: pip install -r requirements.txt")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
