var ft=Object.defineProperty;var pt=(e,r)=>{for(var t in r)ft(e,t,{get:r[t],enumerable:!0})};import*as oe from"node:module";import{pathToFileURL as Or}from"node:url";var ae={};pt(ae,{DEBUG:()=>le});var le=dt(process.env.DEBUG);function dt(e){if(e===void 0)return!1;if(e==="true"||e==="1")return!0;if(e==="false"||e==="0")return!1;if(e==="*")return!0;let r=e.split(",").map(t=>t.split(":")[0]);return r.includes("-tailwindcss")?!1:!!r.includes("tailwindcss")}import F from"enhanced-resolve";import{createJiti as yr}from"jiti";import Se from"node:fs";import nt from"node:fs/promises";import G from"node:path";import{pathToFileURL as et}from"node:url";import{__unstable__loadDesignSystem as br,compile as xr,compileAst as Ar,Features as ya,Polyfills as ba}from"tailwindcss";import se from"node:fs/promises";import z from"node:path";var mt=[/import[\s\S]*?['"](.{3,}?)['"]/gi,/import[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi,/export[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi,/require\(['"`](.+)['"`]\)/gi],gt=[".js",".cjs",".mjs"],ht=["",".js",".cjs",".mjs",".ts",".cts",".mts",".jsx",".tsx"],vt=["",".ts",".cts",".mts",".tsx",".js",".cjs",".mjs",".jsx"];async function wt(e,r){for(let t of r){let i=`${e}${t}`;if((await se.stat(i).catch(()=>null))?.isFile())return i}for(let t of r){let i=`${e}/index${t}`;if(await se.access(i).then(()=>!0,()=>!1))return i}return null}async function Ne(e,r,t,i){let o=gt.includes(i)?ht:vt,l=await wt(z.resolve(t,r),o);if(l===null||e.has(l))return;e.add(l),t=z.dirname(l),i=z.extname(l);let n=await se.readFile(l,"utf-8"),s=[];for(let a of mt)for(let u of n.matchAll(a))u[1].startsWith(".")&&s.push(Ne(e,u[1],t,i));await Promise.all(s)}async function Ve(e){let r=new Set;return await Ne(r,e,z.dirname(e),z.extname(e)),Array.from(r)}import*as xe from"node:path";function ue(e){return{kind:"word",value:e}}function kt(e,r){return{kind:"function",value:e,nodes:r}}function yt(e){return{kind:"separator",value:e}}function V(e,r,t=null){for(let i=0;i<e.length;i++){let o=e[i],l=!1,n=0,s=r(o,{parent:t,replaceWith(a){l||(l=!0,Array.isArray(a)?a.length===0?(e.splice(i,1),n=0):a.length===1?(e[i]=a[0],n=1):(e.splice(i,1,...a),n=a.length):e[i]=a)}})??0;if(l){s===0?i--:i+=n-1;continue}if(s===2)return 2;if(s!==1&&o.kind==="function"&&V(o.nodes,r,o)===2)return 2}}function N(e){let r="";for(let t of e)switch(t.kind){case"word":case"separator":{r+=t.value;break}case"function":r+=t.value+"("+N(t.nodes)+")"}return r}var Te=92,bt=41,Ee=58,Re=44,xt=34,Pe=61,Oe=62,_e=60,De=10,At=40,Ct=39,Ue=47,Ke=32,Fe=9;function A(e){e=e.replaceAll(`\r
`,`
`);let r=[],t=[],i=null,o="",l;for(let n=0;n<e.length;n++){let s=e.charCodeAt(n);switch(s){case Te:{o+=e[n]+e[n+1],n++;break}case Ee:case Re:case Pe:case Oe:case _e:case De:case Ue:case Ke:case Fe:{if(o.length>0){let c=ue(o);i?i.nodes.push(c):r.push(c),o=""}let a=n,u=n+1;for(;u<e.length&&(l=e.charCodeAt(u),!(l!==Ee&&l!==Re&&l!==Pe&&l!==Oe&&l!==_e&&l!==De&&l!==Ue&&l!==Ke&&l!==Fe));u++);n=u-1;let p=yt(e.slice(a,u));i?i.nodes.push(p):r.push(p);break}case Ct:case xt:{let a=n;for(let u=n+1;u<e.length;u++)if(l=e.charCodeAt(u),l===Te)u+=1;else if(l===s){n=u;break}o+=e.slice(a,n+1);break}case At:{let a=kt(o,[]);o="",i?i.nodes.push(a):r.push(a),t.push(a),i=a;break}case bt:{let a=t.pop();if(o.length>0){let u=ue(o);a.nodes.push(u),o=""}t.length>0?i=t[t.length-1]:i=null;break}default:o+=String.fromCharCode(s)}}return o.length>0&&r.push(ue(o)),r}var St=["anchor-size"],Fr=new RegExp(`(${St.join("|")})\\(`,"g");var h=class extends Map{constructor(t){super();this.factory=t}get(t){let i=super.get(t);return i===void 0&&(i=this.factory(t,this),this.set(t,i)),i}};var Wr=new Uint8Array(256);var Y=new Uint8Array(256);function y(e,r){let t=0,i=[],o=0,l=e.length,n=r.charCodeAt(0);for(let s=0;s<l;s++){let a=e.charCodeAt(s);if(t===0&&a===n){i.push(e.slice(o,s)),o=s+1;continue}switch(a){case 92:s+=1;break;case 39:case 34:for(;++s<l;){let u=e.charCodeAt(s);if(u===92){s+=1;continue}if(u===a)break}break;case 40:Y[t]=41,t++;break;case 91:Y[t]=93,t++;break;case 123:Y[t]=125,t++;break;case 93:case 125:case 41:t>0&&a===Y[t-1]&&t--;break}}return i.push(e.slice(o)),i}var Xr=new h(e=>{let r=A(e),t=new Set;return V(r,(i,{parent:o})=>{let l=o===null?r:o.nodes??[];if(i.kind==="word"&&(i.value==="+"||i.value==="-"||i.value==="*"||i.value==="/")){let n=l.indexOf(i)??-1;if(n===-1)return;let s=l[n-1];if(s?.kind!=="separator"||s.value!==" ")return;let a=l[n+1];if(a?.kind!=="separator"||a.value!==" ")return;t.add(s),t.add(a)}else i.kind==="separator"&&i.value.trim()==="/"?i.value="/":i.kind==="separator"&&i.value.length>0&&i.value.trim()===""?(l[0]===i||l[l.length-1]===i)&&t.add(i):i.kind==="separator"&&i.value.trim()===","&&(i.value=",")}),t.size>0&&V(r,(i,{replaceWith:o})=>{t.has(i)&&(t.delete(i),o([]))}),ce(r),N(r)});var ei=new h(e=>{let r=A(e);return r.length===3&&r[0].kind==="word"&&r[0].value==="&"&&r[1].kind==="separator"&&r[1].value===":"&&r[2].kind==="function"&&r[2].value==="is"?N(r[2].nodes):e});function ce(e){for(let r of e)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=L(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=L(r.value);for(let t=0;t<r.nodes.length;t++)ce([r.nodes[t]]);break}r.value=L(r.value),ce(r.nodes);break}case"separator":r.value=L(r.value);break;case"word":{(r.value[0]!=="-"||r.value[1]!=="-")&&(r.value=L(r.value));break}default:$t(r)}}var ti=new h(e=>{let r=A(e);return r.length===1&&r[0].kind==="function"&&r[0].value==="var"});function $t(e){throw new Error(`Unexpected value: ${e}`)}function L(e){return e.replaceAll("_",String.raw`\_`).replaceAll(" ","_")}var T=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,ci=new RegExp(`^${T.source}$`);var fi=new RegExp(`^${T.source}%$`);var pi=new RegExp(`^${T.source}s*/s*${T.source}$`);var Nt=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],di=new RegExp(`^${T.source}(${Nt.join("|")})$`);var Vt=["deg","rad","grad","turn"],mi=new RegExp(`^${T.source}(${Vt.join("|")})$`);var gi=new RegExp(`^${T.source} +${T.source} +${T.source}$`);function x(e){let r=Number(e);return Number.isInteger(r)&&r>=0&&String(r)===String(e)}function j(e,r){if(r===null)return e;let t=Number(r);return Number.isNaN(t)||(r=`${t*100}%`),r==="100%"?e:`color-mix(in oklab, ${e} ${r}, transparent)`}var Rt={"--alpha":Pt,"--spacing":Ot,"--theme":_t,theme:Dt};function Pt(e,r,t,...i){let[o,l]=y(t,"/").map(n=>n.trim());if(!o||!l)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${o||"var(--my-color)"} / ${l||"50%"})\``);if(i.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${o||"var(--my-color)"} / ${l||"50%"})\``);return j(o,l)}function Ot(e,r,t,...i){if(!t)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(i.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${i.length+1}.`);let o=e.theme.resolve(null,["--spacing"]);if(!o)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${o} * ${t})`}function _t(e,r,t,...i){if(!t.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let o=!1;t.endsWith(" inline")&&(o=!0,t=t.slice(0,-7)),r.kind==="at-rule"&&(o=!0);let l=e.resolveThemeValue(t,o);if(!l){if(i.length>0)return i.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(i.length===0)return l;let n=i.join(", ");if(n==="initial")return l;if(l==="initial")return n;if(l.startsWith("var(")||l.startsWith("theme(")||l.startsWith("--theme(")){let s=A(l);return Kt(s,n),N(s)}return l}function Dt(e,r,t,...i){t=Ut(t);let o=e.resolveThemeValue(t);if(!o&&i.length>0)return i.join(", ");if(!o)throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return o}var _i=new RegExp(Object.keys(Rt).map(e=>`${e}\\(`).join("|"));function Ut(e){if(e[0]!=="'"&&e[0]!=='"')return e;let r="",t=e[0];for(let i=1;i<e.length-1;i++){let o=e[i],l=e[i+1];o==="\\"&&(l===t||l==="\\")?(r+=l,i++):r+=o}return r}function Kt(e,r){V(e,t=>{if(t.kind==="function"&&!(t.value!=="var"&&t.value!=="theme"&&t.value!=="--theme"))if(t.nodes.length===1)t.nodes.push({kind:"word",value:`, ${r}`});else{let i=t.nodes[t.nodes.length-1];i.kind==="word"&&i.value==="initial"&&(i.value=r)}})}var W=92,Z=47,X=42,Mt=34,Wt=39,Bt=58,ee=59,S=10,te=13,B=32,re=9,Me=123,me=125,ve=40,We=41,Ht=91,qt=93,Be=45,ge=64,Gt=33;function q(e,r){let t=r?.from?{file:r.from,code:e}:null;e[0]==="\uFEFF"&&(e=" "+e.slice(1));let i=[],o=[],l=[],n=null,s=null,a="",u="",p=0,c;for(let f=0;f<e.length;f++){let m=e.charCodeAt(f);if(!(m===te&&(c=e.charCodeAt(f+1),c===S)))if(m===W)a===""&&(p=f),a+=e.slice(f,f+2),f+=1;else if(m===Z&&e.charCodeAt(f+1)===X){let d=f;for(let v=f+2;v<e.length;v++)if(c=e.charCodeAt(v),c===W)v+=1;else if(c===X&&e.charCodeAt(v+1)===Z){f=v+1;break}let g=e.slice(d,f+1);if(g.charCodeAt(2)===Gt){let v=ke(g.slice(2,-2));o.push(v),t&&(v.src=[t,d,f+1],v.dst=[t,d,f+1])}}else if(m===Wt||m===Mt){let d=f;for(let g=f+1;g<e.length;g++)if(c=e.charCodeAt(g),c===W)g+=1;else if(c===m){f=g;break}else{if(c===ee&&(e.charCodeAt(g+1)===S||e.charCodeAt(g+1)===te&&e.charCodeAt(g+2)===S))throw new Error(`Unterminated string: ${e.slice(d,g+1)+String.fromCharCode(m)}`);if(c===S||c===te&&e.charCodeAt(g+1)===S)throw new Error(`Unterminated string: ${e.slice(d,g)+String.fromCharCode(m)}`)}a+=e.slice(d,f+1)}else{if((m===B||m===S||m===re)&&(c=e.charCodeAt(f+1))&&(c===B||c===S||c===re||c===te&&(c=e.charCodeAt(f+2))&&c==S))continue;if(m===S){if(a.length===0)continue;c=a.charCodeAt(a.length-1),c!==B&&c!==S&&c!==re&&(a+=" ")}else if(m===Be&&e.charCodeAt(f+1)===Be&&a.length===0){let d="",g=f,v=-1;for(let k=f+2;k<e.length;k++)if(c=e.charCodeAt(k),c===W)k+=1;else if(c===Z&&e.charCodeAt(k+1)===X){for(let D=k+2;D<e.length;D++)if(c=e.charCodeAt(D),c===W)D+=1;else if(c===X&&e.charCodeAt(D+1)===Z){k=D+1;break}}else if(v===-1&&c===Bt)v=a.length+k-g;else if(c===ee&&d.length===0){a+=e.slice(g,k),f=k;break}else if(c===ve)d+=")";else if(c===Ht)d+="]";else if(c===Me)d+="}";else if((c===me||e.length-1===k)&&d.length===0){f=k-1,a+=e.slice(g,k);break}else(c===We||c===qt||c===me)&&d.length>0&&e[k]===d[d.length-1]&&(d=d.slice(0,-1));let I=he(a,v);if(!I)throw new Error("Invalid custom property, expected a value");t&&(I.src=[t,g,f],I.dst=[t,g,f]),n?n.nodes.push(I):i.push(I),a=""}else if(m===ee&&a.charCodeAt(0)===ge)s=H(a),t&&(s.src=[t,p,f],s.dst=[t,p,f]),n?n.nodes.push(s):i.push(s),a="",s=null;else if(m===ee&&u[u.length-1]!==")"){let d=he(a);if(!d)throw a.length===0?new Error("Unexpected semicolon"):new Error(`Invalid declaration: \`${a.trim()}\``);t&&(d.src=[t,p,f],d.dst=[t,p,f]),n?n.nodes.push(d):i.push(d),a=""}else if(m===Me&&u[u.length-1]!==")")u+="}",s=R(a.trim()),t&&(s.src=[t,p,f],s.dst=[t,p,f]),n&&n.nodes.push(s),l.push(n),n=s,a="",s=null;else if(m===me&&u[u.length-1]!==")"){if(u==="")throw new Error("Missing opening {");if(u=u.slice(0,-1),a.length>0)if(a.charCodeAt(0)===ge)s=H(a),t&&(s.src=[t,p,f],s.dst=[t,p,f]),n?n.nodes.push(s):i.push(s),a="",s=null;else{let g=a.indexOf(":");if(n){let v=he(a,g);if(!v)throw new Error(`Invalid declaration: \`${a.trim()}\``);t&&(v.src=[t,p,f],v.dst=[t,p,f]),n.nodes.push(v)}}let d=l.pop()??null;d===null&&n&&i.push(n),n=d,a="",s=null}else if(m===ve)u+=")",a+="(";else if(m===We){if(u[u.length-1]!==")")throw new Error("Missing opening (");u=u.slice(0,-1),a+=")"}else{if(a.length===0&&(m===B||m===S||m===re))continue;a===""&&(p=f),a+=String.fromCharCode(m)}}}if(a.charCodeAt(0)===ge){let f=H(a);t&&(f.src=[t,p,e.length],f.dst=[t,p,e.length]),i.push(f)}if(u.length>0&&n){if(n.kind==="rule")throw new Error(`Missing closing } at ${n.selector}`);if(n.kind==="at-rule")throw new Error(`Missing closing } at ${n.name} ${n.params}`)}return o.length>0?o.concat(i):i}function H(e,r=[]){let t=e,i="";for(let o=5;o<e.length;o++){let l=e.charCodeAt(o);if(l===B||l===ve){t=e.slice(0,o),i=e.slice(o);break}}return C(t.trim(),i.trim(),r)}function he(e,r=e.indexOf(":")){if(r===-1)return null;let t=e.indexOf("!important",r+1);return E(e.slice(0,r).trim(),e.slice(r+1,t===-1?e.length:t).trim(),t!==-1)}var ye={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function _(e){return{__BARE_VALUE__:e}}var $=_(e=>{if(x(e.value))return e.value}),w=_(e=>{if(x(e.value))return`${e.value}%`}),P=_(e=>{if(x(e.value))return`${e.value}px`}),qe=_(e=>{if(x(e.value))return`${e.value}ms`}),ie=_(e=>{if(x(e.value))return`${e.value}deg`}),rr=_(e=>{if(e.fraction===null)return;let[r,t]=y(e.fraction,"/");if(!(!x(r)||!x(t)))return e.fraction}),Ge=_(e=>{if(x(Number(e.value)))return`repeat(${e.value}, minmax(0, 1fr))`}),ir={accentColor:({theme:e})=>e("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...rr},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>({...e("brightness"),...w}),backdropContrast:({theme:e})=>({...e("contrast"),...w}),backdropGrayscale:({theme:e})=>({...e("grayscale"),...w}),backdropHueRotate:({theme:e})=>({...e("hueRotate"),...ie}),backdropInvert:({theme:e})=>({...e("invert"),...w}),backdropOpacity:({theme:e})=>({...e("opacity"),...w}),backdropSaturate:({theme:e})=>({...e("saturate"),...w}),backdropSepia:({theme:e})=>({...e("sepia"),...w}),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>e("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...P},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...w},caretColor:({theme:e})=>e("colors"),colors:()=>({...ye}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...$},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...w},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>({...e("borderWidth"),...P}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...e("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...$},flexShrink:{0:"0",DEFAULT:"1",...$},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...w},grayscale:{0:"0",DEFAULT:"100%",...w},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...$},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...$},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...$},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...$},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Ge},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Ge},height:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...ie},inset:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),invert:{0:"0",DEFAULT:"100%",...w},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...$},maxHeight:({theme:e})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),maxWidth:({theme:e})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...e("spacing")}),minHeight:({theme:e})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),minWidth:({theme:e})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...w},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...$},outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),ringColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...ie},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...w},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...w},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:e})=>e("spacing"),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%",...w},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...ie},space:({theme:e})=>e("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:e})=>({none:"none",...e("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...$},supports:{},data:{},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},textIndent:({theme:e})=>e("spacing"),textOpacity:({theme:e})=>e("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...qe},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...qe},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),size:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),width:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...$}};var nr=64;function U(e,r=[]){return{kind:"rule",selector:e,nodes:r}}function C(e,r="",t=[]){return{kind:"at-rule",name:e,params:r,nodes:t}}function R(e,r=[]){return e.charCodeAt(0)===nr?H(e,r):U(e,r)}function E(e,r,t=!1){return{kind:"declaration",property:e,value:r,important:t}}function ke(e){return{kind:"comment",value:e}}function b(e,r,t=[],i={}){for(let o=0;o<e.length;o++){let l=e[o],n=t[t.length-1]??null;if(l.kind==="context"){if(b(l.nodes,r,t,{...i,...l.context})===2)return 2;continue}t.push(l);let s=!1,a=0,u=r(l,{parent:n,context:i,path:t,replaceWith(p){s||(s=!0,Array.isArray(p)?p.length===0?(e.splice(o,1),a=0):p.length===1?(e[o]=p[0],a=1):(e.splice(o,1,...p),a=p.length):(e[o]=p,a=1))}})??0;if(t.pop(),s){u===0?o--:o+=a-1;continue}if(u===2)return 2;if(u!==1&&"nodes"in l){t.push(l);let p=b(l.nodes,r,t,i);if(t.pop(),p===2)return 2}}}function K(e,r){let t=0,i={file:null,code:""};function o(n,s=0){let a="",u="  ".repeat(s);if(n.kind==="declaration"){if(a+=`${u}${n.property}: ${n.value}${n.important?" !important":""};
`,r){t+=u.length;let p=t;t+=n.property.length,t+=2,t+=n.value?.length??0,n.important&&(t+=11);let c=t;t+=2,n.dst=[i,p,c]}}else if(n.kind==="rule"){if(a+=`${u}${n.selector} {
`,r){t+=u.length;let p=t;t+=n.selector.length,t+=1;let c=t;n.dst=[i,p,c],t+=2}for(let p of n.nodes)a+=o(p,s+1);a+=`${u}}
`,r&&(t+=u.length,t+=2)}else if(n.kind==="at-rule"){if(n.nodes.length===0){let p=`${u}${n.name} ${n.params};
`;if(r){t+=u.length;let c=t;t+=n.name.length,t+=1,t+=n.params.length;let f=t;t+=2,n.dst=[i,c,f]}return p}if(a+=`${u}${n.name}${n.params?` ${n.params} `:" "}{
`,r){t+=u.length;let p=t;t+=n.name.length,n.params&&(t+=1,t+=n.params.length),t+=1;let c=t;n.dst=[i,p,c],t+=2}for(let p of n.nodes)a+=o(p,s+1);a+=`${u}}
`,r&&(t+=u.length,t+=2)}else if(n.kind==="comment"){if(a+=`${u}/*${n.value}*/
`,r){t+=u.length;let p=t;t+=2+n.value.length+2;let c=t;n.dst=[i,p,c],t+=1}}else if(n.kind==="context"||n.kind==="at-root")return"";return a}let l="";for(let n of e)l+=o(n,0);return i.code=l,l}function or(e,r){if(typeof e!="string")throw new TypeError("expected path to be a string");if(e==="\\"||e==="/")return"/";var t=e.length;if(t<=1)return e;var i="";if(t>4&&e[3]==="\\"){var o=e[2];(o==="?"||o===".")&&e.slice(0,2)==="\\\\"&&(e=e.slice(2),i="//")}var l=e.split(/[/\\]+/);return r!==!1&&l[l.length-1]===""&&l.pop(),i+l.join("/")}function be(e){let r=or(e);return e.startsWith("\\\\")&&r.startsWith("/")&&!r.startsWith("//")?`/${r}`:r}var Ae=/(?<!@import\s+)(?<=^|[^\w\-\u0080-\uffff])url\((\s*('[^']+'|"[^"]+")\s*|[^'")]+)\)/,Je=/(?<=image-set\()((?:[\w-]{1,256}\([^)]*\)|[^)])*)(?=\))/,lr=/(?:gradient|element|cross-fade|image)\(/,ar=/^\s*data:/i,sr=/^([a-z]+:)?\/\//,ur=/^[A-Z_][.\w-]*\(/i,cr=/(?:^|\s)(?<url>[\w-]+\([^)]*\)|"[^"]*"|'[^']*'|[^,]\S*[^,])\s*(?:\s(?<descriptor>\w[^,]+))?(?:,|$)/g,fr=/(?<!\\)"/g,pr=/(?: |\\t|\\n|\\f|\\r)+/g,dr=e=>ar.test(e),mr=e=>sr.test(e);async function Ye({css:e,base:r,root:t}){if(!e.includes("url(")&&!e.includes("image-set("))return e;let i=q(e),o=[];function l(n){if(n[0]==="/")return n;let s=xe.posix.join(be(r),n),a=xe.posix.relative(be(t),s);return a.startsWith(".")||(a="./"+a),a}return b(i,n=>{if(n.kind!=="declaration"||!n.value)return;let s=Ae.test(n.value),a=Je.test(n.value);if(s||a){let u=a?gr:Qe;o.push(u(n.value,l).then(p=>{n.value=p}))}}),o.length&&await Promise.all(o),K(i)}function Qe(e,r){return Xe(e,Ae,async t=>{let[i,o]=t;return await Ze(o.trim(),i,r)})}async function gr(e,r){return await Xe(e,Je,async t=>{let[,i]=t;return await vr(i,async({url:l})=>Ae.test(l)?await Qe(l,r):lr.test(l)?l:await Ze(l,l,r))})}async function Ze(e,r,t,i="url"){let o="",l=e[0];if((l==='"'||l==="'")&&(o=l,e=e.slice(1,-1)),hr(e))return r;let n=await t(e);return o===""&&n!==encodeURI(n)&&(o='"'),o==="'"&&n.includes("'")&&(o='"'),o==='"'&&n.includes('"')&&(n=n.replace(fr,'\\"')),`${i}(${o}${n}${o})`}function hr(e,r){return mr(e)||dr(e)||!e[0].match(/[\.a-zA-Z0-9_]/)||ur.test(e)}function vr(e,r){return Promise.all(wr(e).map(async({url:t,descriptor:i})=>({url:await r({url:t,descriptor:i}),descriptor:i}))).then(kr)}function wr(e){let r=e.trim().replace(pr," ").replace(/\r?\n/,"").replace(/,\s+/,", ").replaceAll(/\s+/g," ").matchAll(cr);return Array.from(r,({groups:t})=>({url:t?.url?.trim()??"",descriptor:t?.descriptor?.trim()??""})).filter(({url:t})=>!!t)}function kr(e){return e.map(({url:r,descriptor:t})=>r+(t?` ${t}`:"")).join(", ")}async function Xe(e,r,t){let i,o=e,l="";for(;i=r.exec(o);)l+=o.slice(0,i.index),l+=await t(i),o=o.slice(i.index+i[0].length);return l+=o,l}function ot({base:e,from:r,polyfills:t,onDependency:i,shouldRewriteUrls:o,customCssResolver:l,customJsResolver:n}){return{base:e,polyfills:t,from:r,async loadModule(s,a){return at(s,a,i,n)},async loadStylesheet(s,a){let u=await st(s,a,i,l);return o&&(u.content=await Ye({css:u.content,root:e,base:u.base})),u}}}async function lt(e,r){if(e.root&&e.root!=="none"){let t=/[*{]/,i=[];for(let l of e.root.pattern.split("/")){if(t.test(l))break;i.push(l)}if(!await nt.stat(G.resolve(r,i.join("/"))).then(l=>l.isDirectory()).catch(()=>!1))throw new Error(`The \`source(${e.root.pattern})\` does not exist`)}}async function Ca(e,r){let t=await Ar(e,ot(r));return await lt(t,r.base),t}async function Sa(e,r){let t=await xr(e,ot(r));return await lt(t,r.base),t}async function $a(e,{base:r}){return br(e,{base:r,async loadModule(t,i){return at(t,i,()=>{})},async loadStylesheet(t,i){return st(t,i,()=>{})}})}async function at(e,r,t,i){if(e[0]!=="."){let s=await it(e,r,i);if(!s)throw new Error(`Could not resolve '${e}' from '${r}'`);let a=await rt(et(s).href);return{path:s,base:G.dirname(s),module:a.default??a}}let o=await it(e,r,i);if(!o)throw new Error(`Could not resolve '${e}' from '${r}'`);let[l,n]=await Promise.all([rt(et(o).href+"?id="+Date.now()),Ve(o)]);for(let s of n)t(s);return{path:o,base:G.dirname(o),module:l.default??l}}async function st(e,r,t,i){let o=await Sr(e,r,i);if(!o)throw new Error(`Could not resolve '${e}' from '${r}'`);if(t(o),typeof globalThis.__tw_readFile=="function"){let n=await globalThis.__tw_readFile(o,"utf-8");if(n)return{path:o,base:G.dirname(o),content:n}}let l=await nt.readFile(o,"utf-8");return{path:o,base:G.dirname(o),content:l}}var tt=null;async function rt(e){if(typeof globalThis.__tw_load=="function"){let r=await globalThis.__tw_load(e);if(r)return r}try{return await import(e)}catch{return tt??=yr(import.meta.url,{moduleCache:!1,fsCache:!1}),await tt.import(e)}}var $e=["node_modules",...process.env.NODE_PATH?[process.env.NODE_PATH]:[]],Cr=F.ResolverFactory.createResolver({fileSystem:new F.CachedInputFileSystem(Se,4e3),useSyncFileSystemCalls:!0,extensions:[".css"],mainFields:["style"],conditionNames:["style"],modules:$e});async function Sr(e,r,t){if(typeof globalThis.__tw_resolve=="function"){let i=globalThis.__tw_resolve(e,r);if(i)return Promise.resolve(i)}if(t){let i=await t(e,r);if(i)return i}return Ce(Cr,e,r)}var $r=F.ResolverFactory.createResolver({fileSystem:new F.CachedInputFileSystem(Se,4e3),useSyncFileSystemCalls:!0,extensions:[".js",".json",".node",".ts"],conditionNames:["node","import"],modules:$e}),Nr=F.ResolverFactory.createResolver({fileSystem:new F.CachedInputFileSystem(Se,4e3),useSyncFileSystemCalls:!0,extensions:[".js",".json",".node",".ts"],conditionNames:["node","require"],modules:$e});async function it(e,r,t){if(typeof globalThis.__tw_resolve=="function"){let i=globalThis.__tw_resolve(e,r);if(i)return Promise.resolve(i)}if(t){let i=await t(e,r);if(i)return i}return Ce($r,e,r).catch(()=>Ce(Nr,e,r))}function Ce(e,r,t){return new Promise((i,o)=>e.resolve({},t,r,{},(l,n)=>{if(l)return o(l);i(n)}))}Symbol.dispose??=Symbol("Symbol.dispose");Symbol.asyncDispose??=Symbol("Symbol.asyncDispose");var ut=class{constructor(r=t=>void process.stderr.write(`${t}
`)){this.defaultFlush=r}#r=new h(()=>({value:0}));#t=new h(()=>({value:0n}));#e=[];hit(r){this.#r.get(r).value++}start(r){let t=this.#e.map(o=>o.label).join("//"),i=`${t}${t.length===0?"":"//"}${r}`;this.#r.get(i).value++,this.#t.get(i),this.#e.push({id:i,label:r,namespace:t,value:process.hrtime.bigint()})}end(r){let t=process.hrtime.bigint();if(this.#e[this.#e.length-1].label!==r)throw new Error(`Mismatched timer label: \`${r}\`, expected \`${this.#e[this.#e.length-1].label}\``);let i=this.#e.pop(),o=t-i.value;this.#t.get(i.id).value+=o}reset(){this.#r.clear(),this.#t.clear(),this.#e.splice(0)}report(r=this.defaultFlush){let t=[],i=!1;for(let n=this.#e.length-1;n>=0;n--)this.end(this.#e[n].label);for(let[n,{value:s}]of this.#r.entries()){if(this.#t.has(n))continue;t.length===0&&(i=!0,t.push("Hits:"));let a=n.split("//").length;t.push(`${"  ".repeat(a)}${n} ${ne(ct(`\xD7 ${s}`))}`)}this.#t.size>0&&i&&t.push(`
Timers:`);let o=-1/0,l=new Map;for(let[n,{value:s}]of this.#t){let a=`${(Number(s)/1e6).toFixed(2)}ms`;l.set(n,a),o=Math.max(o,a.length)}for(let n of this.#t.keys()){let s=n.split("//").length;t.push(`${ne(`[${l.get(n).padStart(o," ")}]`)}${"  ".repeat(s-1)}${s===1?" ":ne(" \u21B3 ")}${n.split("//").pop()} ${this.#r.get(n).value===1?"":ne(ct(`\xD7 ${this.#r.get(n).value}`))}`.trimEnd())}r(`
${t.join(`
`)}
`),this.reset()}[Symbol.dispose](){le&&this.report()}};function ne(e){return`\x1B[2m${e}\x1B[22m`}function ct(e){return`\x1B[34m${e}\x1B[39m`}import Vr from"@ampproject/remapping";import{Features as J,transform as Tr}from"lightningcss";import Er from"magic-string";function Oa(e,{file:r="input.css",minify:t=!1,map:i}={}){function o(a,u){return Tr({filename:r,code:a,minify:t,sourceMap:typeof u<"u",inputSourceMap:u,drafts:{customMedia:!0},nonStandard:{deepSelectorCombinator:!0},include:J.Nesting|J.MediaQueries,exclude:J.LogicalProperties|J.DirSelector|J.LightDark,targets:{safari:16<<16|1024,ios_saf:16<<16|1024,firefox:8388608,chrome:7274496},errorRecovery:!0})}let l=o(Buffer.from(e),i);i=l.map?.toString(),l=o(l.code,i),i=l.map?.toString();let n=l.code.toString(),s=new Er(n);if(s.replaceAll("@media not (","@media not all and ("),i!==void 0&&s.hasChanged()){let a=s.generateMap({source:"original",hires:"boundary"}).toString();i=Vr([a,i],()=>null).toString()}return n=s.toString(),{code:n,map:i}}import{SourceMapGenerator as Rr}from"source-map-js";function Pr(e){let r=new Rr,t=1,i=new h(o=>({url:o?.url??`<unknown ${t++}>`,content:o?.content??"<none>"}));for(let o of e.mappings){let l=i.get(o.originalPosition?.source??null);r.addMapping({generated:o.generatedPosition,original:o.originalPosition,source:l.url,name:o.name}),r.setSourceContent(l.url,l.content)}return r.toString()}function Ka(e){let r=typeof e=="string"?e:Pr(e);return{raw:r,get inline(){let t="";return t+="/*# sourceMappingURL=data:application/json;base64,",t+=Buffer.from(r,"utf-8").toString("base64"),t+=` */
`,t}}}if(!process.versions.bun){let e=oe.createRequire(import.meta.url);oe.register?.(Or(e.resolve("@tailwindcss/node/esm-cache-loader")))}export{ya as Features,ut as Instrumentation,ba as Polyfills,$a as __unstable__loadDesignSystem,Sa as compile,Ca as compileAst,ae as env,at as loadModule,be as normalizePath,Oa as optimize,Ka as toSourceMap};
