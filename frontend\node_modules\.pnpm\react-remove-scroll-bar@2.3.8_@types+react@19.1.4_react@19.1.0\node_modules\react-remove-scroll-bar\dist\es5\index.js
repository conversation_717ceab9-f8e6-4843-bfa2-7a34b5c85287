"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGapWidth = exports.removedBarSizeVariable = exports.noScrollbarsClassName = exports.fullWidthClassName = exports.zeroRightClassName = exports.RemoveScrollBar = void 0;
var component_1 = require("./component");
Object.defineProperty(exports, "RemoveScrollBar", { enumerable: true, get: function () { return component_1.RemoveScrollBar; } });
var constants_1 = require("./constants");
Object.defineProperty(exports, "zeroRightClassName", { enumerable: true, get: function () { return constants_1.zeroRightClassName; } });
Object.defineProperty(exports, "fullWidthClassName", { enumerable: true, get: function () { return constants_1.fullWidthClassName; } });
Object.defineProperty(exports, "noScrollbarsClassName", { enumerable: true, get: function () { return constants_1.noScrollbarsClassName; } });
Object.defineProperty(exports, "removedBarSizeVariable", { enumerable: true, get: function () { return constants_1.removedBarSizeVariable; } });
var utils_1 = require("./utils");
Object.defineProperty(exports, "getGapWidth", { enumerable: true, get: function () { return utils_1.getGapWidth; } });
