{"version": 3, "file": "vest.modern.mjs", "sources": ["../src/vest.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { FieldError } from 'react-hook-form';\nimport promisify from 'vest/promisify';\nimport type { Resolver, VestErrors } from './types';\n\nconst parseErrorSchema = (\n  vestError: VestErrors,\n  validateAllFieldCriteria: boolean,\n) => {\n  const errors: Record<string, FieldError> = {};\n  for (const path in vestError) {\n    if (!errors[path]) {\n      errors[path] = { message: vestError[path][0], type: '' };\n    }\n\n    if (validateAllFieldCriteria) {\n      errors[path].types = vestError[path].reduce<Record<number, string>>(\n        (acc, message, index) => (acc[index] = message) && acc,\n        {},\n      );\n    }\n  }\n  return errors;\n};\n\n/**\n * Creates a resolver for react-hook-form using Vest validation\n * @param {Function} schema - The Vest validation schema\n * @param {Object} _ - Unused parameter\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {string} [resolverOptions.mode='async'] - Validation mode\n * @returns {Resolver} A resolver function compatible with react-hook-form\n * @example\n * const schema = vest.create((data) => {\n *   if (!data.name) {\n *     return 'Name is required';\n *   }\n * });\n *\n * useForm({\n *   resolver: vestResolver(schema)\n * });\n */\nexport const vestResolver: Resolver =\n  (schema, _, resolverOptions = {}) =>\n  async (values, context, options) => {\n    const result =\n      resolverOptions.mode === 'sync'\n        ? schema(values, options.names, context)\n        : await promisify(schema)(values, options.names, context);\n\n    if (result.hasErrors()) {\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrorSchema(\n            result.getErrors(),\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return { values, errors: {} };\n  };\n"], "names": ["parseErrorSchema", "vestError", "validateAllFieldCriteria", "errors", "path", "message", "type", "types", "reduce", "acc", "index", "vestResolver", "schema", "_", "resolverOptions", "async", "values", "context", "options", "result", "mode", "names", "promisify", "hasErrors", "toNestErrors", "getErrors", "shouldUseNativeValidation", "criteriaMode", "validateFieldsNatively"], "mappings": "6GAKA,MAAMA,EAAmBA,CACvBC,EACAC,KAEA,MAAMC,EAAqC,GAC3C,IAAK,MAAMC,KAAQH,EACZE,EAAOC,KACVD,EAAOC,GAAQ,CAAEC,QAASJ,EAAUG,GAAM,GAAIE,KAAM,KAGlDJ,IACFC,EAAOC,GAAMG,MAAQN,EAAUG,GAAMI,OACnC,CAACC,EAAKJ,EAASK,KAAWD,EAAIC,GAASL,IAAYI,EACnD,CAAA,IAIN,OAAON,GAqBIQ,EACXA,CAACC,EAAQC,EAAGC,EAAkB,KAC9BC,MAAOC,EAAQC,EAASC,KACtB,MAAMC,EACqB,SAAzBL,EAAgBM,KACZR,EAAOI,EAAQE,EAAQG,MAAOJ,SACxBK,EAAUV,EAAVU,CAAkBN,EAAQE,EAAQG,MAAOJ,GAErD,OAAIE,EAAOI,YACF,CACLP,OAAQ,CAAE,EACVb,OAAQqB,EACNxB,EACEmB,EAAOM,aACNP,EAAQQ,2BACkB,QAAzBR,EAAQS,cAEZT,KAKNA,EAAQQ,2BAA6BE,EAAuB,GAAIV,GAEzD,CAAEF,SAAQb,OAAQ,CAAA"}