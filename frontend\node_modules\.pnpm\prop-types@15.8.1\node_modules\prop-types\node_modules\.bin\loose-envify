#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/frontend/node_modules/.pnpm/loose-envify@1.4.0/node_modules/loose-envify/node_modules:/home/<USER>/frontend/node_modules/.pnpm/loose-envify@1.4.0/node_modules:/home/<USER>/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/frontend/node_modules/.pnpm/loose-envify@1.4.0/node_modules/loose-envify/node_modules:/home/<USER>/frontend/node_modules/.pnpm/loose-envify@1.4.0/node_modules:/home/<USER>/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../loose-envify@1.4.0/node_modules/loose-envify/cli.js" "$@"
else
  exec node  "$basedir/../../../../../loose-envify@1.4.0/node_modules/loose-envify/cli.js" "$@"
fi
