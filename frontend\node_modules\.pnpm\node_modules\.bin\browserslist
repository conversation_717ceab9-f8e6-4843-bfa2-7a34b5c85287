#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/frontend/node_modules/.pnpm/browserslist@4.24.5/node_modules/browserslist/node_modules:/home/<USER>/frontend/node_modules/.pnpm/browserslist@4.24.5/node_modules:/home/<USER>/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/frontend/node_modules/.pnpm/browserslist@4.24.5/node_modules/browserslist/node_modules:/home/<USER>/frontend/node_modules/.pnpm/browserslist@4.24.5/node_modules:/home/<USER>/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../browserslist/cli.js" "$@"
else
  exec node  "$basedir/../browserslist/cli.js" "$@"
fi
