!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","react-hook-form"],r):r((e||self).hookformResolversTypeschema={},e.hookformResolvers,e.ReactHookForm)}(this,function(e,r,o){e.typeschemaResolver=function(e,s,t){return void 0===t&&(t={}),function(s,i,n){try{var a=function(){if(f.issues){var e=function(e,r){for(var s=Object.assign([],e),t={};s.length;){var i=e[0];if(i.path){var n=i.path.join(".");if(t[n]||(t[n]={message:i.message,type:""}),r){var a=t[n].types,f=a&&a[""];t[n]=o.appendErrors(n,r,t,"",f?[].concat(f,i.message):i.message)}s.shift()}}return t}(f.issues,!n.shouldUseNativeValidation&&"all"===n.criteriaMode);return{values:{},errors:r.toNestErrors(e,n)}}return n.shouldUseNativeValidation&&r.validateFieldsNatively({},n),{values:t.raw?Object.assign({},s):f.value,errors:{}}},f=e["~standard"].validate(s),u=function(){if(f instanceof Promise)return Promise.resolve(f).then(function(e){f=e})}();return Promise.resolve(u&&u.then?u.then(a):a())}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=typeschema.umd.js.map
