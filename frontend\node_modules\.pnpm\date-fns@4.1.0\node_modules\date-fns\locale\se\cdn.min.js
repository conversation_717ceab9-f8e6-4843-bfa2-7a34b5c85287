(()=>{var $;function U(C){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},U(C)}function x(C,G){var H=Object.keys(C);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(C);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(C,X).enumerable})),H.push.apply(H,J)}return H}function Q(C){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?x(Object(H),!0).forEach(function(J){A(C,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(J){Object.defineProperty(C,J,Object.getOwnPropertyDescriptor(H,J))})}return C}function A(C,G,H){if(G=N(G),G in C)Object.defineProperty(C,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else C[G]=H;return C}function N(C){var G=z(C,"string");return U(G)=="symbol"?G:String(G)}function z(C,G){if(U(C)!="object"||!C)return C;var H=C[Symbol.toPrimitive];if(H!==void 0){var J=H.call(C,G||"default");if(U(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(C)}var W=Object.defineProperty,GC=function C(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},S={lessThanXSeconds:{one:"unnit go ovtta sekundda",other:"unnit go {{count}} sekundda"},xSeconds:{one:"sekundda",other:"{{count}} sekundda"},halfAMinute:"bealle minuhta",lessThanXMinutes:{one:"unnit go bealle minuhta",other:"unnit go {{count}} minuhta"},xMinutes:{one:"minuhta",other:"{{count}} minuhta"},aboutXHours:{one:"sullii ovtta diimmu",other:"sullii {{count}} diimmu"},xHours:{one:"diimmu",other:"{{count}} diimmu"},xDays:{one:"beaivvi",other:"{{count}} beaivvi"},aboutXWeeks:{one:"sullii ovtta vahku",other:"sullii {{count}} vahku"},xWeeks:{one:"vahku",other:"{{count}} vahku"},aboutXMonths:{one:"sullii ovtta m\xE1nu",other:"sullii {{count}} m\xE1nu"},xMonths:{one:"m\xE1nu",other:"{{count}} m\xE1nu"},aboutXYears:{one:"sullii ovtta jagi",other:"sullii {{count}} jagi"},xYears:{one:"jagi",other:"{{count}} jagi"},overXYears:{one:"guhkit go jagi",other:"guhkit go {{count}} jagi"},almostXYears:{one:"measta jagi",other:"measta {{count}} jagi"}},D=function C(G,H,J){var X,Y=S[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"geah\u010Den "+X;else return X+" \xE1igi";return X};function E(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):C.defaultWidth,J=C.formats[H]||C.formats[C.defaultWidth];return J}}var M={full:"EEEE MMMM d. 'b.' y",long:"MMMM d. 'b.' y",medium:"MMM d. 'b.' y",short:"dd.MM.y"},R={full:"'dii.' HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},V={full:"{{date}} 'dii.' {{time}}",long:"{{date}} 'dii.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},L={date:E({formats:M,defaultWidth:"full"}),time:E({formats:R,defaultWidth:"full"}),dateTime:E({formats:V,defaultWidth:"full"})},j={lastWeek:"'ovddit' eeee 'dii.' p",yesterday:"'ikte dii.' p",today:"'odne dii.' p",tomorrow:"'ihtin dii.' p",nextWeek:"EEEE 'dii.' p",other:"P"},w=function C(G,H,J,X){return j[G]};function I(C){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=C.formattingValues[Z]||C.formattingValues[Y]}else{var B=C.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;X=C.values[q]||C.values[B]}var T=C.argumentCallback?C.argumentCallback(G):G;return X[T]}}var f={narrow:["o.Kr.","m.Kr."],abbreviated:["o.Kr.","m.Kr."],wide:["ovdal Kristusa","ma\u014B\u014Bel Kristusa"]},_={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. kvart\xE1la","2. kvart\xE1la","3. kvart\xE1la","4. kvart\xE1la"]},F={narrow:["O","G","N","C","M","G","S","B","\u010C","G","S","J"],abbreviated:["o\u0111\u0111a","guov","njuk","cuo","mies","geas","suoi","borg","\u010Dak\u010D","golg","sk\xE1b","juov"],wide:["o\u0111\u0111ajagem\xE1nnu","guovvam\xE1nnu","njuk\u010Dam\xE1nnu","cuo\u014Bom\xE1nnu","miessem\xE1nnu","geassem\xE1nnu","suoidnem\xE1nnu","borgem\xE1nnu","\u010Dak\u010Dam\xE1nnu","golggotm\xE1nnu","sk\xE1bmam\xE1nnu","juovlam\xE1nnu"]},P={narrow:["S","V","M","G","D","B","L"],short:["sotn","vuos","ma\u014B","gask","duor","bear","l\xE1v"],abbreviated:["sotn","vuos","ma\u014B","gask","duor","bear","l\xE1v"],wide:["sotnabeaivi","vuoss\xE1rga","ma\u014B\u014Beb\xE1rga","gaskavahkku","duorastat","bearjadat","l\xE1vvardat"]},v={narrow:{am:"a",pm:"p",midnight:"gaskaidja",noon:"gaskabeaivi",morning:"i\u0111\u0111es",afternoon:"ma\u014B\u014Bel gaska.",evening:"eahkes",night:"ihkku"},abbreviated:{am:"a.m.",pm:"p.m.",midnight:"gaskaidja",noon:"gaskabeaivvi",morning:"i\u0111\u0111es",afternoon:"ma\u014B\u014Bel gaskabea.",evening:"eahkes",night:"ihkku"},wide:{am:"a.m.",pm:"p.m.",midnight:"gaskaidja",noon:"gaskabeavvi",morning:"i\u0111\u0111es",afternoon:"ma\u014B\u014Bel gaskabeaivvi",evening:"eahkes",night:"ihkku"}},k=function C(G,H){var J=Number(G);return J+"."},b={ordinalNumber:k,era:I({values:f,defaultWidth:"wide"}),quarter:I({values:_,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:P,defaultWidth:"wide"}),dayPeriod:I({values:v,defaultWidth:"wide"})};function O(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&C.matchPatterns[J]||C.matchPatterns[C.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],B=J&&C.parsePatterns[J]||C.parsePatterns[C.defaultParseWidth],q=Array.isArray(B)?m(B,function(K){return K.test(Z)}):h(B,function(K){return K.test(Z)}),T;T=C.valueCallback?C.valueCallback(q):q,T=H.valueCallback?H.valueCallback(T):T;var CC=G.slice(Z.length);return{value:T,rest:CC}}}function h(C,G){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&G(C[H]))return H;return}function m(C,G){for(var H=0;H<C.length;H++)if(G(C[H]))return H;return}function y(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(C.matchPattern);if(!J)return null;var X=J[0],Y=G.match(C.parsePattern);if(!Y)return null;var Z=C.valueCallback?C.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var B=G.slice(X.length);return{value:Z,rest:B}}}var c=/^(\d+)\.?/i,d=/\d+/i,g={narrow:/^(o\.? ?Kr\.?|m\.? ?Kr\.?)/i,abbreviated:/^(o\.? ?Kr\.?|m\.? ?Kr\.?)/i,wide:/^(ovdal Kristusa|ovdal min áiggi|maŋŋel Kristusa|min áigi)/i},p={any:[/^o/i,/^m/i]},u={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? kvartála/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^[ogncmsbčj]/i,abbreviated:/^(ođđa|guov|njuk|cuo|mies|geas|suoi|borg|čakč|golg|skáb|juov)\.?/i,wide:/^(ođđajagemánnu|guovvamánnu|njukčamánnu|cuoŋománnu|miessemánnu|geassemánnu|suoidnemánnu|borgemánnu|čakčamánnu|golggotmánnu|skábmamánnu|juovlamánnu)/i},n={narrow:[/^o/i,/^g/i,/^n/i,/^c/i,/^m/i,/^g/i,/^s/i,/^b/i,/^č/i,/^g/i,/^s/i,/^j/i],any:[/^o/i,/^gu/i,/^n/i,/^c/i,/^m/i,/^ge/i,/^su/i,/^b/i,/^č/i,/^go/i,/^sk/i,/^j/i]},s={narrow:/^[svmgdbl]/i,short:/^(sotn|vuos|maŋ|gask|duor|bear|láv)/i,abbreviated:/^(sotn|vuos|maŋ|gask|duor|bear|láv)/i,wide:/^(sotnabeaivi|vuossárga|maŋŋebárga|gaskavahkku|duorastat|bearjadat|lávvardat)/i},o={any:[/^s/i,/^v/i,/^m/i,/^g/i,/^d/i,/^b/i,/^l/i]},r={narrow:/^(gaskaidja|gaskabeaivvi|(på) (iđđes|maŋŋel gaskabeaivvi|eahkes|ihkku)|[ap])/i,any:/^([ap]\.?\s?m\.?|gaskaidja|gaskabeaivvi|(på) (iđđes|maŋŋel gaskabeaivvi|eahkes|ihkku))/i},a={any:{am:/^a(\.?\s?m\.?)?$/i,pm:/^p(\.?\s?m\.?)?$/i,midnight:/^gaskai/i,noon:/^gaskab/i,morning:/iđđes/i,afternoon:/maŋŋel gaskabeaivvi/i,evening:/eahkes/i,night:/ihkku/i}},e={ordinalNumber:y({matchPattern:c,parsePattern:d,valueCallback:function C(G){return parseInt(G,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:O({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},t={code:"se",formatDistance:D,formatLong:L,formatRelative:w,localize:b,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{se:t})})})();

//# debugId=1D12994F2601E53364756E2164756E21
