from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Product(db.Model):
    __tablename__ = 'products'
    
    product_id = db.Column(db.String(50), primary_key=True)
    product_name = db.Column(db.String(255), nullable=False)
    sku = db.Column(db.String(100), unique=True, nullable=False)
    category = db.Column(db.String(100))
    brand = db.Column(db.String(100))
    unit = db.Column(db.String(50))
    purchase_price = db.Column(db.Numeric(10, 2))
    sales_price = db.Column(db.Numeric(10, 2))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联关系
    inventories = db.relationship('Inventory', backref='product', lazy=True)
    inbound_items = db.relationship('InboundOrderItem', backref='product', lazy=True)
    outbound_items = db.relationship('OutboundOrderItem', backref='product', lazy=True)

class Warehouse(db.Model):
    __tablename__ = 'warehouses'

    warehouse_id = db.Column(db.String(50), primary_key=True)
    warehouse_name = db.Column(db.String(255), nullable=False)
    location = db.Column(db.String(255))
    capacity = db.Column(db.Numeric(10, 2))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联关系
    inventories = db.relationship('Inventory', backref='warehouse', lazy=True)
    inbound_orders = db.relationship('InboundOrder', backref='warehouse', lazy=True)
    outbound_orders = db.relationship('OutboundOrder', backref='warehouse', lazy=True)

class Inventory(db.Model):
    __tablename__ = 'inventories'
    
    inventory_id = db.Column(db.String(50), primary_key=True)
    product_id = db.Column(db.String(50), db.ForeignKey('products.product_id'), nullable=False)
    warehouse_id = db.Column(db.String(50), db.ForeignKey('warehouses.warehouse_id'), nullable=False)
    quantity = db.Column(db.Integer, default=0)
    available_quantity = db.Column(db.Integer, default=0)
    locked_quantity = db.Column(db.Integer, default=0)
    warning_threshold = db.Column(db.Integer, default=10)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)

class Supplier(db.Model):
    __tablename__ = 'suppliers'
    
    supplier_id = db.Column(db.String(50), primary_key=True)
    supplier_name = db.Column(db.String(255), nullable=False)
    contact_person = db.Column(db.String(100))
    contact_phone = db.Column(db.String(50))
    address = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联关系
    inbound_orders = db.relationship('InboundOrder', backref='supplier', lazy=True)

class Customer(db.Model):
    __tablename__ = 'customers'
    
    customer_id = db.Column(db.String(50), primary_key=True)
    customer_name = db.Column(db.String(255), nullable=False)
    contact_phone = db.Column(db.String(50))
    shipping_address = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联关系
    outbound_orders = db.relationship('OutboundOrder', backref='customer', lazy=True)

class InboundOrder(db.Model):
    __tablename__ = 'inbound_orders'
    
    inbound_order_id = db.Column(db.String(50), primary_key=True)
    order_type = db.Column(db.String(50), nullable=False)
    supplier_id = db.Column(db.String(50), db.ForeignKey('suppliers.supplier_id'))
    warehouse_id = db.Column(db.String(50), db.ForeignKey('warehouses.warehouse_id'), nullable=False)
    order_date = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(50), default='待入库')
    remarks = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联关系
    items = db.relationship('InboundOrderItem', backref='inbound_order', lazy=True, cascade='all, delete-orphan')

class InboundOrderItem(db.Model):
    __tablename__ = 'inbound_order_items'
    
    inbound_order_item_id = db.Column(db.String(50), primary_key=True)
    inbound_order_id = db.Column(db.String(50), db.ForeignKey('inbound_orders.inbound_order_id'), nullable=False)
    product_id = db.Column(db.String(50), db.ForeignKey('products.product_id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Numeric(10, 2))
    batch_number = db.Column(db.String(100))
    production_date = db.Column(db.Date)
    expiry_date = db.Column(db.Date)

class OutboundOrder(db.Model):
    __tablename__ = 'outbound_orders'
    
    outbound_order_id = db.Column(db.String(50), primary_key=True)
    order_type = db.Column(db.String(50), nullable=False)
    customer_id = db.Column(db.String(50), db.ForeignKey('customers.customer_id'))
    warehouse_id = db.Column(db.String(50), db.ForeignKey('warehouses.warehouse_id'), nullable=False)
    order_date = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(50), default='待出库')
    remarks = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联关系
    items = db.relationship('OutboundOrderItem', backref='outbound_order', lazy=True, cascade='all, delete-orphan')

class OutboundOrderItem(db.Model):
    __tablename__ = 'outbound_order_items'
    
    outbound_order_item_id = db.Column(db.String(50), primary_key=True)
    outbound_order_id = db.Column(db.String(50), db.ForeignKey('outbound_orders.outbound_order_id'), nullable=False)
    product_id = db.Column(db.String(50), db.ForeignKey('products.product_id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Numeric(10, 2))
    batch_number = db.Column(db.String(100))
    serial_number = db.Column(db.String(100))

