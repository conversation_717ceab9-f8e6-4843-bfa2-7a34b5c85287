// https://d3js.org/d3-path/ v3.1.0 Copyright 2015-2022 <PERSON>
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?i(exports):"function"==typeof define&&define.amd?define(["exports"],i):i((t="undefined"!=typeof globalThis?globalThis:t||self).d3=t.d3||{})}(this,(function(t){"use strict";const i=Math.PI,s=2*i,h=1e-6,e=s-h;function n(t){this._+=t[0];for(let i=1,s=t.length;i<s;++i)this._+=arguments[i]+t[i]}class _{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?n:function(t){let i=Math.floor(t);if(!(i>=0))throw new Error(`invalid digits: ${t}`);if(i>15)return n;const s=10**i;return function(t){this._+=t[0];for(let i=1,h=t.length;i<h;++i)this._+=Math.round(arguments[i]*s)/s+t[i]}}(t)}moveTo(t,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+i}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,i){this._append`L${this._x1=+t},${this._y1=+i}`}quadraticCurveTo(t,i,s,h){this._append`Q${+t},${+i},${this._x1=+s},${this._y1=+h}`}bezierCurveTo(t,i,s,h,e,n){this._append`C${+t},${+i},${+s},${+h},${this._x1=+e},${this._y1=+n}`}arcTo(t,s,e,n,_){if(t=+t,s=+s,e=+e,n=+n,(_=+_)<0)throw new Error(`negative radius: ${_}`);let a=this._x1,$=this._y1,o=e-t,r=n-s,p=a-t,d=$-s,l=p*p+d*d;if(null===this._x1)this._append`M${this._x1=t},${this._y1=s}`;else if(l>h)if(Math.abs(d*o-r*p)>h&&_){let u=e-a,f=n-$,x=o*o+r*r,y=u*u+f*f,c=Math.sqrt(x),M=Math.sqrt(l),b=_*Math.tan((i-Math.acos((x+l-y)/(2*c*M)))/2),g=b/M,w=b/c;Math.abs(g-1)>h&&this._append`L${t+g*p},${s+g*d}`,this._append`A${_},${_},0,0,${+(d*u>p*f)},${this._x1=t+w*o},${this._y1=s+w*r}`}else this._append`L${this._x1=t},${this._y1=s}`;else;}arc(t,n,_,a,$,o){if(t=+t,n=+n,o=!!o,(_=+_)<0)throw new Error(`negative radius: ${_}`);let r=_*Math.cos(a),p=_*Math.sin(a),d=t+r,l=n+p,u=1^o,f=o?a-$:$-a;null===this._x1?this._append`M${d},${l}`:(Math.abs(this._x1-d)>h||Math.abs(this._y1-l)>h)&&this._append`L${d},${l}`,_&&(f<0&&(f=f%s+s),f>e?this._append`A${_},${_},0,1,${u},${t-r},${n-p}A${_},${_},0,1,${u},${this._x1=d},${this._y1=l}`:f>h&&this._append`A${_},${_},0,${+(f>=i)},${u},${this._x1=t+_*Math.cos($)},${this._y1=n+_*Math.sin($)}`)}rect(t,i,s,h){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+i}h${s=+s}v${+h}h${-s}Z`}toString(){return this._}}function a(){return new _}a.prototype=_.prototype,t.Path=_,t.path=a,t.pathRound=function(t=3){return new _(+t)}}));
