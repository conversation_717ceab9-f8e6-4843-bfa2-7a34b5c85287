// Same as fr
import { formatDistance } from "./fr/_lib/formatDistance.js";
import { formatRelative } from "./fr/_lib/formatRelative.js";
import { localize } from "./fr/_lib/localize.js";
import { match } from "./fr/_lib/match.js";

// Unique for fr-CA
import { formatLong } from "./fr-CA/_lib/formatLong.js";

/**
 * @category Locales
 * @summary French locale (Canada).
 * @language French
 * @iso-639-2 fra
 * <AUTHOR> [@izeau](https://github.com/izeau)
 * <AUTHOR> [@fbonzon](https://github.com/fbonzon)
 * <AUTHOR> [@gpetrioli](https://github.com/gpetrioli)
 */
export const frCA = {
  code: "fr-CA",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,

  // Unique for fr-CA
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default frCA;
