"use strict";
exports.cs = void 0;
var _index = require("./cs/_lib/formatDistance.cjs");
var _index2 = require("./cs/_lib/formatLong.cjs");
var _index3 = require("./cs/_lib/formatRelative.cjs");
var _index4 = require("./cs/_lib/localize.cjs");
var _index5 = require("./cs/_lib/match.cjs");

/**
 * @category Locales
 * @summary Czech locale.
 * @language Czech
 * @iso-639-2 ces
 * <AUTHOR> [@davidrus](https://github.com/davidrus)
 * <AUTHOR> [@SilenY](https://github.com/SilenY)
 * <AUTHOR> [@JozefBiros](https://github.com/JozefBiros)
 */
const cs = (exports.cs = {
  code: "cs",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
