import { formatDistance } from "./zh-TW/_lib/formatDistance.js";
import { formatLong } from "./zh-TW/_lib/formatLong.js";
import { formatRelative } from "./zh-TW/_lib/formatRelative.js";
import { localize } from "./zh-TW/_lib/localize.js";
import { match } from "./zh-TW/_lib/match.js";

/**
 * @category Locales
 * @summary Chinese Traditional locale.
 * @language Chinese Traditional
 * @iso-639-2 zho
 * <AUTHOR> [@tpai](https://github.com/tpai)
 * <AUTHOR> [@jackhsu978](https://github.com/jackhsu978)
 * <AUTHOR> [@skyuplam](https://github.com/skyuplam)
 */
export const zhTW = {
  code: "zh-TW",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default zhTW;
