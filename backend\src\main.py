import os
import sys
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory
from flask_cors import CORS
from src.models.inventory import db
from src.models.user import User  # 导入用户模型以确保表被创建
from src.routes.user import user_bp
from src.routes.user_test import user_test_bp
from src.routes.inventory import inventory_bp

app = Flask(__name__, static_folder=None, static_url_path=None)  # 完全禁用静态文件
app.config['SECRET_KEY'] = 'asdf#FGSgvasgf$5$WGT'

# 启用CORS
CORS(app)

app.register_blueprint(user_bp, url_prefix='/api')
app.register_blueprint(user_test_bp, url_prefix='/api')
app.register_blueprint(inventory_bp, url_prefix='/api')

# uncomment if you need to use database
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'database', 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)
with app.app_context():
    db.create_all()

# 注释掉静态文件路由，避免与API路由冲突
# @app.route('/', defaults={'path': ''})
# @app.route('/<path:path>')
# def serve(path):
#     static_folder_path = app.static_folder
#     if static_folder_path is None:
#             return "Static folder not configured", 404

#     if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
#         return send_from_directory(static_folder_path, path)
#     else:
#         index_path = os.path.join(static_folder_path, 'index.html')
#         if os.path.exists(index_path):
#             return send_from_directory(static_folder_path, 'index.html')
#         else:
#             return "index.html not found", 404

@app.route('/')
def index():
    return jsonify({'message': '电商库存管理系统后端API', 'version': '1.0', 'status': 'running'})


if __name__ == '__main__':
    print("正在启动后端服务...")
    try:
        print("数据库初始化完成")
        print("后端服务启动成功！")
        print("访问地址: http://localhost:5000")
        print("API地址: http://localhost:5000/api")
        app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
