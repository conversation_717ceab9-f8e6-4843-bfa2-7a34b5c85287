<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 系统测试页面</h1>
    
    <div class="card">
        <h2>前端测试</h2>
        <p>如果您能看到这个页面，说明前端服务器正常运行。</p>
        <p>✅ 前端服务器状态：正常</p>
    </div>

    <div class="card">
        <h2>后端API测试</h2>
        <button onclick="testBackend()">测试后端连接</button>
        <button onclick="testProducts()">测试商品API</button>
        <button onclick="testWarehouses()">测试仓库API</button>
        <div id="apiResult" class="result"></div>
    </div>

    <div class="card">
        <h2>主应用测试</h2>
        <button onclick="loadMainApp()">加载主应用</button>
        <p>如果主应用无法加载，请检查JavaScript控制台错误。</p>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000';
        
        function showResult(message) {
            document.getElementById('apiResult').textContent = message;
        }
        
        async function testBackend() {
            showResult('正在测试后端连接...');
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.json();
                showResult(`✅ 后端连接成功:\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                showResult(`❌ 后端连接失败:\n${error.message}`);
            }
        }
        
        async function testProducts() {
            showResult('正在测试商品API...');
            try {
                const response = await fetch(`${API_BASE_URL}/api/products`);
                const data = await response.json();
                showResult(`✅ 商品API成功:\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                showResult(`❌ 商品API失败:\n${error.message}`);
            }
        }
        
        async function testWarehouses() {
            showResult('正在测试仓库API...');
            try {
                const response = await fetch(`${API_BASE_URL}/api/warehouses`);
                const data = await response.json();
                showResult(`✅ 仓库API成功:\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                showResult(`❌ 仓库API失败:\n${error.message}`);
            }
        }
        
        function loadMainApp() {
            window.location.href = '/';
        }
        
        // 页面加载时自动测试后端
        window.onload = function() {
            testBackend();
        };
    </script>
</body>
</html>
