#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的后端服务 - 用于快速启动和测试
"""

from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# 模拟数据
SAMPLE_PRODUCTS = [
    {
        'product_id': 'P001',
        'product_name': '华为Mate60 Pro',
        'sku': 'HW-M60P-001',
        'category': '智能手机',
        'brand': '华为',
        'unit': '台',
        'purchase_price': '5999.00',
        'sales_price': '6999.00'
    },
    {
        'product_id': 'P002',
        'product_name': 'iPhone 15 Pro',
        'sku': 'IP-15P-001',
        'category': '智能手机',
        'brand': '苹果',
        'unit': '台',
        'purchase_price': '7999.00',
        'sales_price': '8999.00'
    }
]

SAMPLE_WAREHOUSES = [
    {
        'warehouse_id': 'W001',
        'warehouse_name': '北京仓库',
        'location': '北京市朝阳区',
        'capacity': 10000.0
    },
    {
        'warehouse_id': 'W002',
        'warehouse_name': '上海仓库',
        'location': '上海市浦东新区',
        'capacity': 15000.0
    }
]

SAMPLE_INVENTORY = [
    {
        'inventory_id': 'INV001',
        'product_name': '华为Mate60 Pro',
        'sku': 'HW-M60P-001',
        'warehouse_name': '北京仓库',
        'quantity': 50,
        'available_quantity': 45,
        'locked_quantity': 5,
        'warning_threshold': 10
    }
]

SAMPLE_WARNINGS = [
    {
        'product_name': 'iPhone 15 Pro',
        'sku': 'IP-15P-001',
        'warehouse_name': '上海仓库',
        'current_quantity': 5,
        'warning_threshold': 10,
        'message': '库存低于预警值，请及时补货！'
    }
]

@app.route('/')
def index():
    return jsonify({
        'message': '电商库存管理系统后端API',
        'version': '1.0',
        'status': 'running'
    })

@app.route('/api/products', methods=['GET'])
def get_products():
    return jsonify({
        'code': 200,
        'message': '获取商品列表成功',
        'success': True,
        'data': SAMPLE_PRODUCTS
    })

@app.route('/api/warehouses', methods=['GET'])
def get_warehouses():
    return jsonify({
        'code': 200,
        'message': '获取仓库列表成功',
        'success': True,
        'data': SAMPLE_WAREHOUSES
    })

@app.route('/api/inventory', methods=['GET'])
def get_inventory():
    return jsonify({
        'code': 200,
        'message': '获取库存成功',
        'success': True,
        'data': SAMPLE_INVENTORY
    })

@app.route('/api/inventory/warnings', methods=['GET'])
def get_warnings():
    return jsonify({
        'code': 200,
        'message': '获取预警成功',
        'success': True,
        'data': SAMPLE_WARNINGS
    })

@app.route('/api/products', methods=['POST'])
def create_product():
    return jsonify({
        'code': 200,
        'message': '商品创建成功',
        'success': True,
        'data': {'product_id': 'P003'}
    })

@app.route('/api/products/<product_id>', methods=['DELETE'])
def delete_product(product_id):
    return jsonify({
        'code': 200,
        'message': '商品删除成功',
        'success': True
    })

@app.route('/api/warehouses', methods=['POST'])
def create_warehouse():
    return jsonify({
        'code': 200,
        'message': '仓库创建成功',
        'success': True,
        'data': {'warehouse_id': 'W003'}
    })

if __name__ == '__main__':
    print("🚀 启动简化后端服务...")
    print("📡 后端API地址: http://localhost:5000/api")
    print("🏠 主页: http://localhost:5000/")
    print("✅ 所有API端点已启用")
    print("⚠️  这是简化版本，使用模拟数据")
    print("-" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=False)
