#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的后端服务 - 用于快速启动和测试
"""

from flask import Flask, jsonify, render_template_string
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# 模拟数据
SAMPLE_PRODUCTS = [
    {
        'product_id': 'P001',
        'product_name': '华为Mate60 Pro',
        'sku': 'HW-M60P-001',
        'category': '智能手机',
        'brand': '华为',
        'unit': '台',
        'purchase_price': '5999.00',
        'sales_price': '6999.00'
    },
    {
        'product_id': 'P002',
        'product_name': 'iPhone 15 Pro',
        'sku': 'IP-15P-001',
        'category': '智能手机',
        'brand': '苹果',
        'unit': '台',
        'purchase_price': '7999.00',
        'sales_price': '8999.00'
    }
]

SAMPLE_WAREHOUSES = [
    {
        'warehouse_id': 'W001',
        'warehouse_name': '北京仓库',
        'location': '北京市朝阳区',
        'capacity': 10000.0
    },
    {
        'warehouse_id': 'W002',
        'warehouse_name': '上海仓库',
        'location': '上海市浦东新区',
        'capacity': 15000.0
    }
]

SAMPLE_INVENTORY = [
    {
        'inventory_id': 'INV001',
        'product_name': '华为Mate60 Pro',
        'sku': 'HW-M60P-001',
        'warehouse_name': '北京仓库',
        'quantity': 50,
        'available_quantity': 45,
        'locked_quantity': 5,
        'warning_threshold': 10
    }
]

SAMPLE_WARNINGS = [
    {
        'product_name': 'iPhone 15 Pro',
        'sku': 'IP-15P-001',
        'warehouse_name': '上海仓库',
        'current_quantity': 5,
        'warning_threshold': 10,
        'message': '库存低于预警值，请及时补货！'
    }
]

@app.route('/')
def index():
    html_template = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 电商库存管理系统 - 后端API</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔧</text></svg>">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        h1 { font-size: 2.5rem; margin-bottom: 1rem; font-weight: 700; }
        .subtitle { font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; }
        .status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(16, 185, 129, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            border: 1px solid rgba(16, 185, 129, 0.3);
            margin-bottom: 2rem;
        }
        .status-dot {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: blink 1.5s infinite;
        }
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        .footer {
            margin-top: 2rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }
        .version {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        a { color: #fbbf24; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔧</div>
        <h1>电商库存管理系统</h1>
        <div class="subtitle">后端API服务</div>

        <div class="status">
            <div class="status-dot"></div>
            <span>服务运行中</span>
        </div>

        <div class="info">
            <h3>📡 API服务信息</h3>
            <p>✅ 商品管理API</p>
            <p>✅ 仓库管理API</p>
            <p>✅ 库存查询API</p>
            <p>✅ 预警系统API</p>
        </div>

        <div class="footer">
            <p>版本: <span class="version">v1.0</span></p>
            <p>🏠 前端应用: <a href="http://localhost:8080">http://localhost:8080</a></p>
            <p>📖 这是简化版后端服务</p>
        </div>
    </div>
</body>
</html>
    '''
    return render_template_string(html_template)

@app.route('/api/status')
def api_status():
    return jsonify({
        'message': '电商库存管理系统后端API',
        'version': '1.0',
        'status': 'running'
    })

@app.route('/api/products', methods=['GET'])
def get_products():
    return jsonify({
        'code': 200,
        'message': '获取商品列表成功',
        'success': True,
        'data': SAMPLE_PRODUCTS
    })

@app.route('/api/warehouses', methods=['GET'])
def get_warehouses():
    return jsonify({
        'code': 200,
        'message': '获取仓库列表成功',
        'success': True,
        'data': SAMPLE_WAREHOUSES
    })

@app.route('/api/inventory', methods=['GET'])
def get_inventory():
    return jsonify({
        'code': 200,
        'message': '获取库存成功',
        'success': True,
        'data': SAMPLE_INVENTORY
    })

@app.route('/api/inventory/warnings', methods=['GET'])
def get_warnings():
    return jsonify({
        'code': 200,
        'message': '获取预警成功',
        'success': True,
        'data': SAMPLE_WARNINGS
    })

@app.route('/api/products', methods=['POST'])
def create_product():
    return jsonify({
        'code': 200,
        'message': '商品创建成功',
        'success': True,
        'data': {'product_id': 'P003'}
    })

@app.route('/api/products/<product_id>', methods=['DELETE'])
def delete_product(product_id):
    return jsonify({
        'code': 200,
        'message': '商品删除成功',
        'success': True
    })

@app.route('/api/warehouses', methods=['POST'])
def create_warehouse():
    return jsonify({
        'code': 200,
        'message': '仓库创建成功',
        'success': True,
        'data': {'warehouse_id': 'W003'}
    })

if __name__ == '__main__':
    print("🚀 启动简化后端服务...")
    print("📡 后端API地址: http://localhost:5000/api")
    print("🏠 主页: http://localhost:5000/")
    print("✅ 所有API端点已启用")
    print("⚠️  这是简化版本，使用模拟数据")
    print("-" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=False)
