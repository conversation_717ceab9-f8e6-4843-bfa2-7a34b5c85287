# 电商库存管理系统使用说明

## 系统概述

电商库存管理系统是一个基于Web的现代化库存管理解决方案，专为电商企业设计。系统采用前后端分离架构，提供直观的用户界面和强大的功能模块，帮助企业实现库存的精细化管理。

### 技术架构

- **前端**：React + Tailwind CSS + shadcn/ui
- **后端**：Flask + SQLAlchemy
- **数据库**：SQLite（可扩展至MySQL/PostgreSQL）
- **部署**：支持本地部署和云端部署

### 核心功能

1. **仪表板**：提供库存概览和关键指标
2. **商品管理**：商品信息的增删改查
3. **仓库管理**：多仓库信息管理
4. **库存查询**：实时库存状态查询
5. **入库管理**：采购入库、退货入库等
6. **出库管理**：销售出库、调拨出库等
7. **库存预警**：低库存自动提醒

## 系统安装

### 环境要求

- Python 3.8+
- Node.js 16+
- npm 或 pnpm

### 后端安装

1. 进入后端目录：
```bash
cd backend
```

2. 激活虚拟环境：
```bash
source venv/bin/activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 启动后端服务：
```bash
python src/main.py
```

后端服务将在 http://localhost:5000 启动。

### 前端安装

1. 进入前端目录：
```bash
cd frontend
```

2. 安装依赖：
```bash
pnpm install
```

3. 启动开发服务器：
```bash
pnpm run dev
```

前端应用将在 http://localhost:5173 启动。

## 功能使用指南

### 1. 仪表板

仪表板是系统的首页，提供以下关键信息：

- **商品总数**：系统中录入的商品数量
- **仓库数量**：已配置的仓库数量
- **低库存商品**：库存低于预警阈值的商品数量
- **库存总价值**：当前库存的总价值

### 2. 商品管理

#### 添加商品

1. 点击"添加商品"按钮
2. 填写商品信息：
   - 商品名称（必填）
   - SKU（必填，唯一标识）
   - 分类
   - 品牌
   - 单位
   - 采购价格
   - 销售价格
3. 点击"保存"按钮

#### 查看商品列表

商品列表显示所有已录入的商品信息，包括：
- 商品名称
- SKU
- 分类
- 品牌
- 单位
- 采购价格
- 销售价格

### 3. 仓库管理

仓库管理功能允许您管理多个仓库的基本信息。

#### 添加仓库

1. 点击"添加仓库"按钮
2. 填写仓库信息：
   - 仓库名称（必填）
   - 位置
   - 容量（立方米）
3. 点击"保存"按钮

#### 查看仓库列表

仓库列表显示所有已配置的仓库信息，包括：
- 仓库名称
- 位置
- 容量

### 4. 库存查询

库存查询页面提供实时库存信息：

- **商品名称**：商品的名称
- **SKU**：商品的唯一标识
- **仓库**：商品所在仓库
- **总库存**：商品的总数量
- **可用库存**：可用于销售的数量
- **锁定库存**：已被订单锁定的数量
- **预警阈值**：库存预警的临界值
- **状态**：库存状态（正常/库存不足）

### 5. 库存预警

库存预警功能自动监控库存水平：

- 当商品库存低于预警阈值时，系统会自动标记为"库存不足"
- 预警列表显示所有需要补货的商品
- 提供详细的预警信息和建议

### 6. 入库管理

入库管理支持多种入库类型：

- **采购入库**：从供应商采购的商品入库
- **退货入库**：客户退回的商品重新入库
- **调拨入库**：从其他仓库调拨的商品

#### 创建入库单

1. 点击"创建入库单"按钮
2. 选择入库类型（采购入库/退货入库/调拨入库）
3. 选择供应商
4. 选择目标仓库
5. 添加入库商品明细：
   - 选择商品
   - 输入数量
   - 输入单价
   - 点击"添加"按钮
6. 确认商品明细和总金额
7. 点击"创建入库单"按钮

#### 查看入库单列表

入库单列表显示所有入库记录，包括：
- 入库单号
- 入库类型
- 供应商
- 目标仓库
- 总金额
- 状态（待确认/已确认）
- 创建时间

### 7. 出库管理

出库管理支持多种出库类型：

- **销售出库**：客户订单出库
- **调拨出库**：向其他仓库调拨
- **报损出库**：损坏商品出库

#### 创建出库单

1. 点击"创建出库单"按钮
2. 选择出库类型（销售出库/调拨出库/报损出库）
3. 选择客户
4. 选择出库仓库
5. 添加出库商品明细：
   - 选择商品
   - 输入数量
   - 输入单价
   - 点击"添加"按钮
6. 系统自动检查库存
7. 确认商品明细和总金额
8. 点击"创建出库单"按钮

#### 查看出库单列表

出库单列表显示所有出库记录，包括：
- 出库单号
- 出库类型
- 客户
- 出库仓库
- 总金额
- 状态（待确认/已确认）
- 创建时间

### 8. 库存预警（原第5节内容保持不变）

入库管理支持多种入库类型：

- **采购入库**：从供应商采购的商品入库
- **退货入库**：客户退回的商品重新入库
- **调拨入库**：从其他仓库调拨的商品

#### 创建入库单

1. 选择入库类型
2. 选择供应商（采购入库）
3. 选择目标仓库
4. 添加入库商品明细
5. 确认入库

### 6. 出库管理

出库管理支持多种出库类型：

- **销售出库**：客户订单出库
- **调拨出库**：向其他仓库调拨
- **报损出库**：损坏商品出库

#### 创建出库单

1. 选择出库类型
2. 选择客户（销售出库）
3. 选择出库仓库
4. 添加出库商品明细
5. 系统自动检查库存
6. 确认出库

## API接口文档

### 商品管理API

#### 获取商品列表
```
GET /api/products
```

#### 创建商品
```
POST /api/products
Content-Type: application/json

{
  "product_name": "商品名称",
  "sku": "商品SKU",
  "category": "分类",
  "brand": "品牌",
  "unit": "单位",
  "purchase_price": 100.00,
  "sales_price": 150.00
}
```

### 库存查询API

#### 查询库存
```
GET /api/inventory?product_id=xxx&warehouse_id=xxx
```

### 库存预警API

#### 获取预警列表
```
GET /api/inventory/warnings
```

## 数据库设计

### 核心数据表

1. **products**：商品信息表
2. **warehouses**：仓库信息表
3. **inventories**：库存信息表
4. **suppliers**：供应商信息表
5. **customers**：客户信息表
6. **inbound_orders**：入库单表
7. **outbound_orders**：出库单表

### 数据关系

- 商品与库存：一对多关系
- 仓库与库存：一对多关系
- 入库单与入库明细：一对多关系
- 出库单与出库明细：一对多关系

## 系统配置

### 数据库配置

系统默认使用SQLite数据库，配置文件位于 `backend/src/main.py`：

```python
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'database', 'app.db')}"
```

### CORS配置

系统已配置CORS支持，允许前端跨域访问：

```python
from flask_cors import CORS
CORS(app)
```

## 故障排除

### 常见问题

1. **后端启动失败**
   - 检查Python版本是否符合要求
   - 确认虚拟环境已激活
   - 检查依赖是否正确安装

2. **前端无法连接后端**
   - 确认后端服务已启动
   - 检查API地址配置
   - 查看浏览器控制台错误信息

3. **数据库连接失败**
   - 检查数据库文件权限
   - 确认数据库路径正确
   - 查看后端日志信息

### 日志查看

- 后端日志：在终端中查看Flask应用输出
- 前端日志：在浏览器开发者工具的Console中查看

## 系统扩展

### 功能扩展

系统采用模块化设计，支持以下扩展：

1. **多语言支持**：添加国际化配置
2. **权限管理**：添加用户角色和权限控制
3. **报表功能**：添加更多统计报表
4. **移动端支持**：开发移动端应用
5. **第三方集成**：对接ERP、CRM等系统

### 数据库扩展

支持迁移到其他数据库：

- MySQL
- PostgreSQL
- SQL Server

只需修改数据库连接字符串即可。



